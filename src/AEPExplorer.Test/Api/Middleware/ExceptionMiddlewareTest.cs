namespace AEPExplorer.Test.Api.Middleware
{
    public class ExceptionMiddlewareTest
    {
        //[Fact]
        //public async Task ExceptionMiddlewareShouldReturnInternalServerError()
        //{
        //    var expectedException = new ArgumentNullException();

        //    RequestDelegate mockNexMiddleware = (HttpContext) =>
        //    {
        //        return Task.FromException(expectedException);
        //    };

        //    var httpContext = new DefaultHttpContext();

        //    var exceptionHandlingMiddleware = new ExceptionMiddleware(mockNexMiddleware);

        //    await exceptionHandlingMiddleware.InvokeAsync(httpContext);

        //    Assert.Equal(HttpStatusCode.InternalServerError, (HttpStatusCode)httpContext.Response.StatusCode);
        //}
    }
}
