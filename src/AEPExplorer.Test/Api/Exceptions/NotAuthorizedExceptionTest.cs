namespace AEPExplorer.API.Exceptions
{
    public class NotAuthorizedExceptionTest
    {
        [Fact]
        public void NotAuthorizedShouldReturnResponseCode400()
        {
            var exception = new NotAuthorizedException();

            var result = exception.HttpResponseCode;

            Assert.Equal(401, result);
        }

        [Fact]
        public void NotAuthorizedShouldReturnDefaultMessage()
        {
            var exception = new NotAuthorizedException();

            var result = exception.Message;

            Assert.NotNull(result);
            Assert.Equal("Not authorized!", result);
        }

        [Fact]
        public void NotAuthorizedShouldReturnCorrectMessage()
        {
            var exception = new NotAuthorizedException("Test");

            var result = exception.Message;

            Assert.NotNull(result);
            Assert.Equal("Test", result);
        }

        [Fact]
        public void NotAuthorizedShouldReturnInnerException()
        {
            var exception = new NotAuthorizedException("Test", new Exception("Test inner exception"));

            var result = exception.InnerException;

            Assert.NotNull(result);
            Assert.Equal("Test inner exception", result.Message);
        }
    }
}