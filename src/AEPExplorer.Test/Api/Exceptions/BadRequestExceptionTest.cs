namespace AEPExplorer.API.Exceptions
{
    public class BadRequestExceptionTest
    {
        [Fact]
        public void BadRequestShouldReturnResponseCode400()
        {
            var exception = new BadRequestException();

            var result = exception.HttpResponseCode;

            Assert.Equal(400, result);
        }

        [Fact]
        public void BadRequestShouldReturnDefaultMessage()
        {
            var exception = new BadRequestException();

            var result = exception.Message;

            Assert.NotNull(result);
            Assert.Equal("The request was not understood by the server.", result);
        }

        [Fact]
        public void BadRequestShouldReturnCorrectMessage()
        {
            var exception = new BadRequestException("Test");

            var result = exception.Message;

            Assert.NotNull(result);
            Assert.Equal("Test", result);
        }

        [Fact]
        public void BadRequestShouldReturnInnerException()
        {
            var exception = new BadRequestException("Test", new Exception("Test inner exception"));

            var result = exception.InnerException;

            Assert.NotNull(result);
            Assert.Equal("Test inner exception", result.Message);
        }
    }
}