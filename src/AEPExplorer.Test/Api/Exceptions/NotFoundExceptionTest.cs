namespace AEPExplorer.API.Exceptions
{
    public class NotFoundExceptionTest
    {
        [Fact]
        public void NotFoundShouldReturnResponseCode400()
        {
            var exception = new NotFoundException();

            var result = exception.HttpResponseCode;

            Assert.Equal(404, result);
        }

        [Fact]
        public void NotFoundShouldReturnDefaultMessage()
        {
            var exception = new NotFoundException();

            var result = exception.Message;

            Assert.NotNull(result);
            Assert.Equal("No results found", result);
        }

        [Fact]
        public void NotFoundShouldReturnCorrectMessage()
        {
            var exception = new NotFoundException("Test");

            var result = exception.Message;

            Assert.NotNull(result);
            Assert.Equal("Test", result);
        }

        [Fact]
        public void NotFoundShouldReturnInnerException()
        {
            var exception = new NotFoundException("Test", new Exception("Test inner exception"));

            var result = exception.InnerException;

            Assert.NotNull(result);
            Assert.Equal("Test inner exception", result.Message);
        }
    }
}