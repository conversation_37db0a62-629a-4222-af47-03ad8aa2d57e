using AEPExplorer.API.Controllers;
using AEPExplorer.Model.Request;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.Test.Api.Controllers
{
    public class ImportControllerTest
    {
        //[Fact]
        //public void ImportDailyDataShouldReturnActionResult()
        //{
        //    var mediatorMoq = new Mock<IMediator>();
        //    var query = new Query();

        //    var controller = new ImportController(mediatorMoq.Object);

        //    var result = controller.ImportDailyData(query);
        //    Assert.NotNull(result);
        //    Assert.IsType<Task<ActionResult>>(result);
        //}

        //[Fact]
        //public void Import10MinDataShouldReturnActionResult()
        //{
        //    var mediatorMoq = new Mock<IMediator>();
        //    var controller = new ImportController(mediatorMoq.Object);

        //    var result = controller.Import10MinData();
        //    Assert.NotNull(result);
        //    Assert.IsType<Task<ActionResult>>(result);
        //}

        [Fact]
        public void ImportEpcShouldReturnActionResult()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new ImportQuery();
            var controller = new ImportController(mediatorMoq.Object);

            var result = controller.ImportEpc(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult>>(result);
        }

        [Fact]
        public void DeleteEpcShouldReturnActionResult()
        {
            var mediatorMoq = new Mock<IMediator>();
            var controller = new ImportController(mediatorMoq.Object);

            var result = controller.DeleteEpc();
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult>>(result);
        }
    }
}
