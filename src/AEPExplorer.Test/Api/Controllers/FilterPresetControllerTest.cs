using AEPExplorer.API.Controllers;
using AEPExplorer.Model.Domain;
using AEPExplorer.Model.Request;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AEPExplorer.Test.Api.Controllers
{
    public class FilterPresetControllerTest
    {
        [Fact]
        public void GetFilterPresetShouldReturnActionResultFilterPresetList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var controller = new FilterPresetController(mediatorMoq.Object);

            var result = controller.GetFilterPresets();

            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<FilterPreset>>>>(result);
        }

        [Fact]
        public void AddFilterPresetShouldReturnActionResultGuid()
        {
            var mediatorMoq = new Mock<IMediator>();
            var controller = new FilterPresetController(mediatorMoq.Object);
            var filterPresetModel = new AddFilterPresetModel();

            var result = controller.AddFilterPreset(filterPresetModel);

            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<Guid>>>(result);
        }

        [Fact]
        public void UpdateFavoriteFilterPresetShouldReturnActionResultGuid()
        {
            var mediatorMoq = new Mock<IMediator>();
            var controller = new FilterPresetController(mediatorMoq.Object);
            var filterPresetModel = new FavoriteFilterPresetModel();

            var result = controller.UpdateFavoriteFilterPreset(filterPresetModel);

            Assert.IsType<Task<ActionResult<Guid?>>>(result);
        }

        [Fact]
        public void UpdateFilterPresetShouldReturnActionResultGuid()
        {
            var mediatorMoq = new Mock<IMediator>();
            var controller = new FilterPresetController(mediatorMoq.Object);
            var filterPresetModel = new UpdateFilterPresetModel();

            var result = controller.UpdateFilterPreset(filterPresetModel);

            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<Guid>>>(result);
        }

        [Fact]
        public async Task UpdateFilterPresetShouldReturnNotFound()
        {
            var mediatorMoq = new Mock<IMediator>();
            var controller = new FilterPresetController(mediatorMoq.Object);
            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]{
                new Claim(ClaimTypes.Name, "example name"),
                new Claim(ClaimTypes.NameIdentifier, "1"),
                new Claim("http://schemas.microsoft.com/identity/claims/objectidentifier", Guid.NewGuid().ToString())
            }, "mock"));

            controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = user
                }
            };
            var filterPresetModel = new UpdateFilterPresetModel();

            var result = await controller.UpdateFilterPreset(filterPresetModel);

            Assert.IsType<NotFoundResult>(result.Result);
        }

        [Fact]
        public void DeleteFilterPresetShouldReturnActionResultBool()
        {
            var mediatorMoq = new Mock<IMediator>();
            var controller = new FilterPresetController(mediatorMoq.Object);
            var id = Guid.NewGuid();

            var result = controller.DeleteFilterPreset(id);

            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<bool>>>(result);
        }

        [Fact]
        public async Task DeleteFilterPresetShouldReturnNotFound()
        {
            var mediatorMoq = new Mock<IMediator>();
            var controller = new FilterPresetController(mediatorMoq.Object);
            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]{
                new Claim(ClaimTypes.Name, "example name"),
                new Claim(ClaimTypes.NameIdentifier, "1"),
                new Claim("http://schemas.microsoft.com/identity/claims/objectidentifier", Guid.NewGuid().ToString())
            }, "mock"));
            controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = user
                }
            };

            var id = Guid.NewGuid();

            var result = await controller.DeleteFilterPreset(id);

            Assert.IsType<NotFoundResult>(result.Result);
        }
    }
}
