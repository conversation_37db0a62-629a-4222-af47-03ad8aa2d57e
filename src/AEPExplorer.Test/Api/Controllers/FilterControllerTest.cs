using AEPExplorer.API.Controllers;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.Test.Api.Controllers
{
    public class FilterControllerTest
    {
        [Fact]
        public void GetRegionShouldReturnActionResultValueLabelList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new FilterQuery();
            
            var controller = new FilterController(mediatorMoq.Object);
            
            var result = controller.GetRegion(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<ValueLabel>>>>(result);
        }

        [Fact]
        public void GetCountryShouldReturnActionResultValueLabelList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new FilterQuery();

            var controller = new FilterController(mediatorMoq.Object);

            var result = controller.GetCountry(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<ValueLabel>>>>(result);
        }

        [Fact]
        public void GetSiteShouldReturnActionResultValueLabelList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new FilterQuery();

            var controller = new FilterController(mediatorMoq.Object);

            var result = controller.GetSite(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<ValueLabel>>>>(result);
        }

        [Fact]
        public void GetTurbineShouldReturnActionResultValueLabelList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new FilterQuery();

            var controller = new FilterController(mediatorMoq.Object);

            var result = controller.GetTurbine(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<ValueLabel>>>>(result);
        }

        [Fact]
        public void GetPlatformShouldReturnActionResultValueLabelList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new FilterQuery();

            var controller = new FilterController(mediatorMoq.Object);

            var result = controller.GetPlatform(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<ValueLabelWithChildren>>>>(result);
        }

        [Fact]
        public void GetCategoryShouldReturnActionResultValueLabelList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new FilterQuery();

            var controller = new FilterController(mediatorMoq.Object);

            var result = controller.GetCategory(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<ValueLabelWithChildren>>>>(result);
        }

        [Fact]
        public void GetDefaultRangeShouldReturnActionResultFromTo()
        {
            var mediatorMoq = new Mock<IMediator>();

            var controller = new FilterController(mediatorMoq.Object);

            var result = controller.GetDefaultDateRange();
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<DateRangeModel>>>(result);
        }
    }
}
