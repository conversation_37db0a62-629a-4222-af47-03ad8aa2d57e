using AEPExplorer.API.Controllers;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

namespace AEPExplorer.Test.Api.Controllers
{
    public class MapControllerTest
    {
        [Fact]
        public void GetRegionShouldReturnActionResultListMapModel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var configurationMock = new Mock<IConfiguration>();
            var query = new Query();

            var controller = new MapController(mediatorMoq.Object, configurationMock.Object);

            var result = controller.GetRegion(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<MapModelTotalLosses>>>>(result);
        }

        [Fact]
        public void GetCountryShouldReturnActionResultListMapModel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var configurationMock = new Mock<IConfiguration>();
            var query = new Query();

            var controller = new MapController(mediatorMoq.Object, configurationMock.Object);

            var result = controller.GetCountry(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<MapModelTotalLosses>>>>(result);
        }

        [Fact]
        public void GetSiteShouldReturnActionResultListMapModel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var configurationMock = new Mock<IConfiguration>();
            var query = new Query();

            var controller = new MapController(mediatorMoq.Object, configurationMock.Object);

            var result = controller.GetSite(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<MapModelTotalLosses>>>>(result);
        }

        [Fact]
        public void GetTurbineShouldReturnActionResultListMapModel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var configurationMock = new Mock<IConfiguration>();
            var query = new Query();

            var controller = new MapController(mediatorMoq.Object, configurationMock.Object);

            var result = controller.GetTurbine(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<MapModelTotalLosses>>>>(result);
        }

        [Fact]
        public void GetDefaultPerformanceShouldReturnActionResultPerformanceModel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var configurationMock = new Mock<IConfiguration>();

            var controller = new MapController(mediatorMoq.Object, configurationMock.Object);

            var result = controller.GetPerformance(true);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<PerformanceModel>>>(result);
        }

        [Fact]
        public void GetPerformanceShouldReturnActionResultPerformanceModel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var configurationMock = new Mock<IConfiguration>();

            var controller = new MapController(mediatorMoq.Object, configurationMock.Object);

            var result = controller.GetPerformance(false);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<PerformanceModel>>>(result);
        }

        [Fact]
        public void SetPerformanceShouldReturnActionResultPerformanceModel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var configurationMock = new Mock<IConfiguration>();
            var performanceModel = new PerformanceModel();

            var controller = new MapController(mediatorMoq.Object, configurationMock.Object);

            var result = controller.SetPerformance(performanceModel);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<bool>>>(result);
        }
    }
}
