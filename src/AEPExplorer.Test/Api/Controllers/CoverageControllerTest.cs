using AEPExplorer.API.Controllers;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.Test.Api.Controllers
{
    public class CoverageControllerTest
    {
        [Fact]
        public void GetAverageCoverageShouldReturnActionResultCoverageModel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();
            
            var controller = new CoverageController(mediatorMoq.Object);
            
            var result = controller.GetAverageCoverage(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<CoverageModel>>>(result);
        }

        [Fact]
        public void GetItemBarchartCoverageShouldReturnActionResultCoverageItemModelList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();
            var controller = new CoverageController(mediatorMoq.Object);

            var result = controller.GetItemBarchartCoverage(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<CoverageItemModel>>>>(result);
        }
    }
}
