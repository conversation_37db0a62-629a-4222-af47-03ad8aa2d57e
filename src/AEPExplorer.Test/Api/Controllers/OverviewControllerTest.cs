using AEPExplorer.API.Controllers;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using iText.Layout.Element;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.Test.Api.Controllers;

public class OverviewControllerTest
{
    [Fact]
    public void GetAverageCoverageShouldReturnActionResultKpiModel()
    {
        var mediatorMoq = new Mock<IMediator>();
        var query = new Query();
            
        var controller = new OverviewController(mediatorMoq.Object);
            
        var result = controller.GetActualProductionVsLostEnergy(query);
        Assert.NotNull(result);
        Assert.IsType<Task<ActionResult<List<NameValuePercentage>>>>(result);
    }

    [Fact]
    public void GetLpfTimelineShouldReturnActionResultLpfTimelineModel()
    {
        var mediatorMoq = new Mock<IMediator>();
        var query = new Query();

        var controller = new OverviewController(mediatorMoq.Object);

        var result = controller.GetDataCoverage(query);
        Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<CoverageTimelineModel>>>>(result);
    }

    [Fact]
    public void GetLossesByPlatformShouldReturnActionResultLossesByPlatformModelList()
    {
        var mediatorMoq = new Mock<IMediator>();
        var query = new Query();

        var controller = new OverviewController(mediatorMoq.Object);

        var result = controller.GetLossesByPlatform(query);
        Assert.NotNull(result);
        Assert.IsType<Task<ActionResult<List<LossesByPlatformModel>>>>(result);
    }

    [Fact]
    public void GetLossesByPlatformAndCategoryShouldReturnActionResultNameValuePercentageList()
    {
        var mediatorMoq = new Mock<IMediator>();
        var query = new Query();
        var platform = Guid.NewGuid();
        var controller = new OverviewController(mediatorMoq.Object);

        var result = controller.GetLossesByPlatformAndCategory(platform, query);
        Assert.NotNull(result);
        Assert.IsType<Task<ActionResult<List<NameValuePercentageWithChildren>>>>(result);
    }

    [Fact]
    public void GetLossesByCategoryShouldReturnActionResultLossLevelModelList()
    {
        var mediatorMoq = new Mock<IMediator>();
        var query = new Query();

        var controller = new OverviewController(mediatorMoq.Object);

        var result = controller.GetLossesByCategory(query);
        Assert.NotNull(result);
        Assert.IsType<Task<ActionResult<List<LossLevel>>>>(result);
    }
}