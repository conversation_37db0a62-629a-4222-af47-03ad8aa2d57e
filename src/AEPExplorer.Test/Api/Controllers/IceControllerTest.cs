using AEPExplorer.API.Controllers;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.Test.Api.Controllers
{
    public class IceControllerTest
    {
        [Fact]
        public void GetIceDataAnalyticsLossesTotalShouldReturnActionResultIceDataAnalyticsLossesTotal()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();

            var controller = new IceController(mediatorMoq.Object);

            var result = controller.GetIceDataAnalyticsLossesTotal(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<NameValuePercentageWithChildren>>>>(result);
        }

        [Fact]
        public void GetIceDataAnalyticsLossesTimelineShouldReturnActionResultIceListLossesWithDate()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();

            var controller = new IceController(mediatorMoq.Object);

            var result = controller.GetIceDataAnalyticsLossesTimeline(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<IceLossesWithDate>>>>(result);
        }

        [Fact]
        public void GetIceDataAnalyticsDurationShouldReturnActionResultListNameValue()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();

            var controller = new IceController(mediatorMoq.Object);

            var result = controller.GetIceDataAnalyticsDistribution(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<NameValue>>>>(result);
        }

        [Fact]
        public void GetIceDataAnalyticsPeriodShouldReturnActionResultIceDataAnalyticsPeriod()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();


            var controller = new IceController(mediatorMoq.Object);

            var result = controller.GetIceDataAnalyticsPeriod(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<IceDataAnalyticsDistributionOverTime>>>(result);
        }

        [Fact]
        public void GetEpcDataShouldReturnActionResultListValueLabel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();


            var controller = new IceController(mediatorMoq.Object);

            var result = controller.GetEpcData(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<ValueLabel>>>>(result);
        }

        [Fact]
        public void GetEpcListShouldReturnActionResultListValueLabel()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();


            var controller = new IceController(mediatorMoq.Object);

            var result = controller.GetEpcList(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<ValueIntLabel>>>>(result);
        }

        [Fact]
        public void GetEpcLinesShouldReturnActionResultListOfEpiricalPowerCurveLine()
        {
            var mediatorMoq = new Mock<IMediator>();
            var epcQuery = new EpcQuery();


            var controller = new IceController(mediatorMoq.Object);

            var result = controller.GetEpcLines(epcQuery);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<EmpiricalPowerCurveLine>>>>(result);
        }

        [Fact]
        public void GetEpcDotsShouldReturnActionResultListEpcDots()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();


            var controller = new IceController(mediatorMoq.Object);

            var result = controller.GetEpcDots(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<IceEstimatesPowerData>>>>(result);
        }
    }
}
