using AEPExplorer.API.Controllers;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.Test.Api.Controllers
{
    public class ComparisonControllerTest
    {
        [Fact]
        public void GetProductionLossesTimelineShouldReturnActionResultProductionAndLossesTimelineComparison()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();
            
            var controller = new ComparisonController(mediatorMoq.Object);
            
            var result = controller.GetProductionLossesTimeline(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<ProductionAndLossesTimelineComparison>>>(result);
        }

        [Fact]
        public void GetProductionBarchartShouldReturnActionResultBarChartComparisonList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();

            var controller = new ComparisonController(mediatorMoq.Object);

            var result = controller.GetProductionLossesBarchart(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<BarChartComparison>>>>(result);
        }

        [Fact]
        public void GetLossesBarchartShouldReturnActionResultLossesBarChartComparisonList()
        {
            var mediatorMoq = new Mock<IMediator>();
            var query = new Query();

            var controller = new ComparisonController(mediatorMoq.Object);

            var result = controller.GetLossesBarchart(query);
            Assert.NotNull(result);
            Assert.IsType<Task<ActionResult<List<LossesByGroups>>>>(result);
        }
    }
}
