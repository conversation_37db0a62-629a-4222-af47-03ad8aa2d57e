using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Service;

namespace AEPExplorer.Test.Service.Elasticsearch
{
    public class ElasticsearchGeneralServiceTest
    {
        // [Fact]
        // public void CreateIndexShouldReturnBoolean()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //
        //     var result = ElasticsearchGeneralService.CreateIndex<AepElasticData>(client, "test");
        //
        //     Assert.IsType<bool>(result);
        // }
        //
        // [Fact]
        // public void DeleteIndexShouldReturnBoolean()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //
        //     var result = ElasticsearchGeneralService.DeleteIndex(client, "test");
        //
        //     Assert.IsType<Task<bool>>(result);
        // }
        //
        // [Fact]
        // public void BulkInsertShouldReturnBoolean()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //
        //     var mockData = new Mock<List<AepElasticData>>();
        //
        //     var result = ElasticsearchGeneralService.BulkInsert(client, mockData.Object, "test");
        //
        //     Assert.IsType<bool>(result);
        // }
        //
        // [Fact]
        // public void BulkInsertAsyncShouldReturnTaskBoolean()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //
        //     var mockData = new Mock<List<AepElasticData>>();
        //
        //     var result = ElasticsearchGeneralService.BulkInsertAsync(client, mockData.Object, "test");
        //
        //     Assert.IsType<Task<bool>>(result);
        // }
        //
        // [Fact]
        // public async Task GetFilterShouldReturnListFuncQueryContainerDescriptorAepElasticDataAndQueryContainer()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //
        //     var query = new Query();
        //     
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchGeneralService.GetFilter(client, query));
        // }
        //
        // [Fact]
        // public async Task GetQueryContainerShouldReturnQueryContainer()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //
        //     var query = new Query { Region = new List<Guid> { Guid.NewGuid() } };
        //     var result = 
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchGeneralService.GetQueryContainer(client, query));
        // }
        //
        // [Fact]
        // public async Task GetDistinctValuesShouldReturnBoolean()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //
        //     var query = new Query { Region = new List<Guid> { Guid.NewGuid() } };
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchGeneralService.GetDistinctValuesAsync(client, x => x.RegionName, query));
        // }       
    }
}
