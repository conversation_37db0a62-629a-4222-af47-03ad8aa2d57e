using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Service;

namespace AEPExplorer.Test.Service.Elasticsearch
{
    public class ElasticsearchSumServiceTest
    {
        // [Fact]
        // public async Task GetSumAsyncShouldThrowException()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings); var query = new Query();
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchSumService.GetSumAsync(client, x => x.RegionName, query));
        // }
        //
        // [Fact]
        // public async Task GetSumScriptAsyncThrowException()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //
        //     var query = new Query();
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchSumService.GetSumAsync(client, "test script", query));
        //
        // }
        //
        // [Fact]
        // public async Task GetSumByDateHistogramAsyncThrowException()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //     var query = new Query();
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchSumService.GetSumByDateHistogramAsync(client, x => x.RegionName, query, DateInterval.Day));
        // }
        //
        // [Fact]
        // public async Task GetSumByDateHistogramScriptAsyncThrowException()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //     var query = new Query();
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchSumService.GetSumByDateHistogramAsync(client, "test script", query, DateInterval.Day));
        // }
        //
        // [Fact]
        // public async Task GetSumByGroupPropertyAsyncAsyncThrowException()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //     var query = new Query();
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchSumService.GetSumByGroupPropertyAsync(client, x => x.RegionName, x => x.CountryName, query));
        // }
        //
        // [Fact]
        // public async Task GetSumByGroupPropertyScriptAsyncAsyncThrowException()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //     var query = new Query();
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchSumService.GetSumByGroupPropertyAsync(client, "test script", x => x.RegionName, query));
        // }
    }
}
