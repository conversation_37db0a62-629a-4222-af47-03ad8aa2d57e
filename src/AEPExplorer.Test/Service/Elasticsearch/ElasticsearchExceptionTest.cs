using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.ElasticsearchTest
{
    public class ElasticsearchExceptionTest
    {

        [Fact]
        public void ElasticsearchExceptionShouldReturnDefaultMessage()
        {
            var exception = new ElasticsearchException();

            var result = exception.Message;

            Assert.NotNull(result);
            Assert.Equal("Elasticsearch returned an error.", result);
        }

        [Fact]
        public void ElasticsearchExceptionShouldReturnCorrectMessage()
        {
            var exception = new ElasticsearchException("Test");

            var result = exception.Message;

            Assert.NotNull(result);
            Assert.Equal("Test", result);
        }

        [Fact]
        public void ElasticsearchExceptionShouldReturnInnerException()
        {
            var exception = new ElasticsearchException("Test", new Exception("Test inner exception"));

            var result = exception.InnerException;

            Assert.NotNull(result);
            Assert.Equal("Test inner exception", result.Message);
        }
    }
}