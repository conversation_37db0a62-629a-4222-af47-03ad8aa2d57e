using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Service;

namespace AEPExplorer.Test.Service.Elasticsearch
{
    public class ElasticsearchAverageServiceTest
    {
        // [Fact]
        // public async Task GetSumAsyncShouldThrowException()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings); var query = new Query();
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchAverageService.GetAverageAsync(client, x => x.RegionName, query));
        // }
        //
        // [Fact]
        // public async Task GetSumByDateHistogramAsyncThrowException()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //     var query = new Query();
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchAverageService.GetAverageByDateHistogramAsync(client, x => x.RegionName, query, DateInterval.Day));
        // }        
        //
        // [Fact]
        // public async Task GetSumByGroupPropertyAsyncAsyncThrowException()
        // {
        //     var url = "http://localhost";
        //     var username = "testuser";
        //     var password = "testpassword";
        //     var defaultIndex = "test";
        //
        //     var settings = new ConnectionSettings(new Uri(url))
        //         .DefaultMappingFor<AepElasticData>(x => x.IndexName(defaultIndex))
        //         .DefaultIndex(defaultIndex)
        //         .BasicAuthentication(username, password);
        //
        //     var client = new ElasticClient(settings);
        //     var query = new Query();
        //
        //     await Assert.ThrowsAsync<ElasticsearchException>(async () => await ElasticsearchAverageService.GetAverageByGroupPropertyAsync(client, x => x.RegionName, x => x.CountryName, query));
        // }
    }
}
