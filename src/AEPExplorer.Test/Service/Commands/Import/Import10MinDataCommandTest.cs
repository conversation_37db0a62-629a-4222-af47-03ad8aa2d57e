namespace AEPExplorer.Test.Service.Commands.Import
{
    //public class Import10MinDataCommandTest
    //{
    //    [Fact]
    //    public void Import10MinDataHandlerShouldReturnBoolean()
    //    {
    //        var mockElasticsearchClient = new Mock<IElasticClient>();
    //        Mock<IConfiguration> configurationStub = new Mock<IConfiguration>();

    //        var handler = new Import10MinDataCommandHandler(mockElasticsearchClient.Object, configurationStub.Object);
    //        var request = new Import10MinDataCommand();
    //        var result = handler.Handle(request, new System.Threading.CancellationToken());

    //        Assert.IsType<Task<bool>>(result);
    //    }
    //}
}