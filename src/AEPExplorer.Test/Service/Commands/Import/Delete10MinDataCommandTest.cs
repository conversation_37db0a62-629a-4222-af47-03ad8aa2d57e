namespace AEPExplorer.Test.Service.Commands.Import
{
    //public class Delete10MinDataCommandTest
    //{
    //    [Fact]
    //    public void Delete10MinDataHandlerShouldReturnBoolean()
    //    {            
    //        var mockElasticsearchClient = new Mock<IElasticClient>();
    //        Mock<IConfiguration> configurationStub = new Mock<IConfiguration>();

    //        var handler = new Delete10MinElasticsearchIndexCommandHandler(mockElasticsearchClient.Object, configurationStub.Object);
    //        var request = new Delete10MinElasticsearchIndexCommand();
    //        var result = handler.Handle(request, new System.Threading.CancellationToken());

    //        Assert.IsType<Task<bool>>(result);
    //    }
    //}
}
