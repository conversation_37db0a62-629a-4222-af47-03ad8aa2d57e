using AEPExplorer.Data.EF;
using AEPExplorer.Service.Commands.FilterPreset;
using Microsoft.EntityFrameworkCore;

namespace AEPExplorer.Test.Service.Commands.FilterPreset
{
    public class CreateFilterPresetCommandTest
    {
        [Fact]
        public void CreateFilterPresetShouldReturnGuid()
        {
            var options = new DbContextOptionsBuilder<AepExplorerDbContext>()
            .UseInMemoryDatabase(databaseName: "AEPExplorer")
            .Options;

            using var context = new AepExplorerDbContext(options);
            var handler = new CreateFilterPresetCommandHandler(context);
            var request = new CreateFilterPresetCommand
            {
                Name = "Test",
                Query = new Model.Request.QueryPreset(),
                UserId = Guid.NewGuid()
            };

            var result = handler.Handle(request, new CancellationToken());

            Assert.NotNull(result);
            Assert.IsType<Task<Guid>>(result);
        }
    }
}