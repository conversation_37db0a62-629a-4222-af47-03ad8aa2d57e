using AEPExplorer.Data.EF;
using AEPExplorer.Service.Commands;
using AEPExplorer.Service.Commands.FilterPreset;
using Microsoft.EntityFrameworkCore;

namespace AEPExplorer.Test.Service.Commands
{
     public class UpdateFavoriteFilterPresetCommandTest
    {
        [Fact]
        public async Task UpdateFavoriteFilterPresetShouldReturnGuid()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var filterPresetId = Guid.NewGuid();

            var options = new DbContextOptionsBuilder<AepExplorerDbContext>()
                .UseInMemoryDatabase(databaseName: $"AEPExplorer_{Guid.NewGuid()}")  // Unique DB name for test isolation
                .Options;

            using var context = new AepExplorerDbContext(options);
            await context.Database.EnsureDeletedAsync();
            await context.Database.EnsureCreatedAsync();

            var filterPreset = new Model.Domain.FilterPreset 
            { 
                Id = filterPresetId,
                Name = "Test",                
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            };
            await context.FilterPreset.AddAsync(filterPreset);
            await context.SaveChangesAsync();

            var handler = new UpdateFavoriteFilterPresetCommandHandler(context);
            var request = new UpdateFavoriteFilterPresetCommand
            {
                Id = filterPresetId,
                Favorite = true,
                UserId = userId
            };

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.HasValue, "Expected a Guid value but got null");
            Assert.Equal(filterPresetId, result.Value);

            // Verify the database was actually updated
            var updatedPreset = await context.FilterPreset.FindAsync(filterPresetId);
            Assert.NotNull(updatedPreset);
            Assert.True(updatedPreset.Favorite);
        }

        [Fact]
        public async Task UpdateFavoriteFilterPresetShouldReturnNull()
        {
            var userId = Guid.NewGuid();
            var filterPresetId = Guid.NewGuid();

            var options = new DbContextOptionsBuilder<AepExplorerDbContext>()
            .UseInMemoryDatabase(databaseName: "AEPExplorer")
            .Options;

            using var context = new AepExplorerDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();

            context.FilterPreset.Add(new Model.Domain.FilterPreset
            {
                Id = filterPresetId,
                Name = "Test",
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            });
            await context.SaveChangesAsync();

            var handler = new UpdateFavoriteFilterPresetCommandHandler(context);
            var request = new UpdateFavoriteFilterPresetCommand
            {
                Id = filterPresetId,
                Favorite = true,
                UserId = Guid.NewGuid()
            };

            var result = await handler.Handle(request, new CancellationToken());

            Assert.Null(result);
        }
    }
}