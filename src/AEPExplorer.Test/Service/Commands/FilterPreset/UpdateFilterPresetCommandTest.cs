using AEPExplorer.Data.EF;
using AEPExplorer.Service.Commands;
using Microsoft.EntityFrameworkCore;

namespace AEPExplorer.Test.Service.Commands
{
    public class UpdateFilterPresetCommandTest
    {
        [Fact]
        public async Task UpdateFilterPresetShouldReturnGuid()
        {
            var userId = Guid.NewGuid();
            var filterPresetId = Guid.NewGuid();

            var options = new DbContextOptionsBuilder<AepExplorerDbContext>()
            .UseInMemoryDatabase(databaseName: "AEPExplorerUpdate")
            .Options;

            using var context = new AepExplorerDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();

            context.FilterPreset.Add(new Model.Domain.FilterPreset { 
                Id = filterPresetId,
                Name = "Test",                
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            });
            await context.SaveChangesAsync();

            var handler = new UpdateFilterPresetCommandHandler(context);
            var request = new UpdateFilterPresetCommand
            {
                Id = filterPresetId,
                Name = "Test2",
                UserId = userId
            };

            var result = await handler.Handle(request, new CancellationToken());

            Assert.NotNull(result);
            Assert.IsType<Guid>(result);
            Assert.Equal(filterPresetId, result);
        }

        [Fact]
        public async Task UpdateFilterPresetShouldReturnNull()
        {
            var userId = Guid.NewGuid();
            var filterPresetId = Guid.NewGuid();

            var options = new DbContextOptionsBuilder<AepExplorerDbContext>()
            .UseInMemoryDatabase(databaseName: "AEPExplorer")
            .Options;

            using var context = new AepExplorerDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();

            context.FilterPreset.Add(new Model.Domain.FilterPreset
            {
                Id = filterPresetId,
                Name = "Test",
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            });
            await context.SaveChangesAsync();

            var handler = new UpdateFilterPresetCommandHandler(context);
            var request = new UpdateFilterPresetCommand
            {
                Id = filterPresetId,
                Name = "Test2",
                UserId = Guid.NewGuid()
            };

            var result = await handler.Handle(request, new CancellationToken());

            Assert.Null(result);
        }
    }
}