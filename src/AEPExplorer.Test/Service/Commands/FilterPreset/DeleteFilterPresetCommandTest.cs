using AEPExplorer.Data.EF;
using AEPExplorer.Service.Commands;
using Microsoft.EntityFrameworkCore;

namespace AEPExplorer.Test.Service.Commands
{
    public class DeleteFilterPresetCommandTest
    {
        [Fact]
        public async Task DeleteFilterPresetShouldReturnTrue()
        {
            var userId = Guid.NewGuid();
            var filterPresetId = Guid.NewGuid();

            var options = new DbContextOptionsBuilder<AepExplorerDbContext>()
            .UseInMemoryDatabase(databaseName: "AEPExplorerDelete")
            .Options;

            using var context = new AepExplorerDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();

            context.FilterPreset.Add(new Model.Domain.FilterPreset
            {
                Id = filterPresetId,
                Name = "Test",
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            });
            await context.SaveChangesAsync();

            var handler = new DeleteFilterPresetCommandHandler(context);
            var request = new DeleteFilterPresetCommand
            {
                Id = filterPresetId,
                UserId = userId
            };

            var result = await handler.Handle(request, new CancellationToken());            
            
            Assert.IsType<bool>(result);
            Assert.True(result);
        }

        [Fact]
        public async Task DeleteFilterPresetShouldReturnFalse()
        {
            var userId = Guid.NewGuid();
            var filterPresetId = Guid.NewGuid();

            var options = new DbContextOptionsBuilder<AepExplorerDbContext>()
            .UseInMemoryDatabase(databaseName: "AEPExplorer")
            .Options;

            using var context = new AepExplorerDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();

            context.FilterPreset.Add(new Model.Domain.FilterPreset
            {
                Id = filterPresetId,
                Name = "Test",
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            });
            await context.SaveChangesAsync();

            var handler = new DeleteFilterPresetCommandHandler(context);
            var request = new DeleteFilterPresetCommand
            {
                Id = filterPresetId,
                UserId = Guid.NewGuid()
            };

            var result = await handler.Handle(request, new CancellationToken());

            Assert.IsType<bool>(result);
            Assert.False(result);
        }
    }
}