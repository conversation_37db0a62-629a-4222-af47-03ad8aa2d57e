using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Overview;

namespace AEPExplorer.Test
{
    public class LossByCategoryQueryTest
    {
        //[Fact]
        //public void GetLossByCategoryShouldReturnNameValuePercentageList()
        //{
        //    var mockElasticsearchClient = new Mock<IElasticClient>();
        //    var handler = new LossByCategoryQueryHandler(mockElasticsearchClient.Object);
        //    var request = new LossByCategoryQuery
        //    {
        //        Query = new Query()
        //    };

        //    var result = handler.Handle(request, new CancellationToken());

        //    Assert.NotNull(result);
        //    Assert.IsType<Task<List<NameValuePercentage>>>(result);
        //}
    }
}
