using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Overview;

namespace AEPExplorer.Test.Service.Queries.DataCoverage;

public class DataCoverageQueryTest
{
    [Fact]
    public void GetCoverageShouldReturnCoverageModel()
    {
        var mockElasticsearchClient = new Mock<IElasticClient>();
        var handler = new DataCoverageQueryHandler(mockElasticsearchClient.Object);
        var request = new DataCoverageQuery
        {
            Query = new Query()
        };

        var result = handler.Handle(request, new CancellationToken());

        Assert.NotNull(result);
        Assert.IsType<Task<List<CoverageTimelineModel>>>(result);
    }
}