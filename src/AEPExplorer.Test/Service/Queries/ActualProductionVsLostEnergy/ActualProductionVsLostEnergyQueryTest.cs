using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Overview;

namespace AEPExplorer.Test;

public class ActualProductionVsLostEnergyQueryTest
{
    [Fact]
    public void GetKpiShouldReturnKpiModel()
    {
        var mockElasticsearchClient = new Mock<IElasticClient>();
        var handler = new ActualProductionVsLostEnergyQueryHandler(mockElasticsearchClient.Object);
        var request = new ActualProductionVsLostEnergyQuery
        {
            Query = new Query()
        };

        var result = handler.Handle(request, new CancellationToken());

        Assert.NotNull(result);
        Assert.IsType<Task<List<NameValuePercentage>>>(result);
    }
}