using AEPExplorer.Data.EF;
using AEPExplorer.Model.Domain;
using AEPExplorer.Service.Queries;
using AEPExplorer.Service.Queries.FilterPreset;
using Microsoft.EntityFrameworkCore;

namespace AEPExplorer.Test
{
    public class FilterPresetQueryTest
    {
        [Fact]
        public async Task GetFilterPresetShouldReturnListOfFilterPresets()
        {
            var userId = Guid.NewGuid();
            var filterPresetId = Guid.NewGuid();

            var options = new DbContextOptionsBuilder<AepExplorerDbContext>()
            .UseInMemoryDatabase(databaseName: "AEPExplorer")
            .Options;

            using var context = new AepExplorerDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();

            context.FilterPreset.Add(new FilterPreset
            {
                Id = filterPresetId,
                Name = "Test",
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            });
            await context.SaveChangesAsync();

            var handler = new FilterPresetQueryHandler(context);
            var request = new FilterPresetQuery
            {
                UserId = userId
            };

            var result = await handler.Handle(request, new CancellationToken());

            Assert.NotNull(result);
            Assert.IsType<List<FilterPreset>>(result);
        }
    }
}
