using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Filter;
using Microsoft.EntityFrameworkCore;

namespace AEPExplorer.Test
{
    public class CategoryFilterQueryTest
    {
        [Fact]
        public void GetCategoryShouldReturnValueLabelType()
        {
            var options = new DbContextOptionsBuilder<AepExplorerDbContext>()
                .UseInMemoryDatabase(databaseName: "AEPExplorer")
                .Options;

            using var context = new AepExplorerDbContext(options);
            var handler = new CategoryFilterQueryHandler(context);
            var request = new CategoryFilterQuery();
            
            var result = handler.Handle(request, new CancellationToken());

            Assert.NotNull(result);
            Assert.IsType<Task<List<ValueLabelWithChildren>>>(result);
        }      
    }
}
