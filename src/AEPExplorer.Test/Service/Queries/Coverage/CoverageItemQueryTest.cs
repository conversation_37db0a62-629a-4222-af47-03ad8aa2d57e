namespace AEPExplorer.Test.Service.Queries.Coverage
{
    public class CoverageItemQueryTest
    {
        // [Fact]
        // public void GetCoverageItemShouldReturnCoverageItemModelList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new CoverageItemQueryHandler(mockElasticsearchClient.Object);
        //     var request = new CoverageItemQuery
        //     {
        //         Query = new Query()
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<CoverageItemModel>>>(result);
        // }
    }
}
