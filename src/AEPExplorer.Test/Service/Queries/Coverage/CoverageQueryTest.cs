using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Coverage;

namespace AEPExplorer.Test.Service.Queries.Coverage
{
    public class CoverageQueryTest
    {
        [Fact]
        public void GetCoverageShouldReturnCoverageModel()
        {
            var mockElasticsearchClient = new Mock<IElasticClient>();
            var handler = new CoverageQueryHandler(mockElasticsearchClient.Object);
            var request = new CoverageQuery
            {
                Query = new Query()
            };

            var result = handler.Handle(request, new CancellationToken());

            Assert.NotNull(result);
            Assert.IsType<Task<CoverageModel>>(result);
        }
    }
}
