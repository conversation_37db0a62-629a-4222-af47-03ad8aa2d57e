using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Comparison;

namespace AEPExplorer.Test
{
    public class ProductionAndLossesBarChartComparisonQueryTest
    {
        // [Fact]
        // public void GetProductionAndLossesBarChartForTurbineShouldReturnBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             TurbineId = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<BarChartComparison>>>(result);
        // }
        //
        // [Fact]
        // public void GetProductionAndLossesBarChartForSiteShouldReturnBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Site = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<BarChartComparison>>>(result);
        // }
        //
        // [Fact]
        // public void GetProductionAndLossesBarChartForCountryShouldReturnBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //
        //             Country = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<BarChartComparison>>>(result);
        // }
        //
        // [Fact]
        // public void GetProductionAndLossesBarChartForRegionShouldReturnBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Region = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<BarChartComparison>>>(result);
        // }
        //
        // [Fact]
        // public void GetProductionAndLossesBarChartForRegionAndCategoryShouldReturnBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Region = new List<Guid> { Guid.NewGuid() },
        //             Category = new List<string> { "testCategory" }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<BarChartComparison>>>(result);
        // }
    }
}