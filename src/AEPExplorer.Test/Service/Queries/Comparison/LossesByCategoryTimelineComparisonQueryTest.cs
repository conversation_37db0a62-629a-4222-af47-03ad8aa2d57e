using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Comparison;

namespace AEPExplorer.Test
{
    public class LossesByCategoryTimelineComparisonQueryTest
    {
        // [Fact]
        // public void GetLossesByCategoryTimelineForTurbineShouldReturnLossesTimelineChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             TurbineId = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesTimelineChartComparison>>>(result);
        // }
        //
        // [Fact]
        // public void GetLossesByCategoryTimelineForSiteShouldReturnLossesTimelineChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Site = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesTimelineChartComparison>>>(result);
        // }
        //
        // [Fact]
        // public void GetLossesByCategoryTimelineForCountryShouldReturnLossesTimelineChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Country = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesTimelineChartComparison>>>(result);
        // }
        //
        // [Fact]
        // public void GetLossesByCategoryTimelineForRegionShouldReturnLossesTimelineChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Region = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesTimelineChartComparison>>>(result);
        // }
        //
        // [Fact]
        // public void GetLossesByCategoryTimelineForRegionAndCategoryShouldReturnLossesTimelineChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Region = new List<Guid> { Guid.NewGuid() },
        //             Category = new List<string> { "testCategory" }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesTimelineChartComparison>>>(result);
        // }
    }
}