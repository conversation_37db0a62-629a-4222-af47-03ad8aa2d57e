using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Comparison;

namespace AEPExplorer.Test
{
    public class ProductionAndLossesTimelineComparisonQueryTest
    {
        // [Fact]
        // public void GetProductionAndLossesTimelineForTurbineShouldReturnProductionAndLossesTimelineComparisonType()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             TurbineId = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<ProductionAndLossesTimelineComparison>>(result);
        // }
        //
        // [Fact]
        // public void GetProductionAndLossesTimelineForSiteShouldReturnProductionAndLossesTimelineComparisonType()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Site = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<ProductionAndLossesTimelineComparison>>(result);
        // }
        //
        // [Fact]
        // public void GetProductionAndLossesTimelineForCountryShouldReturnProductionAndLossesTimelineComparisonType()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //
        //             Country = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<ProductionAndLossesTimelineComparison>>(result);
        // }
        //
        // [Fact]
        // public void GetProductionAndLossesTimelineForRegionShouldReturnProductionAndLossesTimelineComparisonType()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Region = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<ProductionAndLossesTimelineComparison>>(result);
        // }
        //
        // [Fact]
        // public void GetProductionAndLossesTimelineForRegionAndCategoryShouldReturnProductionAndLossesTimelineComparisonType()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new ProductionAndLossesTimelineComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new ProductionAndLossesTimelineComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Region = new List<Guid> { Guid.NewGuid() },
        //             Category = new List<string> { "testCategory" }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<ProductionAndLossesTimelineComparison>>(result);
        // }
    }
}