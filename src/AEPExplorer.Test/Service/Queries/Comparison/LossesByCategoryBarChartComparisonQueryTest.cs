using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Comparison;

namespace AEPExplorer.Test
{
    public class LossesByCategoryBarChartComparisonQueryTest
    {
        // [Fact]
        // public void GetLossesByCategoryBarChartForTurbineShouldReturnLossesBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             TurbineId = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesByGroups>>>(result);
        // }
        //
        // [Fact]
        // public void GetLossesByCategoryBarChartForSiteShouldReturnLossesBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Site = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesByGroups>>>(result);
        // }
        //
        // [Fact]
        // public void GetLossesByCategoryBarChartForCountryShouldReturnLossesBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Country = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesByGroups>>>(result);
        // }
        //
        // [Fact]
        // public void GetLossesByCategoryBarChartForRegionShouldReturnLossesBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Region = new List<Guid> { Guid.NewGuid() }
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesByGroups>>>(result);
        // }
        //
        // [Fact]
        // public void GetLossesByCategoryBarChartForRegionAndCategoryShouldReturnLossesBarChartComparisonList()
        // {
        //     var mockElasticsearchClient = new Mock<IElasticClient>();
        //     var handler = new LossesByCategoryBarChartComparisonQueryHandler(mockElasticsearchClient.Object);
        //     var request = new LossesByCategoryBarChartComparisonQuery
        //     {
        //         Query = new Query
        //         {
        //             Region = new List<Guid> { Guid.NewGuid() },
        //             Category = new List<string> { "testCategory"}
        //         }
        //     };
        //
        //     var result = handler.Handle(request, new CancellationToken());
        //
        //     Assert.NotNull(result);
        //     Assert.IsType<Task<List<LossesByGroups>>>(result);
        // }
    }
}
