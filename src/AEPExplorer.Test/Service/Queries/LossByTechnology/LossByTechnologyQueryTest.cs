namespace AEPExplorer.Test
{
    public class LossByPlatformQueryTest
    {
        //[Fact]
        //public void GetCoverageShouldReturnLossesByPlatformModelList()
        //{
        //    var mockElasticsearchClient = new Mock<IElasticClient>();
        //    var handler = new LossByTechonologyQueryHandler(mockElasticsearchClient.Object, );
        //    var request = new LossByPlatformQuery
        //    {
        //        Query = new Query()
        //    };

        //    var result = handler.Handle(request, new System.Threading.CancellationToken());

        //    Assert.NotNull(result);
        //    Assert.IsType<Task<List<LossesByPlatformModel>>>(result);
        //}
    }
}
