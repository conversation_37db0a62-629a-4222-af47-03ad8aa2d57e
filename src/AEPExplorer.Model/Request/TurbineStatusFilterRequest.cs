using AEPExplorer.Model.Enums;

namespace AEPExplorer.Model.Request
{
    public class TurbineStatusFilterRequest
    {
        private int _page;
        public int Page
        {
            get { return _page > 0 ? _page : 1; }
            set { _page = value; }
        }
        public string SortColumn { get; set; }
        public SortDirectionEnum? SortDirection { get; set; }
        public List<string> RegionName { get; set; } = new List<string>();
        public List<string> RegionShortName { get; set; } = new List<string>();
        public List<string> CountryName { get; set; } = new List<string>();
        public List<string> LocationTypeName { get; set; } = new List<string>();
        public List<string> ProjectParkName { get; set; } = new List<string>();
        public List<string> ScadaParkName { get; set; } = new List<string>();
        public List<string> ProjectParkId { get; set; } = new List<string>();
        public List<string> TurbineName { get; set; } = new List<string>();
        public List<int> TurbineId { get; set; } = new List<int>();
        public List<string> TurbineOem { get; set; } = new List<string>(); 
        public List<string> TurbinePlatform { get; set; } = new List<string>(); 
        public List<string> TurbineModel { get; set; } = new List<string>(); 
        public bool? IsTurbinePresent { get; set; }
        public List<string> TurbineMissingInfo { get; set; } = new List<string>();
    }
}
