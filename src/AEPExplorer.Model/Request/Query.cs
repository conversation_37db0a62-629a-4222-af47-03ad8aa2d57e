using AEPExplorer.Model.Enums;

namespace AEPExplorer.Model.Request;

public class Query
{
    private DateTime? _dateFrom;
    private DateTime? _dateTo;
    private int _page;
    public List<Guid> Customer { get; set; }
    public List<Guid> Region { get; set; }
    public List<Guid> Country { get; set; }
    public List<Guid> Site { get; set; }
    public List<Guid> TurbineId { get; set; }
    public List<Guid> Platform { get; set; }
    public List<Guid> Model { get; set; }
    public List<Guid> Category { get; set; }
    public List<LocationTypeNameEnum> LocationTypeName { get; set; }
    public CalculationMethodEnum CalculationMethod { get; set; } = CalculationMethodEnum.EnsembleTPCPEP;

    public bool FilteredCategories => Category != null && Category.Any();

    public DateTime? DateFrom
    {
        get => _dateFrom != null ? new DateTime(_dateFrom.Value.Year, _dateFrom.Value.Month, _dateFrom.Value.Day, 0, 0, 0, 000, DateTimeKind.Utc) : null;

        set => _dateFrom = value;
    }

    public DateTime? DateTo
    {
        get => _dateTo != null ? new DateTime(_dateTo.Value.Year, _dateTo.Value.Month, _dateTo.Value.Day, 23, 59, 59, 999, DateTimeKind.Utc) : null;

        set => _dateTo = value;
    }

    public int Page
    {
        get { return _page > 0 ? _page : 1; }
        set { _page = value; }
    }

    public string SortColumn { get; set; }
    public SortDirectionEnum? SortDirection { get; set; }

    public int TotalDays => DateTo.HasValue && DateFrom.HasValue ? (DateTo.Value - DateFrom.Value).Days + 1 : 1;

    public virtual Query DeepCopy()
    {
        var copy = (Query)MemberwiseClone();
        copy.DateFrom = DateFrom;
        copy.DateTo = DateTo;
        copy.Customer = Customer?.ConvertAll(x => x);
        copy.Region = Region?.ConvertAll(x => x);
        copy.Country = Country?.ConvertAll(x => x);
        copy.Site = Site?.ConvertAll(x => x);
        copy.TurbineId = TurbineId?.ConvertAll(x => x);
        copy.Platform = Platform?.ConvertAll(x => x);
        copy.Model = Model?.ConvertAll(x => x);
        copy.Category = Category?.ConvertAll(x => x);
        return copy;
    }
}