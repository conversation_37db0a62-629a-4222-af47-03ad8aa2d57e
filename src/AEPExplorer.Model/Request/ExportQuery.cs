using AEPExplorer.Model.Enums;

namespace AEPExplorer.Model.Request;

public class ExportQuery : Query
{
    public DelimiterEnum Delimiter { get; set; } = DelimiterEnum.Semicolon;
    public DecimalSeparatorEnum DecimalSeparator { get; set; } = DecimalSeparatorEnum.Dot;
    public ExportLevelEnum ExportLevel { get; set; } = ExportLevelEnum.Total;
    
    public override ExportQuery DeepCopy()
    {
        var copy = (ExportQuery)base.DeepCopy();
        copy.Delimiter = Delimiter;
        copy.DecimalSeparator = DecimalSeparator;
        copy.ExportLevel = ExportLevel;
        return copy;
    }
}