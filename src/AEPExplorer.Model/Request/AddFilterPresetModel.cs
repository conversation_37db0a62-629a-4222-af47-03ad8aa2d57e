namespace AEPExplorer.Model.Request;

public class AddFilterPresetModel
{
    public string Name { get; set; }
    public QueryPreset Query { get; set; }
}

public class QueryPreset
{
    public List<string> Region { get; set; }
    public List<string> Country { get; set; }
    public List<string> Site { get; set; }
    public List<string> TurbineId { get; set; }
    public List<string> Platform { get; set; }
    public List<string> Model { get; init; }
    public List<string> Category { get; set; }
    public DateTime? DateFrom { get; set; }
    public DateTime? DateTo { get; set; }
    public string CalculationMethod { get; set; }
}