using AEPExplorer.Model.Enums;

namespace AEPExplorer.Model.Request;

public class FilterQuery
{
    public List<Guid> Customer { get; set; }
    public List<Guid> Region { get; set; }
    public List<Guid> Country { get; set; }
    public List<Guid> Site { get; set; }
    public List<Guid> TurbineId { get; set; }
    public List<Guid> Platform { get; set; }
    public List<Guid> Model { get; set; }
    public List<Guid> Category { get; set; }

    public CalculationMethodEnum CalculationMethod { get; set; } = CalculationMethodEnum.EnsembleTPCPEP;

    public List<LocationTypeNameEnum> LocationTypeName { get; set; }

    public virtual FilterQuery DeepCopy()
    {
        var copy = (FilterQuery)MemberwiseClone();
        copy.Customer = Customer?.ConvertAll(x => x);
        copy.Region = Region?.ConvertAll(x => x);
        copy.Country = Country?.ConvertAll(x => x);
        copy.Site = Site?.ConvertAll(x => x);
        copy.TurbineId = TurbineId?.ConvertAll(x => x);
        copy.Platform = Platform?.ConvertAll(x => x);
        copy.Model = Model?.ConvertAll(x => x);
        copy.Category = Category?.ConvertAll(x => x);
        return copy;
    }
}