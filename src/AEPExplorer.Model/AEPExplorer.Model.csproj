<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<CodeAnalysisRuleSet>..\.sonarlint\dfp-aepexplorer-apicsharp.ruleset</CodeAnalysisRuleSet>
		<ImplicitUsings>enable</ImplicitUsings>
		<LangVersion>13</LangVersion>
	</PropertyGroup>

	<ItemGroup>
		<AdditionalFiles Include="..\.sonarlint\dfp-aepexplorer-api\CSharp\SonarLint.xml" Link="SonarLint.xml" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.10" />
		<PackageReference Include="NEST" Version="7.17.5" />
		<PackageReference Include="System.IO.FileSystem.Primitives" Version="4.3.0" />
	</ItemGroup>
</Project>
