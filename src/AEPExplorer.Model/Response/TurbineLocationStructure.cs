using AEPExplorer.Model.Enums;

namespace AEPExplorer.Model.Response;

public class TurbineFlat
{
    public Guid Id { get; set; }
    public int TurbineMasterDataId { get; set; }
    public Guid? CustomerId { get; set; }
    public Guid RegionId { get; set; }
    public Guid CountryId { get; set; }
    public Guid SiteId { get; set; }
    public Guid OemId { get; set; }
    public Guid PlatformId { get; set; }
    public Guid ModelId { get; set; }
    public LocationTypeNameEnum LocationTypeName { get; set; }
}

