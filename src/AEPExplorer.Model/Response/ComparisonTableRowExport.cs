namespace AEPExplorer.Model.Response;

public class ComparisonTableRowExport
{
    public string Region { get; set; }
    public string Country { get; set; }
    public string Site { get; set; }
    public string Turbine { get; set; }
    public int? NumberOfTurbines { get; set; }
    public string Platform { get; set; }
    public double? ActualProduction { get; set; }
    public double? ActualProductionPercentage { get; set; }
    public string Category { get; set; }
    public double? Losses { get; set; }
    public double? LossesPercentage { get; set; }
    public double? CapacityFactorActual { get; set; }
    public double? CapacityFactorPotential { get; set; }
    public double? Coverage { get; set; }
    public double? ProductionRatio { get; set; }
}