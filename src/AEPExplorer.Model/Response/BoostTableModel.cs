namespace AEPExplorer.Model.Response;

public class BoostTableModel
{
    public string Id { get; set; }
    public DateTime StartTimeUtc { get; set; }
    public DateTime EndTimeUtc { get; set; }
    public int Num10Min { get; set; }
    public double BoostCoverage { get; set; }
    public double BoostEnergyCnt { get; set; }
    public double BoostEnergyEst { get; set; }
    public double BoostAva { get; set; }
    public double BoostAct { get; set; }
    public double BoostExpectedTime { get; set; }
    public double BoostExpected { get; set; }
    public double BoostEfficiencyTime { get; set; }
    public double BoostEfficiency { get; set; }
    public double BoostActualTime { get; set; }
    public double BoostUtilisation { get; set; }
    public double BoostLosses { get; set; }
    public double BoostLossesPercentage { get; set; }
    public double BoostExpMin { get; set; }
    public double BoostExpMax { get; set; }
    public double BoostExpAvg { get; set; }
    public double NotCurtailedCf { get; set; }
    public double RunningRestrictedCf { get; set; }
    public double PitchCf { get; set; }
    public double DisabledCf { get; set; }
    public double SafeModeCf { get; set; }
    public double AmbientTempCf { get; set; }
    public double GridVoltageCf { get; set; }
    public double PowerFactorCf { get; set; }
    public double CoolDownCf { get; set; }
    public double TurbulenceCf { get; set; }
    public double AcsCf { get; set; }
    public double SensorErrorCf { get; set; }
    public double RsaCf { get; set; }
    public double NotCurtailedCnf { get; set; }
    public double RunningRestrictedCnf { get; set; }
    public double PitchCnf { get; set; }
    public double DisabledCnf { get; set; }
    public double SafeModeCnf { get; set; }
    public double AmbientTempCnf { get; set; }
    public double GridVoltageCnf { get; set; }
    public double PowerFactorCnf { get; set; }
    public double CoolDownCnf { get; set; }
    public double TurbulenceCnf { get; set; }
    public double AcsCnf { get; set; }
    public double SensorErrorCnf { get; set; }
    public double RsaCnf { get; set; }
}