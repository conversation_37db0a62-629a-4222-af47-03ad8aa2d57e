using AEPExplorer.Model.Enums;

namespace AEPExplorer.Model.Response;

public class MapModelDetailedLosses
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public double Losses { get; set; }
    public double RestOfLosses { get; set; }
    public double RestOfLossesPercentage { get; set; }
    public int NumberOfChildren { get; set; }
    public double Longitude { get; set; }
    public double Latitude { get; set; }
    public PerformanceEnum Performance { get; set; }
    public double PerformanceValue { get; set; }
    public List<NameValuePercentageWithChildren> Data { get; set; }
}