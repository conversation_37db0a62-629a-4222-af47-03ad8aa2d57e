
namespace AEPExplorer.Model.Response
{
    public class WindSpeedPower : IEquatable<WindSpeedPower>
    {
        public double WindSpeed { get; init; }
        public double Power { get; init; }
        public DateTime Utc { get; set; }
        public bool Equals(WindSpeedPower other)
        {
            return other != null && Math.Abs(WindSpeed - other.WindSpeed) < 0.01 && Math.Abs(Power - other.Power) < 0.1;
        }
        
        public override int GetHashCode()
        {
            var hashWindSpeed = WindSpeed.GetHashCode();
            var hashPower = Power.GetHashCode();

            return hashWindSpeed ^ hashPower;
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as WindSpeedPower);
        }
    }
}
