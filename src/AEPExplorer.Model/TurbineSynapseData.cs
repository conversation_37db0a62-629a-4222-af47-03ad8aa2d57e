namespace AEPExplorer.Model;

public class TurbineSynapseData
{
    public Guid Id { get; set; }
    public int MasterDataId { get; set; }
    public string SiteName { get; set; }
    public int ProjectParkId { get; set; }
    public string TurbineName { get; set; }
    public string TurbineModel { get; set; }
    public string TurbinePlatform { get; set; }
    public string TurbineOem { get; set; }
    public string CountryName { get; set; }
    public string RegionName { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public int Altitude { get; set; }
    public double NominalPower { get; set; }
    public string CustomerName { get; set; }
    public string LocationTypeName { get; set; }
    public DateTime? TurbineStartUpDate { get; set; }
    public DateTime? WarrantyStartDate { get; set; }
    public string IsPrototype { get; set; }
    public string TurbineType { get; set; }
}

