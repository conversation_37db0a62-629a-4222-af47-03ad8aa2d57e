using AEPExplorer.Model.Enums;
using Nest;

namespace AEPExplorer.Model.Elasticsearch;

public class PowerBoostImportData
{
    [Date] public DateTime Utc { get; init; }
    [Keyword] public Guid? CustomerId { get; init; }
    [Keyword] public Guid RegionId { get; init; }
    [Keyword] public Guid CountryId { get; init; }
    [Keyword] public Guid SiteId { get; init; }
    [Keyword] public Guid TurbineId { get; init; }
    [Keyword] public Guid TurbineOemId { get; init; }
    [Keyword] public Guid TurbinePlatformId { get; init; }
    [Keyword] public Guid TurbineModelId { get; init; }
    [Keyword] public LocationTypeNameEnum LocationTypeName { get; init; }
    [Number] public double BoostCriteriaOK { get; init; }
    [Number] public double PowerFactor_CNF { get; init; }
    [Number] public double CoolDown_CNF { get; init; }
    [Number] public double Turbulence_CNF { get; init; }
    [Number] public double ACS_CNF { get; init; }
    [Number] public double SensorError_CNF { get; init; }
    [Number] public double RSA_CNF { get; init; }
    [Number] public double RunningRestricted_CNF { get; init; }
    [Number] public double Pitch_CNF { get; init; }
    [Number] public double Disabled_CNF { get; init; }
    [Number] public double SafeMode_CNF { get; init; }
    [Number] public double AmbientTemp_CNF { get; init; }
    [Number] public double GridVoltage_CNF { get; init; }
    [Number] public double CoolDown_CF { get; init; }
    [Number] public double Turbulence_CF { get; init; }
    [Number] public double ACS_CF { get; init; }
    [Number] public double SensorError_CF { get; init; }
    [Number] public double RSA_CF { get; init; }
    [Number] public double NotCurtailed_CNF { get; init; }
    [Number] public double Pitch_CF { get; init; }
    [Number] public double Disabled_CF { get; init; }
    [Number] public double SafeMode_CF { get; init; }
    [Number] public double AmbientTemp_CF { get; init; }
    [Number] public double GridVoltage_CF { get; init; }
    [Number] public double PowerFactor_CF { get; init; }
    [Number] public double? BoostExpMin { get; init; }
    [Number] public double? BoostExpMax { get; init; }
    [Number] public double? BoostExpAvg { get; init; }
    [Number] public double BoostEnergyEst { get; init; }
    [Number] public double NotCurtailed_CF { get; init; }
    [Number] public double RunningRestricted_CF { get; init; }
    [Number] public double BoostExpectedCriteriaOK { get; init; }
    [Number] public double BoostCoverage { get; init; }
    [Number] public double BoostEnergyCnt { get; init; }
    [Number] public double BoostExpected { get; init; }
    [Number] public double BoostAct { get; init; }
    [Number] public double BoostAva { get; init; }
    [Keyword] public AggregationPeriodEnum AggregationPeriod { get; init; }
}