using AEPExplorer.Model.Enums;
using Nest;

namespace AEPExplorer.Model.Elasticsearch;

public class AepImportData
{
    [Date] public DateTime Utc { get; init; }
    [Keyword] public Guid? CustomerId { get; init; }
    [Keyword] public Guid RegionId { get; init; }
    [Keyword] public Guid CountryId { get; init; }
    [Keyword] public Guid SiteId { get; init; }
    [Keyword] public Guid TurbineId { get; init; }
    [Keyword] public Guid TurbineOemId { get; init; }
    [Keyword] public Guid TurbinePlatformId { get; init; }
    [Keyword] public Guid TurbineModelId { get; init; }
    [Keyword] public Guid SubcategoryId { get; init; }
    [Number] public double ActualEnergy { get; init; }
    [Number] public double EnergyPotential { get; init; }
    [Number] public double DurationInHours { get; init; }
    [Number] public double DataCoverage { get; init; }
    [Keyword] public AggregationPeriodEnum AggregationPeriod { get; init; }
    [Keyword] public CalculationMethodEnum CalculationMethod { get; init; }
    [Keyword] public LossesTypeEnum LossesType { get; init; }
    [Keyword] public LocationTypeNameEnum LocationTypeName { get; init; }
}

