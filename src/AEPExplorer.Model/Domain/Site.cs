namespace AEPExplorer.Model.Domain;

public class Site : BaseEntity
{
    public int MasterDataId { get; set; }
    public Guid CountryId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double NominalPower { get; set; }
    public bool PowerBoost { get; set; }
    public bool IsPresent { get; set; }
    public virtual Country Country { get; set; }
    public virtual ICollection<Turbine> Turbines { get; set; }
}