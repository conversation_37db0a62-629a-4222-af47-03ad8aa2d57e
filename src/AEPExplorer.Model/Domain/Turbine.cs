namespace AEPExplorer.Model.Domain;

using AEPExplorer.Model.Enums;

public class Turbine : BaseEntity
{
    public int MasterDataId { get; set; }
    public Guid ModelId { get; set; }
    public Guid SiteId { get; set; }
    public Guid? CustomerId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public int Altitude { get; set; }
    public double NominalPower { get; set; }
    public bool PowerBoost { get; set; }
    public bool IsPresent { get; set; }
    public LocationTypeNameEnum LocationTypeName { get; set; }
    public DateTime? TurbineStartUpDate { get; set; }
    public DateTime? WarrantyStartDate { get; set; }
    public string IsPrototype { get; set; }
    public string TurbineType { get; set; }
    public virtual TurbineModel TurbineModel { get; set; }
    public virtual Site Site { get; set; }
    public virtual Customer Customer { get; set; }
    public virtual ICollection<Tpc> Tpcs { get; set; }
}