namespace AEPExplorer.Model.Domain;

public class TurbineStatus
{
    public Guid Id { get; set; }
    public string TurbineOem { get; set; }
    public string ProjectParkName { get; set; }
    public string ScadaParkName { get; set; }
    public string ProjectParkId { get; set; }
    public string TurbineName { get; set; }
    public int TurbineId { get; set; }
    public string TurbinePlatform { get; set; }
    public string TurbineModel { get; set; }
    public string LocationTypeName { get; set; }
    public string CountryName { get; set; }
    public string RegionName { get; set; }
    public string RegionShortName { get; set; }
    public DateTime TurbineStartUpDate { get; set; }
    public bool IsTurbinePresent { get; set; }
    public string TurbineMissingInfo { get; set; }
}