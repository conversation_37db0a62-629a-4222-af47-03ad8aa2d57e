using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace AEPExplorer.Model.Domain;

[Index(nameof(UserId), IsUnique = false)]
public class FilterPreset : BaseEntity
{
    [Required] public Guid UserId { get; set; }
    public string Query { get; set; }
    public bool Favorite { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}