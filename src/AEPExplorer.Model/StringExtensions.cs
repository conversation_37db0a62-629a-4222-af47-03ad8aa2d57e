using System.Text;

namespace AEPExplorer.Model;

//TODO
//find correct place for these methods
public static class StringExtensions
{
    public static string ToLowerFirstChar(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return char.ToLower(input[0]) + input.Substring(1);
    }

    public static string ToUpperFirstChar(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return char.ToUpper(input[0]) + input.Substring(1);
    }

    public static string ToSeparateWordsByCapitalLetter(this string input)
    {
        StringBuilder builder = new StringBuilder();
        foreach (char c in input)
        {
            if (char.IsUpper(c) && builder.Length > 0) builder.Append(' ');
            builder.Append(c);
        }

        return builder.ToString().ToUpperFirstChar();
    }
}