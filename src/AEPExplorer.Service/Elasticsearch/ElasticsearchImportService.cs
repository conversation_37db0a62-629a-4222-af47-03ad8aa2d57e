using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Elasticsearch;

public static class ElasticsearchImportService
{
    public static bool CreateIndex<T>(IElasticClient client, string indexName) where T : class
    {
        var response = client.Indices.Exists(indexName);
        if (response.Exists)
        {
            return true;
        }

        var createIndexResponse = client.Indices.Create(indexName, i => i
            .Map<T>(x => x.AutoMap())
            .Settings(s => s.Setting(UpdatableIndexSettings.MaxResultWindow, ElasticsearchConstants.BUCKET_SIZE)));

        return createIndexResponse.ApiCall.Success;
    }

    public static async Task<bool> DeleteIndex(IElasticClient client, string indexName)
    {
        var response = await client.Indices.ExistsAsync(indexName);
        if (response.Exists)
        {
            await client.Indices.DeleteAsync(indexName);
        }

        return true;
    }

    public static async Task<bool> DeleteDataRaw(IElasticClient client, DateTime dateFrom, DateTime dateTo, Guid turbineId)
    {
        var filter = new List<Func<QueryContainerDescriptor<AepReadData>, QueryContainer>>
        {
            fq => fq.Terms(t => t.Field(row => row.TurbineId).Terms(turbineId)),
            fq => fq.DateRange(t => t.Field(row => row.Utc)
                .GreaterThanOrEquals(dateFrom)
                .LessThanOrEquals(dateTo))
        };

        var descriptor = new QueryContainerDescriptor<AepReadData>();
        var queryContainer = descriptor.Bool(bq => bq.Filter(filter));
        var response = await client.DeleteByQueryAsync<AepReadData>(x => x
            .Query(_ => queryContainer)
        );

        if (response.IsValid)
        {
            return true;
        }

        throw new ElasticsearchException(response.DebugInformation);
    }

    public static bool BulkInsert<T>(IElasticClient elasticClient, IEnumerable<T> entities, string indexName)
        where T : class
    {
        var response = elasticClient.Ping();

        if (!response.ApiCall.Success)
        {
            return false;
        }

        var result = elasticClient.Bulk(b => b.Index(indexName).IndexMany(entities));
        return result.ApiCall.Success;
    }

    public static async Task<bool> BulkInsertAsync<T>(IElasticClient elasticClient, IEnumerable<T> entities, string indexName)
        where T : class
    {
        var response = await elasticClient.PingAsync();

        if (!response.ApiCall.Success)
        {
            return false;
        }

        var result = await elasticClient.BulkAsync(b => b
            .Index(indexName)
            .IndexMany(entities));
        
        return result.ApiCall.Success;
    }

    public static async Task<List<string>> GetDistinctTurbinesForImportAsync(IElasticClient elasticClient, Query query)
    {
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_NEXT_VERSION)
            .Size(0)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        bf => bf
                            .Terms(t => t.Field(row => row.CalculationMethod).Terms(query.CalculationMethod)),
                        bf => bf
                            .Terms(t => t.Field(row => row.SubcategoryId).Terms(query.Category)),
                        bf => bf
                            .DateRange(t => t.Field(row => row.Utc)
                                .GreaterThanOrEquals(query.DateFrom)
                                .LessThanOrEquals(query.DateTo))
                    )
                )
            )
            .Aggregations(a => a
                .Terms("distinct_values", t => t
                    .Field(dv => dv.TurbineId)
                    .Size(ElasticsearchConstants.BUCKET_SIZE)
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var distinctValues = response.Aggregations
            .Terms("distinct_values")
            .Buckets
            .Select(x => x.Key)
            .OrderBy(x => x)
            .ToList();

        return distinctValues;
    }

    public static async Task<List<string>> GetDistinctTurbinesForImportAlarmsAsync(IElasticClient elasticClient, Query query)
    {
        var response = await elasticClient.SearchAsync<AlarmsImportData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_ALARMS_NEXT_VERSION)
            .Size(0)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        bf => bf
                            .Terms(t => t.Field(row => row.CalculationMethod).Terms(query.CalculationMethod)),
                        bf => bf
                            .DateRange(t => t.Field(row => row.Utc)
                                .GreaterThanOrEquals(query.DateFrom)
                                .LessThanOrEquals(query.DateTo))
                    )
                )
            )
            .Aggregations(a => a
                .Terms("distinct_values", t => t
                    .Field(f => f.TurbineId)
                    .Size(ElasticsearchConstants.BUCKET_SIZE)
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var distinctValues = response.Aggregations
            .Terms("distinct_values")
            .Buckets
            .Select(x => x.Key)
            .OrderBy(x => x)
            .ToList();

        return distinctValues;
    }

    public static async Task<List<string>> GetDistinctTurbinesForImportPowerBoostAsync(IElasticClient elasticClient, Query query)
    {
        var response = await elasticClient.SearchAsync<PowerBoostImportData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_POWER_BOOST_NEXT_VERSION)
            .Size(0)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        bf => bf
                            .DateRange(t => t.Field(row => row.Utc)
                                .GreaterThanOrEquals(query.DateFrom)
                                .LessThanOrEquals(query.DateTo))
                    )
                )
            )
            .Aggregations(a => a
                .Terms("distinct_values", t => t
                    .Field(f => f.TurbineId)
                    .Size(ElasticsearchConstants.BUCKET_SIZE)
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var distinctValues = response.Aggregations
            .Terms("distinct_values")
            .Buckets
            .Select(x => x.Key)
            .OrderBy(x => x)
            .ToList();

        return distinctValues;
    }
}