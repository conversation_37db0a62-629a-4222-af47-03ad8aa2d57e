using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Elasticsearch;

public static class ElasticsearchGeneralService
{
    public static bool IsImportEnabled { get; set; }

    private static async Task<List<Func<QueryContainerDescriptor<AepReadData>, QueryContainer>>> GetFilter(
        IElasticClient elasticClient,
        Query query,
        LossesTypeEnum lossesType,
        AggregationPeriodEnum aggregationPeriodEnum)
    {
        if (query.DateFrom == null || query.DateTo == null)
        {
            var defaultDateRange = await GetDateRange(elasticClient);
            query.DateFrom = defaultDateRange.From;
            query.DateTo = defaultDateRange.To;
        }

        var lossesTypes = query.FilteredCategories && lossesType != LossesTypeEnum.IceEstimateTopic
            ? [LossesTypeEnum.Detailed, LossesTypeEnum.AggregatedLosses]
            : new List<LossesTypeEnum> { lossesType };

        var filter = new List<Func<QueryContainerDescriptor<AepReadData>, QueryContainer>>
        {
            fq => fq.Terms(t => t.Field(row => row.CustomerId).Terms(query.Customer)),
            fq => fq.Terms(t => t.Field(row => row.RegionId).Terms(query.Region)),
            fq => fq.Terms(t => t.Field(row => row.CountryId).Terms(query.Country)),
            fq => fq.Terms(t => t.Field(row => row.SiteId).Terms(query.Site)),
            fq => fq.Terms(t => t.Field(row => row.TurbineId).Terms(query.TurbineId)),
            fq => fq.Terms(t => t.Field(row => row.TurbineModelId).Terms(query.Model)),
            fq => fq.Terms(t => t.Field(p => p.TurbinePlatformId).Terms(query.Platform?.Where(x => !CategoryHelper.OEM_LIST.Contains(x.ToString())).ToList()))
                  || fq.Terms(t => t.Field(p => p.TurbineOemId).Terms(query.Platform?.Where(x => CategoryHelper.OEM_LIST.Contains(x.ToString())).ToList())),
            fq => fq.DateRange(t => t.Field(row => row.Utc).GreaterThanOrEquals(query.DateFrom).LessThanOrEquals(query.DateTo)),
            fq => fq.Terms(t => t.Field(row => row.LossesType).Terms(lossesTypes)),
            fq => fq.Terms(t => t.Field(row => row.AggregationPeriod).Terms(aggregationPeriodEnum)),
            fq => fq.Terms(t => t.Field(row => row.SubcategoryId).Terms(query.Category)),
            fq => fq.Terms(t => t.Field(row => row.CalculationMethod).Terms(query.CalculationMethod)),
            fq => fq.Terms(t=>t.Field(row => row.LocationTypeName).Terms(query.LocationTypeName))
        };
        return filter;
    }

    private static async Task<List<Func<QueryContainerDescriptor<AlarmsReadData>, QueryContainer>>> GetAlarmFilter(
        IElasticClient elasticClient,
        AlarmQuery query,
        AggregationPeriodEnum aggregationPeriodEnum)
    {
        if (query.DateFrom == null || query.DateTo == null)
        {
            var defaultDateRange = await GetDateRange(elasticClient);
            query.DateFrom = defaultDateRange.From;
            query.DateTo = defaultDateRange.To;
        }

        var filter = new List<Func<QueryContainerDescriptor<AlarmsReadData>, QueryContainer>>
        {
            fq => fq.Terms(t => t.Field(row => row.CustomerId).Terms(query.Customer)),
            fq => fq.Terms(t => t.Field(row => row.RegionId).Terms(query.Region)),
            fq => fq.Terms(t => t.Field(row => row.CountryId).Terms(query.Country)),
            fq => fq.Terms(t => t.Field(row => row.SiteId).Terms(query.Site)),
            fq => fq.Terms(t => t.Field(row => row.TurbineId).Terms(query.TurbineId)),
            fq => fq.Terms(t => t.Field(row => row.TurbineModelId).Terms(query.Model)),
            fq => fq
                .Terms(t => t.Field(p => p.TurbinePlatformId)
                    .Terms(query.Platform?.Where(x => !CategoryHelper.OEM_LIST.Contains(x.ToString())).ToList())) || fq
                .Terms(t => t.Field(p => p.TurbineOemId)
                    .Terms(query.Platform?.Where(x => CategoryHelper.OEM_LIST.Contains(x.ToString())).ToList())),
            fq => fq.DateRange(t => t.Field(row => row.Utc)
                .GreaterThanOrEquals(query.DateFrom)
                .LessThanOrEquals(query.DateTo)),
            fq => fq.Terms(t => t.Field(row => row.AggregationPeriod).Terms(aggregationPeriodEnum)),
            fq => fq.Wildcard(t => t.Field(row => row.FunctionalSystem).Value($"*{query.FunctionalSystem}*")),
            fq => fq.Wildcard(t => t.Field(row => row.Alarm).Value($"*{query.Alarm}*")),
            fq => fq.Terms(t => t.Field(row => row.CalculationMethod).Terms(query.CalculationMethod)),
            fq => fq.Terms(t=>t.Field(row => row.LocationTypeName).Terms(query.LocationTypeName))
        };
        return filter;
    }

    private static async Task<List<Func<QueryContainerDescriptor<PowerBoostReadData>, QueryContainer>>> GetPowerBoostFilter(
        IElasticClient elasticClient,
        Query query,
        AggregationPeriodEnum aggregationPeriodEnum)
    {
        if (query.DateFrom == null || query.DateTo == null)
        {
            var defaultDateRange = await GetDateRange(elasticClient);
            query.DateFrom = defaultDateRange.From;
            query.DateTo = defaultDateRange.To;
        }

        var filter = new List<Func<QueryContainerDescriptor<PowerBoostReadData>, QueryContainer>>
        {
            fq => fq.Terms(t => t.Field(row => row.CustomerId).Terms(query.Customer)),
            fq => fq.Terms(t => t.Field(row => row.RegionId).Terms(query.Region)),
            fq => fq.Terms(t => t.Field(row => row.CountryId).Terms(query.Country)),
            fq => fq.Terms(t => t.Field(row => row.SiteId).Terms(query.Site)),
            fq => fq.Terms(t => t.Field(row => row.TurbineId).Terms(query.TurbineId)),
            fq => fq.Terms(t => t.Field(row => row.TurbineModelId).Terms(query.Model)),
            fq => fq.Terms(t => t.Field(p => p.TurbinePlatformId).Terms(query.Platform?.Where(x => !CategoryHelper.OEM_LIST.Contains(x.ToString())).ToList()))
                  || fq.Terms(t => t.Field(p => p.TurbineOemId).Terms(query.Platform?.Where(x => CategoryHelper.OEM_LIST.Contains(x.ToString())).ToList())),
            fq => fq.DateRange(t => t.Field(row => row.Utc).GreaterThanOrEquals(query.DateFrom).LessThanOrEquals(query.DateTo)),
            fq => fq.Terms(t => t.Field(row => row.AggregationPeriod).Terms(aggregationPeriodEnum)),
            fq => fq.Terms(t=>t.Field(row => row.LocationTypeName).Terms(query.LocationTypeName))
        };
        return filter;
    }

    public static async Task<QueryContainer> GetQueryContainer(IElasticClient elasticClient, Query query,
        LossesTypeEnum lossesType, AggregationPeriodEnum aggregationPeriodEnum)
    {
        var filter = await GetFilter(elasticClient, query, lossesType, aggregationPeriodEnum);
        var descriptor = new QueryContainerDescriptor<AepReadData>();
        return descriptor.Bool(bq => bq.Filter(filter));
    }

    public static async Task<QueryContainer> GetAlarmsQueryContainer(IElasticClient elasticClient, AlarmQuery query,
        LossesTypeEnum lossesType, AggregationPeriodEnum aggregationPeriodEnum)
    {
        var filter = await GetAlarmFilter(elasticClient, query, aggregationPeriodEnum);
        var descriptor = new QueryContainerDescriptor<AlarmsReadData>();
        return descriptor.Bool(bq => bq.Filter(filter));
    }

    public static async Task<QueryContainer> GetPowerBoostQueryContainer(IElasticClient elasticClient, Query query,
        AggregationPeriodEnum aggregationPeriodEnum)
    {
        var filter = await GetPowerBoostFilter(elasticClient, query, aggregationPeriodEnum);
        var descriptor = new QueryContainerDescriptor<PowerBoostReadData>();
        return descriptor.Bool(bq => bq.Filter(filter));
    }

    public static async Task<DateRangeModel> GetDateRange(IElasticClient elasticClient)
    {
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Aggregations(a => a
                .Max("commits_max", t => t.Field(f => f.Utc))
                .Min("commits_min", t => t.Field(f => f.Utc))
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var min = DateTime.Parse(response.Aggregations.Min("commits_min").ValueAsString).ToUniversalTime();
        var max = DateTime.Parse(response.Aggregations.Max("commits_max").ValueAsString).ToUniversalTime();

        return new DateRangeModel
        {
            From = min > max.AddYears(-1).AddDays(1) ? min : max.AddYears(-1).AddDays(1),
            To = max,
            Min = min
        };
    }

    public static async Task<DateRangeModel> GetPowerBoostDateRange(IElasticClient elasticClient)
    {
        var response = await elasticClient.SearchAsync<PowerBoostReadData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_POWER_BOOST)
            .Size(0)
            .Aggregations(a => a
                .Max("commits_max", t => t.Field(f => f.Utc))
                .Min("commits_min", t => t.Field(f => f.Utc))
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var min = DateTime.Parse(response.Aggregations.Min("commits_min").ValueAsString).ToUniversalTime();
        var max = DateTime.Parse(response.Aggregations.Max("commits_max").ValueAsString).ToUniversalTime();

        return new DateRangeModel
        {
            From = min > max.AddYears(-1).AddDays(1) ? min : max.AddYears(-1).AddDays(1),
            To = max,
            Min = min
        };
    }

    public static async Task<List<string>> GetDistinctValuesAsync(IElasticClient elasticClient,
        Expression<Func<AepReadData, object>> distinctByExpression,
        Query query,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await GetQueryContainer(elasticClient, query, lossesType, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .Terms("distinct_values", t => t
                    .Field(distinctByExpression)
                    .Size(ElasticsearchConstants.BUCKET_SIZE)
                )
            ));

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var distinctValues = response.Aggregations
            .Terms("distinct_values")
            .Buckets
            .Select(x => x.Key)
            .OrderBy(x => x)
            .ToList();

        return distinctValues;
    }

    public static async Task<List<Guid>> GetPowerBoostDistinctTurbinesAsync(IElasticClient elasticClient,
        Query query, 
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer =
            await GetPowerBoostQueryContainer(elasticClient, query, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<PowerBoostReadData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_POWER_BOOST)
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .Terms("distinct_values", t => t
                    .Field(f => f.TurbineId)
                    .Size(ElasticsearchConstants.BUCKET_SIZE)
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var distinctValues = response.Aggregations
            .Terms("distinct_values")
            .Buckets
            .Select(x => new Guid(x.Key))
            .OrderBy(x => x)
            .ToList();

        return distinctValues;
    }
}