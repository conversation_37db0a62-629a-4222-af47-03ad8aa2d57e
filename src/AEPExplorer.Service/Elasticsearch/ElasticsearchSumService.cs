using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Elasticsearch;

internal static class ElasticsearchSumService
{
    internal static async Task<Dictionary<string, double>> SumAsync(
        IElasticClient elasticClient,
        Query query,
        Dictionary<string, IAggregationContainer> aggregations,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetQueryContainer(elasticClient, query, lossesType, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(aggregations)
        );
        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        return aggregations.ToDictionary(item => item.Key,
            item => response.Aggregations.Sum(item.Key).Value.Round(decimals));
    }

    internal static async Task<double> SumAsync(
        IElasticClient elasticClient,
        Query query,
        Func<SumAggregationDescriptor<AepReadData>, ISumAggregation> selector,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetQueryContainer(elasticClient, query, lossesType, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .Sum("commits_sum", selector)
            )
        );

        if (response.IsValid)
        {
            var commitsSum = response.Aggregations.Sum("commits_sum");
            var values = (commitsSum.Value ?? 0).Round(decimals: decimals);
            return values;
        }
        else
        {
            throw new ElasticsearchException(response.DebugInformation);
        }
    }

    internal static async Task<Dictionary<Guid, Dictionary<string, double>>> SumByGroupAsync(
        IElasticClient elasticClient, Query query,
        string groupProperty,
        Dictionary<string, IAggregationContainer> aggregations,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetQueryContainer(elasticClient, query, lossesType, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .Terms("group_by_property", t => t
                    .Field(groupProperty.ToLowerFirstChar())
                    .Aggregations(aggregations)
                    .Size(ElasticsearchConstants.BUCKET_SIZE)
                )
            )
        );

        if (response.IsValid)
        {
            var result = new Dictionary<Guid, Dictionary<string, double>>();
            var groupByProperties = response.Aggregations.Terms("group_by_property").Buckets.ToList();

            foreach (var property in groupByProperties)
            {
                var values = new Dictionary<string, double>();
                foreach (var value in property)
                {
                    switch (value.Value)
                    {
                        case ValueAggregate valueAggregate:
                            values.Add(value.Key, (valueAggregate.Value ?? 0).Round(decimals));
                            break;
                        case BucketAggregate bucketAggregate:
                            values.Add(value.Key, bucketAggregate.Items.Count);
                            break;
                    }
                }

                result.Add(new Guid(property.Key), values);
            }

            return result;
        }
        else
        {
            throw new ElasticsearchException(response.DebugInformation);
        }
    }

    internal static async Task<Dictionary<string, Dictionary<string, double>>> SumByGroupAlarmsAsync(
        IElasticClient elasticClient, AlarmQuery query,
        string groupProperty,
        Dictionary<string, IAggregationContainer> aggregations,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetAlarmsQueryContainer(elasticClient, query, lossesType, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<AlarmsReadData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_ALARMS)
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .Terms("group_by_property", t => t
                    .Field(groupProperty.ToLowerFirstChar())
                    .Aggregations(aggregations)
                    .Size(ElasticsearchConstants.BUCKET_SIZE)
                )
            )
        );

        if (response.IsValid)
        {
            var result = new Dictionary<string, Dictionary<string, double>>();
            var groupByProperties = response.Aggregations.Terms("group_by_property").Buckets.ToList();

            foreach (var property in groupByProperties)
            {
                var values = new Dictionary<string, double>();
                foreach (var value in property)
                {
                    switch (value.Value)
                    {
                        case ValueAggregate valueAggregate:
                            values.Add(value.Key, (valueAggregate.Value ?? 0).Round(decimals));
                            break;
                        case BucketAggregate bucketAggregate:
                            values.Add(value.Key, bucketAggregate.Items.Count);
                            break;
                    }
                }

                result.Add(property.Key, values);
            }

            return result;
        }
        else
        {
            throw new ElasticsearchException(response.DebugInformation);
        }
    }

    internal static async Task<Dictionary<Guid, Dictionary<string, double>>> PowerBoostSumByGroupAsync(
        IElasticClient elasticClient, Query query,
        string groupProperty,
        Dictionary<string, IAggregationContainer> aggregations,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetPowerBoostQueryContainer(elasticClient, query, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<PowerBoostReadData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_POWER_BOOST)
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .Terms("group_by_property", t => t
                    .Field(groupProperty.ToLowerFirstChar())
                    .Aggregations(aggregations)
                    .Size(ElasticsearchConstants.BUCKET_SIZE)
                )
            )
        );

        if (response.IsValid)
        {
            var result = new Dictionary<Guid, Dictionary<string, double>>();
            var groupByProperties = response.Aggregations.Terms("group_by_property").Buckets.ToList();

            foreach (var property in groupByProperties)
            {
                var values = new Dictionary<string, double>();
                foreach (var value in property)
                {
                    switch (value.Value)
                    {
                        case ValueAggregate valueAggregate:
                            values.Add(value.Key, (valueAggregate.Value ?? 0).Round(decimals));
                            break;
                        case BucketAggregate bucketAggregate:
                            values.Add(value.Key, bucketAggregate.Items.Count);
                            break;
                    }
                }

                result.Add(new Guid(property.Key), values);
            }

            return result;
        }
        else
        {
            throw new ElasticsearchException(response.DebugInformation);
        }
    }

    internal static async Task<Dictionary<Guid, Dictionary<Guid, Dictionary<string, double>>>> SumByGroupAsync(
        IElasticClient elasticClient, Query query,
        string groupProperty,
        string subgroupProperty,
        Dictionary<string, IAggregationContainer> aggregations,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetQueryContainer(elasticClient, query, lossesType, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .Terms("group_by_property", t => t
                    .Field(groupProperty.ToLowerFirstChar())
                    .Aggregations(b => b
                        .Terms("subgroup_by_property", st => st
                            .Field(subgroupProperty.ToLowerFirstChar())
                            .Aggregations(aggregations)
                            .Size(ElasticsearchConstants.BUCKET_SIZE)
                        )
                    ).Size(ElasticsearchConstants.BUCKET_SIZE)
                )
            )
        );

        if (response.IsValid)
        {
            var result = new Dictionary<Guid, Dictionary<Guid, Dictionary<string, double>>>();
            var groupByProperties = response.Aggregations.Terms("group_by_property").Buckets.ToList();

            foreach (var property in groupByProperties)
            {
                var group = new Dictionary<Guid, Dictionary<string, double>>();
                foreach (var subProperties in ((BucketAggregate)property["subgroup_by_property"]).Items.ToList())
                {
                    var values = new Dictionary<string, double>();
                    foreach (var subProperty in (KeyedBucket<object>)subProperties)
                    {
                        switch (subProperty.Value)
                        {
                            case ValueAggregate valueAggregate:
                                values.Add(subProperty.Key, (valueAggregate.Value ?? 0).Round(decimals));
                                break;
                            case BucketAggregate bucketAggregate:
                                values.Add(subProperty.Key, bucketAggregate.Items.Count);
                                break;
                        }
                    }

                    group.Add(new Guid(((KeyedBucket<object>)subProperties).Key.ToString()), values);
                }

                result.Add(new Guid(property.Key), group);
            }

            return result;
        }
        else
        {
            throw new ElasticsearchException(response.DebugInformation);
        }
    }

    internal static async Task<List<NameValueDate>> SumByDateHistogramAsync(
        IElasticClient elasticClient,
        Query query,
        Func<SumAggregationDescriptor<AepReadData>, ISumAggregation> selector,
        DateInterval dateInterval,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetQueryContainer(elasticClient, query, lossesType, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .DateHistogram("group_by_interval", date => date
                    .Field(p => p.Utc)
                    .CalendarInterval(dateInterval)
                    .Format("yyyy-MM-dd")
                    .Aggregations(c => c
                        .Sum("commits_sum", selector)
                    )
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var dateHistogram = response.Aggregations.DateHistogram("group_by_interval");

        var result = dateHistogram.Buckets.Select(x => new NameValueDate
        {
            Name = x.Date.ToDateInterval(dateInterval),
            Value = (((ValueAggregate)x["commits_sum"]).Value ?? 0).Round(decimals),
            Date = x.Date
        }).ToList();

        return result;
    }

    internal static async Task<Dictionary<DateTime, Dictionary<string, double>>> SumByDateHistogramAsync(
        IElasticClient elasticClient, Query query,
        Dictionary<string, IAggregationContainer> aggregations,
        DateInterval dateInterval,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetQueryContainer(elasticClient, query, lossesType, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .DateHistogram("group_by_interval", date => date
                    .Field(p => p.Utc)
                    .CalendarInterval(dateInterval)
                    .Format("yyyy-MM-dd")
                    .Aggregations(aggregations)
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var dateHistogram = response.Aggregations.DateHistogram("group_by_interval");

        return dateHistogram.Buckets.ToDictionary(item => item.Date, item => item.ToDictionary(x => x.Key, x => ((ValueAggregate)x.Value).Value ?? 0.0));
    }

    internal static async Task<Dictionary<DateTime, Dictionary<Guid, Dictionary<string, double>>>> SumByGroupDateHistogramAsync(
        IElasticClient elasticClient,
        Query query,
        string groupProperty,
        Dictionary<string, IAggregationContainer> aggregations,
        DateInterval dateInterval,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriod = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetQueryContainer(elasticClient, query, lossesType, aggregationPeriod);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .DateHistogram("group_by_interval", date => date
                    .Field(p => p.Utc)
                    .CalendarInterval(dateInterval)
                    .Format("yyyy-MM-dd")
                    .Aggregations(b => b
                        .Terms("group_by_property", t => t
                            .Field(groupProperty.ToLowerFirstChar())
                            .Aggregations(aggregations)
                        )
                    )
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var dateHistogram = response.Aggregations.DateHistogram("group_by_interval");

        var result = new Dictionary<DateTime, Dictionary<Guid, Dictionary<string, double>>>();
        foreach (var bucket in dateHistogram.Buckets)
        {
            var categories = new Dictionary<Guid, Dictionary<string, double>>();
            var items = ((BucketAggregate)bucket["group_by_property"]).Items;
            foreach (var item in items)
            {
                var temp = ((KeyedBucket<object>)item);
                var tempValues = temp.ToDictionary(x => x.Key, x => (double)((ValueAggregate)x.Value).Value);
                categories.Add(new Guid(temp.Key.ToString()), tempValues);
            }

            result.Add(bucket.Date, categories);
        }

        return result;
    }

    public static async Task<List<NameValueDate>> CumulateSumByDateHistogramAsync(
        IElasticClient elasticClient,
        Query query,
        string script,
        DateInterval dateInterval = DateInterval.Day,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        LossesTypeEnum lossesType = LossesTypeEnum.Detailed,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer =
            await ElasticsearchGeneralService.GetQueryContainer(elasticClient, query, lossesType, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .DateHistogram("group_by_interval", date => date
                    .Field(p => p.Utc)
                    .CalendarInterval(dateInterval)
                    .Format("yyyy-MM-dd")
                    .Aggregations(b => b
                        .Sum("commits_sum", s => s.Script(script))
                        .CumulativeSum("cumulative_sum", d => d
                            .BucketsPath("commits_sum")
                        )
                    )
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var dateHistogram = response.Aggregations.DateHistogram("group_by_interval");
        var values = dateHistogram.Buckets
            .Select(x => new NameValueDate
            {
                Name = x.Date.ToDateInterval(dateInterval),
                Value = (((ValueAggregate)x["cumulative_sum"]).Value ?? 0).Round(decimals),
                Date = x.Date
            }).ToList();
        return values;
    }

    internal static async Task<Dictionary<DateTime, Dictionary<string, double>>> PowerBoostSumByDateHistogramAsync(
        IElasticClient elasticClient,
        Query query,
        Dictionary<string, IAggregationContainer> aggregations,
        DateInterval dateInterval,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetPowerBoostQueryContainer(elasticClient, query, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<PowerBoostReadData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_POWER_BOOST)
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .DateHistogram("group_by_interval", date => date
                    .Field(p => p.Utc)
                    .CalendarInterval(dateInterval)
                    .Format("yyyy-MM-dd")
                    .Aggregations(aggregations)
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var dateHistogram = response.Aggregations.DateHistogram("group_by_interval");

        return dateHistogram.Buckets.ToDictionary(item => item.Date, item => item.ToDictionary(x => x.Key, x => ((ValueAggregate)x.Value).Value ?? 0.0));
    }

    public static async Task<List<NameValueDate>> PowerBoostCumulateSumByDateHistogramAsync(
        IElasticClient elasticClient,
        Query query,
        Func<SumAggregationDescriptor<PowerBoostReadData>, ISumAggregation> selector,
        DateInterval dateInterval = DateInterval.Day,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer =
            await ElasticsearchGeneralService.GetPowerBoostQueryContainer(elasticClient, query, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<PowerBoostReadData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_POWER_BOOST)
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .DateHistogram("group_by_interval", date => date
                    .Field(p => p.Utc)
                    .CalendarInterval(dateInterval)
                    .Format("yyyy-MM-dd")
                    .Aggregations(b => b
                        .Sum("commits_sum", selector)
                        .CumulativeSum("cumulative_sum", d => d
                            .BucketsPath("commits_sum")
                        )
                    )
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var dateHistogram = response.Aggregations.DateHistogram("group_by_interval");
        var values = dateHistogram.Buckets
            .Select(x => new NameValueDate
            {
                Name = x.Date.ToDateInterval(dateInterval),
                Value = (((ValueAggregate)x["cumulative_sum"]).Value ?? 0).Round(decimals),
                Date = x.Date
            }).ToList();
        return values;
    }
    
    internal static async Task<Dictionary<DateTime, Dictionary<Guid, Dictionary<string, double>>>> PowerBoostSumByGroupDateHistogramAsync(
        IElasticClient elasticClient,
        Query query,
        string groupProperty,
        Dictionary<string, IAggregationContainer> aggregations,
        DateInterval dateInterval,
        AggregationPeriodEnum aggregationPeriod = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetPowerBoostQueryContainer(elasticClient, query, aggregationPeriod);
        var response = await elasticClient.SearchAsync<AepReadData>(x => x
            .Size(0)
            .Index(ElasticsearchConstants.INDEX_NAME_POWER_BOOST)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .DateHistogram("group_by_interval", date => date
                    .Field(p => p.Utc)
                    .CalendarInterval(dateInterval)
                    .Format("yyyy-MM-dd")
                    .Aggregations(b => b
                        .Terms("group_by_property", t => t
                            .Field(groupProperty.ToLowerFirstChar())
                            .Aggregations(aggregations)
                        )
                    )
                )
            )
        );

        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        var dateHistogram = response.Aggregations.DateHistogram("group_by_interval");

        var result = new Dictionary<DateTime, Dictionary<Guid, Dictionary<string, double>>>();
        foreach (var bucket in dateHistogram.Buckets)
        {
            var categories = new Dictionary<Guid, Dictionary<string, double>>();
            var items = ((BucketAggregate)bucket["group_by_property"]).Items;
            foreach (var item in items)
            {
                var temp = ((KeyedBucket<object>)item);
                var tempValues = temp.ToDictionary(x => x.Key, x => (double)((ValueAggregate)x.Value).Value);
                categories.Add(new Guid(temp.Key.ToString()), tempValues);
            }

            result.Add(bucket.Date, categories);
        }

        return result;
    }
    
    internal static async Task<Dictionary<string, double>> PowerBoostSumAsync(
        IElasticClient elasticClient,
        Query query,
        Dictionary<string, IAggregationContainer> aggregations,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetPowerBoostQueryContainer(elasticClient, query, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<PowerBoostReadData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_POWER_BOOST)
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(aggregations)
        );
        if (!response.IsValid)
        {
            throw new ElasticsearchException(response.DebugInformation);
        }

        return aggregations.ToDictionary(item => item.Key,
            item => response.Aggregations.Sum(item.Key).Value.Round(decimals));
    }
    
    internal static async Task<double> PowerBoostSumAsync(
        IElasticClient elasticClient,
        Query query,
        Func<SumAggregationDescriptor<PowerBoostReadData>, ISumAggregation> selector,
        int decimals = ElasticsearchConstants.DECIMAL_PLACES,
        AggregationPeriodEnum aggregationPeriodEnum = AggregationPeriodEnum.Day)
    {
        var queryContainer = await ElasticsearchGeneralService.GetPowerBoostQueryContainer(elasticClient, query, aggregationPeriodEnum);
        var response = await elasticClient.SearchAsync<PowerBoostReadData>(x => x
            .Index(ElasticsearchConstants.INDEX_NAME_POWER_BOOST)
            .Size(0)
            .Query(_ => queryContainer)
            .Aggregations(a => a
                .Sum("commits_sum", selector)
            )
        );

        if (response.IsValid)
        {
            var commitsSum = response.Aggregations.Sum("commits_sum");
            var values = (commitsSum.Value ?? 0).Round(decimals: decimals);
            return values;
        }
        else
        {
            throw new ElasticsearchException(response.DebugInformation);
        }
    }
}