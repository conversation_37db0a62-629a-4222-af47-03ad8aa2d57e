using AEPExplorer.Data.EF;
using AEPExplorer.Model.Domain;

namespace AEPExplorer.Service.Commands;

public class CreatePerformanceCommand : MediatR.IRequest<bool>
{
    public Guid UserId { get; set; }
    public double Limit1 { get; set; }
    public double Limit2 { get; set; }
}

public class CreatePerformanceCommandHandler : IRequestHandler<CreatePerformanceCommand, bool>
{
    private readonly AepExplorerDbContext _dbContext;

    public CreatePerformanceCommandHandler(AepExplorerDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    public async Task<bool> Handle(CreatePerformanceCommand request, CancellationToken cancellationToken)
    {
        var performance = _dbContext.Performance.FirstOrDefault(x => x.UserId == request.UserId);

        if (performance != null)
        {
            performance.Limit1 = request.Limit1;
            performance.Limit2 = request.Limit2;    
            _dbContext.Performance.Update(performance);
        }
        else
        {
            performance = new Performance
            {
                Limit1 = request.Limit1,
                Limit2= request.Limit2,
                UserId = request.UserId
            };
            _dbContext.Performance.Add(performance);
        }

        await _dbContext.SaveChangesAsync(cancellationToken);
        return true;
    }
}