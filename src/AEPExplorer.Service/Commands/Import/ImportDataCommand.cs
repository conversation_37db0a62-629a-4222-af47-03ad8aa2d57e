using AEPExplorer.Data.EF;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;
using Apache.Arrow;
using Apache.Arrow.Ipc;
using Microsoft.Azure.Databricks.Client;
using Microsoft.Azure.Databricks.Client.Models;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;

namespace AEPExplorer.Service.Commands.Import;

public class ImportData : MediatR.IRequest<bool>
{
    public ImportQuery ImportQuery { get; init; }
}

public class ImportDataHandler(IElasticClient elasticClient, IConfiguration configuration, IServiceScopeFactory serviceScopeFactory, DatabricksClient client) : IRequestHandler<ImportData, bool>
{
    public Task<bool> Handle(ImportData request, CancellationToken cancellationToken)
    {
        var response = elasticClient.Ping();
        if (response == null || !response.ApiCall.Success)
        {
            return Task.FromResult(false);
        }

        try
        {
            if (!ElasticsearchImportService.CreateIndex<AepImportData>(elasticClient, ElasticsearchConstants.INDEX_NAME_NEXT_VERSION))
            {
                return Task.FromResult(false);
            }

            _ = Task.Run(() => BC_TryTake(request), cancellationToken);
            return Task.FromResult(true);
        }

        catch (SqlException ex)
        {
            Console.WriteLine(ex.Message);
            return Task.FromResult(false);
        }
    }

    private async Task BC_TryTake(ImportData request)
    {
        var startTime = DateTime.Now;
        Console.WriteLine($"Started at: {startTime.ToShortTimeString()}.");

        try
        {
            var turbines = await GetTurbines();
            Console.WriteLine($"Number of turbines to be imported: {turbines.Count}.");
            var operative = GetOperativeSubcategoriesId(serviceScopeFactory);
            var nonOperative = GetNonOperativeSubcategoriesId(serviceScopeFactory);
            var categoryIds = GetSubcategoryIds(serviceScopeFactory);
            var categories = GetCategories(serviceScopeFactory);

            var operativeSet = new HashSet<Guid>(operative);
            var nonOperativeSet = new HashSet<Guid>(nonOperative);
            var categoryIdSet = new HashSet<Guid>(categoryIds);

            var subcategoryToCategoryMap = new Dictionary<Guid, Guid>();

            foreach (var category in categories)
            {
                var subcategoryIds = category.Subcategories.Select(s => s.Id);

                foreach (var subcategoryId in subcategoryIds)
                {
                    subcategoryToCategoryMap[subcategoryId] = category.Id;
                }
            }

            await GenerateIndexParallel(turbines, categoryIdSet, operativeSet, nonOperativeSet, subcategoryToCategoryMap, request);
            var endTime = DateTime.Now;
            Console.WriteLine($"Finished at: {endTime.ToShortTimeString()}, after {(endTime - startTime).TotalMinutes.Round(2)} minutes");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in BC_TryTake: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");

            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
        }
    }

    private static List<Guid> GetSubcategoryIds(IServiceScopeFactory serviceScopeFactory)
    {
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();

        var categories = dbContext.Subcategories.Select(x => x.Id).ToList();

        return categories;
    }

    private async Task<List<TurbineFlat>> GetTurbines()
    {
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();

        var turbines = await dbContext.Turbines
            .Select(x => new TurbineFlat
            {
                CustomerId = x.CustomerId,
                RegionId = x.Site.Country.RegionId,
                CountryId = x.Site.CountryId,
                SiteId = x.SiteId,
                Id = x.Id,
                TurbineMasterDataId = x.MasterDataId,
                ModelId = x.ModelId,
                PlatformId = x.TurbineModel.TurbinePlatformId,
                OemId = x.TurbineModel.TurbinePlatform.OemId,
                LocationTypeName = x.LocationTypeName
            }).ToListAsync();

        return turbines;
    }

    private static List<Guid> GetOperativeSubcategoriesId(IServiceScopeFactory serviceScopeFactory)
    {
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();
        var operative = dbContext.Categories
            .Where(x => x.Level4.Level3Id == CategoryHelper.LEVEL3_IN_SERVICE_ID
                        || x.Level4.Level3Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
            .SelectMany(x => x.Subcategories.Select(s => s.Id))
            .ToList();
        return operative;
    }

    private static List<Guid> GetNonOperativeSubcategoriesId(IServiceScopeFactory serviceScopeFactory)
    {
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();
        var nonOperative = dbContext.Categories
            .Where(x => x.Level4.Level3Id != CategoryHelper.LEVEL3_IN_SERVICE_ID
                        && x.Level4.Level3Id != CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
            .SelectMany(x => x.Subcategories.Select(s => s.Id))
            .ToList();
        return nonOperative;
    }

    private static List<CategoryStructure> GetCategories(IServiceScopeFactory serviceScopeFactory)
    {
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();
        var categories = dbContext.Categories.Select(x => new CategoryStructure
        {
            Id = x.Id,
            Name = x.Name,
            Subcategories = x.Subcategories.ToList()
        }).ToList();

        return categories;
    }

    private class ArrowDataBatch
    {
        public Date32Array Utc { get; init; }
        public Int32Array TurbineId { get; init; }
        public StringArray SubCatName { get; init; }
        public DoubleArray ActualEnergy { get; init; }
        public DoubleArray EnergyPotential { get; init; }
        public DoubleArray DataCoverage { get; init; }
        public DoubleArray Duration { get; init; }
        public int Length { get; init; }
    }

    private async Task GenerateIndexParallel(List<TurbineFlat> turbinesAll, HashSet<Guid> categoryIdSet, HashSet<Guid> operativeSet, HashSet<Guid> nonOperativeSet,
        Dictionary<Guid, Guid> subcategoryToCategoryMap,
        ImportData request)
    {
        for (var dt = request.ImportQuery.DateFrom; dt <= request.ImportQuery.DateTo; dt = dt.AddDays(1))
        {
            try
            {
                var query = new Query
                {
                    DateFrom = dt,
                    DateTo = dt,
                    CalculationMethod = request.ImportQuery.CalculationMethod,
                    Category = [ConvertExtensions.GuidMd5("Total Losses")]
                };

                Console.WriteLine($"Processing date: {dt:yyyy-MM-dd} [{DateTime.Now.ToShortTimeString()}]");

                var arrowData = await GetDatabricksDataForDate(dt);
                if (arrowData == null || !arrowData.Any())
                {
                    Console.WriteLine($"No data returned for date {dt:yyyy-MM-dd}");
                    continue;
                }

                var importedTurbines = await ElasticsearchImportService.GetDistinctTurbinesForImportAsync(elasticClient, query);
                var importedTurbineSet = new HashSet<string>(importedTurbines);
                var turbinesDaily = turbinesAll.Where(x => !importedTurbineSet.Contains(x.Id.ToString())).ToList();

                Console.WriteLine($"Number of turbines to process for {dt:yyyy-MM-dd}: {turbinesDaily.Count}");
                if (turbinesDaily.Count == 0)
                {
                    continue;
                }

                var turbineByMasterDataId = turbinesDaily.ToDictionary(t => t.TurbineMasterDataId);

                var aepImportData = ProcessArrowData(arrowData, turbineByMasterDataId, categoryIdSet);

                await ProcessTurbinesInParallel(turbinesDaily, aepImportData, operativeSet, nonOperativeSet,
                    subcategoryToCategoryMap);
                
                if (!ElasticsearchGeneralService.IsImportEnabled)
                {
                    Console.WriteLine("Import is stopping.");
                    return;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing date {dt:yyyy-MM-dd}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
        }
    }

    private async Task<List<ArrowDataBatch>> GetDatabricksDataForDate(DateTime dt)
    {
        var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                ?? throw new InvalidOperationException("Databricks Catalog is missing");
        var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                          ?? throw new InvalidOperationException("Databricks WarehouseId is missing");

        const int maxRetries = 10;
        const int retryDelayMs = 5000;

        for (int retryCount = 0; retryCount <= maxRetries; retryCount++)
        {
            try
            {
                if (retryCount > 0)
                {
                    Console.WriteLine($"Retry attempt {retryCount} of {maxRetries} for date {dt:yyyy-MM-dd}");
                    await Task.Delay(retryDelayMs * retryCount);
                }

                var sqlStatement = new SqlStatement
                {
                    WarehouseId = warehouseId,
                    Statement = $"""
                                 SELECT aed.DateUTC as Utc
                                     ,aed.TurbineId as TurbineId
                                     ,aed.SubcatName as SubCatName
                                     ,aed.ActualEnergy as ActualEnergy
                                     ,aed.EnsembleTPCPEP as EnergyPotential
                                     ,aed.EnsembleTPCPEPCoverageTime as DataCoverage
                                     ,aed.EnergyCoverageTime as Duration
                                 FROM aep_agg.aep_energy_dayagg aed
                                 WHERE aed.DateUTC = '{dt:yyyy-MM-dd}';
                                 """,
                    Catalog = databricksCatalog,
                    WaitTimeout = "50s",
                    Format = StatementFormat.ARROW_STREAM,
                    Disposition = SqlStatementDisposition.EXTERNAL_LINKS
                };

                var statement = await client.SQL.StatementExecution.Execute(sqlStatement);

                int statusCheckCount = 0;
                while (statement.Status.State == StatementExecutionState.PENDING ||
                       statement.Status.State == StatementExecutionState.RUNNING)
                {
                    await Task.Delay(1000);
                    statement = await client.SQL.StatementExecution.Get(statement.StatementId);

                    if (statusCheckCount++ % 10 == 0)
                    {
                        Console.WriteLine($"Current status: {statement.Status.State}");
                    }

                    if (statusCheckCount > 120)
                    {
                        throw new TimeoutException("Statement execution timed out after 2 minutes");
                    }
                }

                if (statement.Status.State != StatementExecutionState.SUCCEEDED)
                {
                    if (statement.Status.Error != null &&
                        statement.Status.Error.Message.Contains("Azure storage request is not authorized") &&
                        retryCount < maxRetries)
                    {
                        Console.WriteLine($"Azure storage authorization error, will retry. Details: {statement.Status.Error.Message}");
                        continue;
                    }

                    throw new Exception($"Query failed: {statement.Status.Error}");
                }

                if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
                {
                    return null;
                }

                var result = new List<ArrowDataBatch>();
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (var link in statement.Result.ExternalLinks)
                {
                    var response = await httpClient.GetAsync(link.ExternalLink);
                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"Failed to download data from external link: {response.StatusCode}");
                        continue;
                    }

                    await using var stream = await response.Content.ReadAsStreamAsync();
                    using var reader = new ArrowStreamReader(stream);

                    while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                    {
                        var schema = recordBatch.Schema;

                        var columns = new ArrowDataBatch
                        {
                            Utc = (Date32Array)recordBatch.Column(schema.GetFieldIndex("Utc")),
                            TurbineId = (Int32Array)recordBatch.Column(schema.GetFieldIndex("TurbineId")),
                            SubCatName = (StringArray)recordBatch.Column(schema.GetFieldIndex("SubCatName")),
                            ActualEnergy = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("ActualEnergy")),
                            EnergyPotential = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("EnergyPotential")),
                            DataCoverage = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("DataCoverage")),
                            Duration = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("Duration")),
                            Length = recordBatch.Length
                        };

                        result.Add(columns);
                    }
                }

                return result;
            }
            catch (Exception ex) when (retryCount < maxRetries &&
                                       (ex.Message.Contains("Azure storage request is not authorized") ||
                                        ex.Message.Contains("timeout") ||
                                        ex.Message.Contains("network") ||
                                        ex.Message.Contains("connection")))
            {
                Console.WriteLine($"Retryable error on attempt {retryCount + 1}: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing Arrow data: {ex.Message}");
                return null;
            }
        }

        Console.WriteLine($"Failed to get data after {maxRetries} retry attempts for date {dt:yyyy-MM-dd}");
        return null;
    }

    private List<AepImportData> ProcessArrowData(List<ArrowDataBatch> arrowDataBatches,
        Dictionary<int, TurbineFlat> turbineByMasterDataId, HashSet<Guid> categoryIdSet)
    {
        var result = new List<AepImportData>();

        foreach (var batch in arrowDataBatches)
        {
            for (var i = 0; i < batch.Length; i++)
            {
                var subCatName = batch.SubCatName.GetString(i);
                var categoryIdDb = ConvertExtensions.Md5($"sub{subCatName}");
                var categoryId = new Guid(categoryIdDb ?? string.Empty);

                if (!categoryIdSet.Contains(categoryId))
                {
                    continue;
                }

                var turbineMasterDataId = batch.TurbineId.Values[i];
                if (!turbineByMasterDataId.TryGetValue(turbineMasterDataId, out var turbine))
                {
                    continue;
                }

                var actualEnergy = batch.ActualEnergy.Values[i];
                var energyPotential = batch.EnergyPotential.Values[i];
                var dataCoverage = batch.DataCoverage.Values[i];
                var duration = batch.Duration.Values[i];

                var utc = batch.Utc.GetDateTime(i);
                if (utc == null)
                {
                    continue;
                }

                result.Add(new AepImportData
                {
                    Utc = DateTime.SpecifyKind(utc.Value, DateTimeKind.Utc),
                    TurbineId = turbine.Id,
                    SubcategoryId = categoryId,
                    ActualEnergy = actualEnergy,
                    EnergyPotential = CategoryHelper.RunSubcategories.Contains(categoryId) ? actualEnergy : energyPotential,
                    DataCoverage = dataCoverage / ElasticsearchConstants.SECONDS_IN_ONE_DAY,
                    DurationInHours = duration.Round(0) / ElasticsearchConstants.SECONDS_IN_ONE_HOUR,
                    CalculationMethod = CalculationMethodEnum.EnsembleTPCPEP,
                    AggregationPeriod = AggregationPeriodEnum.Day,
                    CustomerId = turbine.CustomerId,
                    RegionId = turbine.RegionId,
                    CountryId = turbine.CountryId,
                    SiteId = turbine.SiteId,
                    TurbineModelId = turbine.ModelId,
                    TurbinePlatformId = turbine.PlatformId,
                    TurbineOemId = turbine.OemId,
                    LossesType = LossesTypeEnum.Detailed,
                    LocationTypeName = turbine.LocationTypeName
                });

                if (CategoryHelper.RunSubcategories.Contains(categoryId))
                {
                    result.Add(new AepImportData
                    {
                        Utc = DateTime.SpecifyKind(utc.Value, DateTimeKind.Utc),
                        TurbineId = turbine.Id,
                        SubcategoryId = categoryId,
                        ActualEnergy = actualEnergy,
                        EnergyPotential = energyPotential,
                        DataCoverage = dataCoverage / ElasticsearchConstants.SECONDS_IN_ONE_DAY,
                        DurationInHours = duration.Round(0) / ElasticsearchConstants.SECONDS_IN_ONE_HOUR,
                        CalculationMethod = CalculationMethodEnum.EnsembleTPCPEP,
                        AggregationPeriod = AggregationPeriodEnum.Day,
                        CustomerId = turbine.CustomerId,
                        RegionId = turbine.RegionId,
                        CountryId = turbine.CountryId,
                        SiteId = turbine.SiteId,
                        TurbineModelId = turbine.ModelId,
                        TurbinePlatformId = turbine.PlatformId,
                        TurbineOemId = turbine.OemId,
                        LossesType = LossesTypeEnum.RunTopic,
                        LocationTypeName = turbine.LocationTypeName
                    });
                }
            }
        }

        return result;
    }

    private void ProcessTurbine(BlockingCollection<TurbineFlat> turbineQueue, List<AepImportData> aepImportData,
        HashSet<Guid> operativeSet, HashSet<Guid> nonOperativeSet, Dictionary<Guid, Guid> subcategoryToCategoryMap,
        ConcurrentBag<AepImportData> aggregatedData)
    {
        while (turbineQueue.TryTake(out var turbine))
        {
            try
            {
                var turbineData = aepImportData
                    .Where(x => x.LossesType == LossesTypeEnum.Detailed && x.TurbineId == turbine.Id)
                    .ToList();

                if (turbineData.Count == 0)
                    continue;

                var groupedByDate = turbineData.GroupBy(x => x.Utc).ToList();

                foreach (var group in groupedByDate)
                {
                    try
                    {
                        var utcDate = group.Key;
                        var calculationMethod = group.First().CalculationMethod;

                        var totalActualEnergy = group.Sum(s => s.ActualEnergy);
                        var totalEnergyPotential = group.Sum(s => s.EnergyPotential);
                        var totalDataCoverage = group.Sum(s => s.DataCoverage);
                        var totalDurationInHours = group.Sum(s => s.DurationInHours);

                        aggregatedData.Add(new AepImportData
                        {
                            Utc = utcDate,
                            TurbineId = turbine.Id,
                            SubcategoryId = ConvertExtensions.GuidMd5("Total Losses"),
                            ActualEnergy = totalActualEnergy,
                            EnergyPotential = totalEnergyPotential,
                            DataCoverage = totalDataCoverage,
                            DurationInHours = totalDurationInHours,
                            CalculationMethod = calculationMethod,
                            AggregationPeriod = AggregationPeriodEnum.Day,
                            CustomerId = turbine.CustomerId,
                            RegionId = turbine.RegionId,
                            CountryId = turbine.CountryId,
                            SiteId = turbine.SiteId,
                            TurbineModelId = turbine.ModelId,
                            TurbinePlatformId = turbine.PlatformId,
                            TurbineOemId = turbine.OemId,
                            LossesType = LossesTypeEnum.TotalLosses,
                            LocationTypeName = turbine.LocationTypeName
                        });

                        var groupArray = group.ToArray();
                        var categoryAggregations = new Dictionary<Guid, (double ActualEnergy, double EnergyPotential, double DataCoverage, double DurationInHours)>();

                        foreach (var dataPoint in groupArray)
                        {
                            if (!subcategoryToCategoryMap.TryGetValue(dataPoint.SubcategoryId, out var categoryId))
                                continue;

                            if (!categoryAggregations.TryGetValue(categoryId, out var aggregation))
                            {
                                aggregation = (0, 0, 0, 0);
                            }

                            aggregation.ActualEnergy += dataPoint.ActualEnergy;
                            aggregation.EnergyPotential += dataPoint.EnergyPotential;
                            aggregation.DataCoverage += dataPoint.DataCoverage;
                            aggregation.DurationInHours += dataPoint.DurationInHours;

                            categoryAggregations[categoryId] = aggregation;
                        }

                        foreach (var categoryAgg in categoryAggregations)
                        {
                            var categoryId = categoryAgg.Key;
                            var values = categoryAgg.Value;

                            if (values.DurationInHours == 0)
                                continue;

                            aggregatedData.Add(new AepImportData
                            {
                                Utc = utcDate,
                                TurbineId = turbine.Id,
                                SubcategoryId = categoryId,
                                ActualEnergy = values.ActualEnergy,
                                EnergyPotential = values.EnergyPotential,
                                DataCoverage = values.DataCoverage,
                                DurationInHours = values.DurationInHours,
                                CalculationMethod = calculationMethod,
                                AggregationPeriod = AggregationPeriodEnum.Day,
                                CustomerId = turbine.CustomerId,
                                RegionId = turbine.RegionId,
                                CountryId = turbine.CountryId,
                                SiteId = turbine.SiteId,
                                TurbineModelId = turbine.ModelId,
                                TurbinePlatformId = turbine.PlatformId,
                                TurbineOemId = turbine.OemId,
                                LossesType = LossesTypeEnum.AggregatedLosses,
                                LocationTypeName = turbine.LocationTypeName
                            });
                        }

                        var operativeActualEnergy = 0.0;
                        var operativeEnergyPotential = 0.0;
                        var operativeDataCoverage = 0.0;
                        var operativeDurationInHours = 0.0;

                        var nonOperativeActualEnergy = 0.0;
                        var nonOperativeEnergyPotential = 0.0;
                        var nonOperativeDataCoverage = 0.0;
                        var nonOperativeDurationInHours = 0.0;

                        foreach (var dataPoint in groupArray)
                        {
                            if (operativeSet.Contains(dataPoint.SubcategoryId))
                            {
                                operativeActualEnergy += dataPoint.ActualEnergy;
                                operativeEnergyPotential += dataPoint.EnergyPotential;
                                operativeDataCoverage += dataPoint.DataCoverage;
                                operativeDurationInHours += dataPoint.DurationInHours;
                            }
                            else if (nonOperativeSet.Contains(dataPoint.SubcategoryId))
                            {
                                nonOperativeActualEnergy += dataPoint.ActualEnergy;
                                nonOperativeEnergyPotential += dataPoint.EnergyPotential;
                                nonOperativeDataCoverage += dataPoint.DataCoverage;
                                nonOperativeDurationInHours += dataPoint.DurationInHours;
                            }
                        }

                        if (operativeDurationInHours > 0)
                        {
                            aggregatedData.Add(new AepImportData
                            {
                                Utc = utcDate,
                                TurbineId = turbine.Id,
                                SubcategoryId = ConvertExtensions.GuidMd5("operative"),
                                ActualEnergy = operativeActualEnergy,
                                EnergyPotential = operativeEnergyPotential,
                                DataCoverage = operativeDataCoverage,
                                DurationInHours = operativeDurationInHours,
                                CalculationMethod = calculationMethod,
                                AggregationPeriod = AggregationPeriodEnum.Day,
                                CustomerId = turbine.CustomerId,
                                RegionId = turbine.RegionId,
                                CountryId = turbine.CountryId,
                                SiteId = turbine.SiteId,
                                TurbineModelId = turbine.ModelId,
                                TurbinePlatformId = turbine.PlatformId,
                                TurbineOemId = turbine.OemId,
                                LossesType = LossesTypeEnum.AggregatedLosses,
                                LocationTypeName = turbine.LocationTypeName
                            });
                        }

                        if (nonOperativeDurationInHours > 0)
                        {
                            aggregatedData.Add(new AepImportData
                            {
                                Utc = utcDate,
                                TurbineId = turbine.Id,
                                SubcategoryId = ConvertExtensions.GuidMd5("nonOperative"),
                                ActualEnergy = nonOperativeActualEnergy,
                                EnergyPotential = nonOperativeEnergyPotential,
                                DataCoverage = nonOperativeDataCoverage,
                                DurationInHours = nonOperativeDurationInHours,
                                CalculationMethod = calculationMethod,
                                AggregationPeriod = AggregationPeriodEnum.Day,
                                CustomerId = turbine.CustomerId,
                                RegionId = turbine.RegionId,
                                CountryId = turbine.CountryId,
                                SiteId = turbine.SiteId,
                                TurbineModelId = turbine.ModelId,
                                TurbinePlatformId = turbine.PlatformId,
                                TurbineOemId = turbine.OemId,
                                LossesType = LossesTypeEnum.AggregatedLosses,
                                LocationTypeName = turbine.LocationTypeName
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing group for turbine {turbine.Id}, date {group.Key:yyyy-MM-dd}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing turbine {turbine.Id}: {ex.Message}");
            }
        }
    }

    private async Task ProcessTurbinesInParallel(List<TurbineFlat> turbinesDaily, List<AepImportData> aepImportData, HashSet<Guid> operativeSet, HashSet<Guid> nonOperativeSet,
        Dictionary<Guid, Guid> subcategoryToCategoryMap)
    {
        try
        {
            var numberOfThreads = Math.Min(Environment.ProcessorCount, 8);
            Console.WriteLine($"Using {numberOfThreads} threads for parallel processing");

            var turbineQueue = new BlockingCollection<TurbineFlat>();

            foreach (var turbine in turbinesDaily)
            {
                turbineQueue.Add(turbine);
            }

            turbineQueue.CompleteAdding();

            var aggregatedData = new ConcurrentBag<AepImportData>();

            var tasks = new Task[numberOfThreads];
            for (var i = 0; i < numberOfThreads; i++)
            {
                tasks[i] = Task.Run(() => ProcessTurbine(turbineQueue, aepImportData, operativeSet, nonOperativeSet, subcategoryToCategoryMap, aggregatedData));
            }

            await Task.WhenAll(tasks);

            if (!ElasticsearchGeneralService.IsImportEnabled)
            {
                Console.WriteLine("Import is stopping.");
                return;
            }

            try
            {
                Console.WriteLine($"Writing {aepImportData.Count} raw data records to Elasticsearch");
                foreach (var batch in aepImportData.Batch(200))
                {
                    await ElasticsearchImportService.BulkInsertAsync(elasticClient, batch, ElasticsearchConstants.INDEX_NAME_NEXT_VERSION);
                }

                Console.WriteLine($"Writing {aggregatedData.Count} aggregated data records to Elasticsearch");
                foreach (var batch in aggregatedData.Batch(200))
                {
                    await ElasticsearchImportService.BulkInsertAsync(elasticClient, batch, ElasticsearchConstants.INDEX_NAME_NEXT_VERSION);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error writing data to Elasticsearch: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                throw;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ProcessTurbinesInParallel: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");

            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }

            throw;
        }
    }
}