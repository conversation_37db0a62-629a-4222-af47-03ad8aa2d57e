﻿﻿﻿using AEPExplorer.Data.EF;
using AEPExplorer.Model.Domain;
using AEPExplorer.Model.Request;
using AEPExplorer.Service.Elasticsearch;
using Apache.Arrow;
using Apache.Arrow.Ipc;
using Microsoft.Azure.Databricks.Client;
using Microsoft.Azure.Databricks.Client.Models;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace AEPExplorer.Service.Commands.Import;

public class ImportEpcCommand : MediatR.IRequest<bool>
{
    public ImportQuery ImportQuery { get; init; }
}

public class ImportEpcHandler(IConfiguration configuration, IServiceScopeFactory serviceScopeFactory, DatabricksClient databricksClient) : IRequestHandler<ImportEpcCommand, bool>
{
    public Task<bool> Handle(ImportEpcCommand request, CancellationToken cancellationToken)
    {
        try
        {
            Thread backgroundThread = new(() => _ = GenerateEpc(request, cancellationToken));
            backgroundThread.Start();
            return Task.FromResult(true);
        }

        catch (SqlException ex)
        {
            Console.WriteLine(ex.Message);
            return Task.FromResult(false);
        }
    }

    private async Task<bool> GenerateEpc(ImportEpcCommand request, CancellationToken cancellationToken)
    {
        Console.WriteLine($"Started EPC: {DateTime.Now}");

        var epcList = new List<Epc>();
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();
        var from = DateTime.SpecifyKind(request.ImportQuery.DateFrom, DateTimeKind.Utc);
        var to = DateTime.SpecifyKind(request.ImportQuery.DateTo, DateTimeKind.Utc);

        var oldEpc = dbContext.Epc.Where(x => x.From >= from && x.To <= to).ToList();
        dbContext.Epc.RemoveRange(oldEpc);
        await dbContext.SaveChangesAsync(cancellationToken);
        
        var turbines = dbContext.Turbines
            .Select(x => new
            {
                x.Id,
                x.MasterDataId
            }).ToList();

        Console.WriteLine($"Total number of turbines: {turbines.Count}");

        var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                ?? throw new InvalidOperationException("Databricks Catalog is missing");
        var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                          ?? throw new InvalidOperationException("Databricks WarehouseId is missing");

        var sqlStatement = new SqlStatement
        {
            WarehouseId = warehouseId,
            Statement = $"""
                         SELECT
                             IdStation
                             ,EPC_ID
                             ,DefinedStartTimeUTC as start_date
                             ,DefinedEndTimeUTC as end_date
                             ,IsGenerated as succeeded
                         FROM aep_epc.aep_epc_metadata
                         WHERE DefinedStartTimeUTC BETWEEN '{request.ImportQuery.DateFrom:yyyy-MM-dd}' AND '{request.ImportQuery.DateTo:yyyy-MM-dd}';
                         """,
            Catalog = databricksCatalog,
            WaitTimeout = "50s",
            Format = StatementFormat.ARROW_STREAM,
            Disposition = SqlStatementDisposition.EXTERNAL_LINKS
        };

        var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);

        while (statement.Status.State == StatementExecutionState.PENDING ||
               statement.Status.State == StatementExecutionState.RUNNING)
        {
            await Task.Delay(1000);
            statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);
            Console.WriteLine($"Current status: {statement.Status.State}");
        }

        if (statement.Status.State != StatementExecutionState.SUCCEEDED)
        {
            throw new Exception($"Query failed: {statement.Status.Error}");
        }

        if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
        {
            return false;
        }

        var result = new List<ArrowDataBatchEpc>();
        try
        {
            using var httpClient = new HttpClient();
            foreach (var link in statement.Result.ExternalLinks)
            {
                var response = await httpClient.GetAsync(link.ExternalLink);
                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Failed to download data from external link: {response.StatusCode}");
                    continue;
                }

                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new ArrowStreamReader(stream);

                while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                {
                    var schema = recordBatch.Schema;

                    var columns = new ArrowDataBatchEpc
                    {
                        EpcId = (Int32Array)recordBatch.Column(schema.GetFieldIndex("EPC_ID")),
                        IdStation = (Int32Array)recordBatch.Column(schema.GetFieldIndex("IdStation")),
                        StartDate = (TimestampArray)recordBatch.Column(schema.GetFieldIndex("start_date")),
                        EndDate = (TimestampArray)recordBatch.Column(schema.GetFieldIndex("end_date")),
                        Succeeded = (BooleanArray)recordBatch.Column(schema.GetFieldIndex("succeeded")),
                        Length = recordBatch.Length
                    };

                    result.Add(columns);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing Arrow data: {ex.Message}");
            return false;
        }

        foreach (var batch in result)
        {
            for (var i = 0; i < batch.Length; i++)
            {
                var turbineId = turbines.FirstOrDefault(x => x.MasterDataId == batch.IdStation.Values[i])?.Id;
                if (turbineId != null)
                {
                    epcList.Add(new Epc
                    {
                        TurbineGuid = turbineId.Value,
                        EpcId = batch.EpcId.Values[i],
                        From = DateTime.SpecifyKind(batch.StartDate.GetTimestamp(i).Value.DateTime, DateTimeKind.Utc),
                        To = DateTime.SpecifyKind(batch.EndDate.GetTimestamp(i).Value.DateTime, DateTimeKind.Utc),
                        Succeeded = batch.Succeeded.GetValue(i).Value
                    });
                }
            }
        }

        Console.WriteLine($"Total number of EPCs: {epcList.Count}");

        var counter = 0;
        try
        {
            foreach (var epcBatch in epcList.Batch(100))
            {
                try
                {
                    counter += epcBatch.Count();
                    Console.WriteLine($"{counter} of {epcList.Count}");
                    var epcLines = new List<(int, string)>();
                    var epcJoin = string.Join(',', epcBatch.Select(x => x.EpcId));
                    sqlStatement = new SqlStatement
                    {
                        WarehouseId = warehouseId,
                        Statement = $"""
                                     SELECT
                                         EPC_ID
                                         ,WindSpeedBin
                                         ,PowerQ10
                                         ,PowerQ50
                                         ,PowerQ90
                                     FROM {databricksCatalog}.aep_epc.aep_epc
                                     WHERE EPC_ID IN ({epcJoin})
                                     ORDER BY WindSpeedBin
                                     """,
                        Catalog = databricksCatalog,
                        WaitTimeout = "50s",
                        Format = StatementFormat.ARROW_STREAM,
                        Disposition = SqlStatementDisposition.EXTERNAL_LINKS
                    };

                    statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);

                    while (statement.Status.State == StatementExecutionState.PENDING ||
                           statement.Status.State == StatementExecutionState.RUNNING)
                    {
                        await Task.Delay(1000);
                        statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);
                        Console.WriteLine($"Current status: {statement.Status.State}");
                    }

                    if (statement.Status.State != StatementExecutionState.SUCCEEDED)
                    {
                        throw new Exception($"Query failed: {statement.Status.Error}");
                    }

                    if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
                    {
                        continue;
                    }

                    var epcLinesArrowDataBatches = new List<ArrowDataBatchEpcLines>();
                    try
                    {
                        using var httpClient = new HttpClient();
                        foreach (var link in statement.Result.ExternalLinks)
                        {
                            try
                            {
                                var response = await httpClient.GetAsync(link.ExternalLink);
                                if (!response.IsSuccessStatusCode)
                                {
                                    Console.WriteLine($"Failed to download data from external link: {response.StatusCode}");
                                    continue;
                                }

                                await using var stream = await response.Content.ReadAsStreamAsync();
                                using var reader = new ArrowStreamReader(stream);

                                while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                                {
                                    var schema = recordBatch.Schema;

                                    var columns = new ArrowDataBatchEpcLines()
                                    {
                                        EpcId = (Int32Array)recordBatch.Column(schema.GetFieldIndex("EPC_ID")),
                                        WindSpeedBin = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("WindSpeedBin")),
                                        PowerQ10 = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("PowerQ10")),
                                        PowerQ50 = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("PowerQ50")),
                                        PowerQ90 = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("PowerQ90")),
                                        Length = recordBatch.Length
                                    };

                                    epcLinesArrowDataBatches.Add(columns);
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error processing external link: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing Arrow data: {ex.Message}");
                        continue;
                    }

                    foreach (var batch in epcLinesArrowDataBatches)
                    {
                        for (var i = 0; i < batch.Length; i++)
                        {
                            try
                            {
                                var epcId = batch.EpcId.Values[i];
                                double? windSpeed = batch.WindSpeedBin.Values[i];
                                var epcP50 = Helper.GetNullableDoubleValue(batch.PowerQ50, i, 4);
                                var epcP10 = Helper.GetNullableDoubleValue(batch.PowerQ10, i, 4);
                                var epcP90 = Helper.GetNullableDoubleValue(batch.PowerQ90, i, 4);
                                epcLines.Add(new ValueTuple<int, string>(epcId,
                                    $"{{\"windSpeed\": {windSpeed}, \"epcP50\": {(epcP50 == null ? "null" : epcP50)}, \"epcP10\": {(epcP10 == null ? "null" : epcP10)}, \"epcP90\": {(epcP90 == null ? "null" : epcP90)}}}"));
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error processing EPC line: {ex.Message}");
                            }
                        }
                    }

                    foreach (var epc in epcBatch)
                    {
                        try
                        {
                            var turbineEpcLines = epcLines.Where(x => x.Item1 == epc.EpcId).Select(x => x.Item2);
                            epc.LineCoordinates = $"[{string.Join(", ", turbineEpcLines)}]";

                            dbContext.Epc.Add(epc);
                            await dbContext.SaveChangesAsync();
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error saving EPC {epc.EpcId}: {ex.Message}");
                        }
                    }

                    if (ElasticsearchGeneralService.IsImportEnabled)
                    {
                        continue;
                    }

                    Console.WriteLine("Import is stopping.");
                    return false;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing EPC batch: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Critical error in EPC processing: {ex.Message}");
            throw;
        }

        Console.WriteLine($"Finished: {DateTime.Now}");
        return true;
    }

    private class ArrowDataBatchEpc
    {
        public Int32Array EpcId { get; init; }
        public Int32Array IdStation { get; init; }
        public TimestampArray StartDate { get; set; }
        public TimestampArray EndDate { get; set; }
        public BooleanArray Succeeded { get; set; }
        public int Length { get; init; }
    }

    private class ArrowDataBatchEpcLines
    {
        public Int32Array EpcId { get; init; }
        public DoubleArray WindSpeedBin { get; init; }
        public DoubleArray PowerQ10 { get; init; }
        public DoubleArray PowerQ50 { get; init; }
        public DoubleArray PowerQ90 { get; init; }
        public int Length { get; init; }
    }
}