using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Domain;
using AEPExplorer.Model.Enums;
using Microsoft.Data.SqlClient;
using Apache.Arrow;
using Apache.Arrow.Ipc;
using Microsoft.Azure.Databricks.Client;
using Microsoft.Azure.Databricks.Client.Models;
using Microsoft.Extensions.Configuration;

namespace AEPExplorer.Service.Commands.Import;

public class ImportAepDataStructureCommand : MediatR.IRequest<bool>
{
    public bool IsOldVersion { get; set; }
}

public class ImportAepDataStructureCommandHandler(IConfiguration configuration, AepExplorerDbContext dbContext, DatabricksClient databricksClient)
    : IRequestHandler<ImportAepDataStructureCommand, bool>
{
    public async Task<bool> Handle(ImportAepDataStructureCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var turbinesOriginalData = await GetTurbinesOriginalData();

            await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(ImportLog)}\";", cancellationToken);
            await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(AlarmDescription)}\" CASCADE;", cancellationToken: cancellationToken);
            await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Subcategory)}\";", cancellationToken: cancellationToken);
            await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Category)}\" CASCADE;", cancellationToken: cancellationToken);
            await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Level4)}\" CASCADE;", cancellationToken: cancellationToken);
            await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Level3)}\" CASCADE;", cancellationToken: cancellationToken);

            await GenerateTurbineTechnologyStructure(turbinesOriginalData);
            await GenerateTurbineLocationStructure(turbinesOriginalData);

            var categoriesOriginalData = await GetCategoriesOriginalData(request.IsOldVersion);
            await GenerateCategoryStructure(categoriesOriginalData);
            var alarmsOriginalData = await GetAlarmsOriginalData();
            await GenerateAlarmDescription(alarmsOriginalData);

            return true;
        }
        catch (SqlException ex)
        {
            Console.WriteLine(ex.Message);
            return false;
        }
    }

    private async Task GenerateCategoryStructure(List<CategorySynapseData> subcategories)
    {
        try
        {
            var level3Losses = subcategories.Select(x => new
            {
                x.Level3Id,
                x.Level3Name
            }).Distinct().ToList();
            level3Losses.ForEach(level3 =>
            {
                var existingLevel3 = dbContext.Level3s.FirstOrDefault(l => l.Name == level3.Level3Name);
                if (existingLevel3 != null)
                {
                    return;
                }

                dbContext.Level3s.Add(new Level3
                {
                    Id = new Guid(level3.Level3Id),
                    Name = level3.Level3Name
                });
            });
            var savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {level3Losses.Count}, {savedItems} added or updated Level 3 items.");

            var level4Losses = subcategories.Select(x => new
            {
                x.Level4Id,
                x.Level4Name,
                x.Level3Id
            }).Distinct().ToList();
            foreach (var level4 in level4Losses)
            {
                var existingLevel4 = dbContext.Level4s.FirstOrDefault(l => l.Name == level4.Level4Name);
                var newLevel4 = new Level4
                {
                    Id = new Guid(level4.Level4Id),
                    Name = level4.Level4Name,
                    Level3Id = new Guid(level4.Level3Id)
                };

                if (existingLevel4 != null)
                {
                    existingLevel4.Level3Id = newLevel4.Level3Id;
                }
                else
                {
                    dbContext.Level4s.Add(newLevel4);
                }
            }

            savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {level4Losses.Count}, {savedItems} added or updated Level 4 items.");

            var categories = subcategories.Select(x => new
            {
                x.CategoryId,
                x.CategoryName,
                x.Level4Id
            }).Distinct().ToList();
            foreach (var category in categories)
            {
                var existingCategory = dbContext.Categories.FirstOrDefault(c => c.Id == new Guid(category.CategoryId));
                var newCategory = new Category
                {
                    Id = new Guid(category.CategoryId),
                    Name = category.CategoryName,
                    Level4Id = new Guid(category.Level4Id)
                };

                if (existingCategory != null)
                {
                    existingCategory.Level4Id = newCategory.Level4Id;
                }
                else
                {
                    dbContext.Categories.Add(newCategory);
                }
            }

            savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {categories.Count}, {savedItems} added or updated categories.");

            foreach (var subcategory in subcategories)
            {
                var existingSubcategory = dbContext.Subcategories.FirstOrDefault(x => x.CategoryId == new Guid(subcategory.CategoryId));
                if (existingSubcategory != null)
                {
                    existingSubcategory.CategoryId = new Guid(subcategory.CategoryId);
                }
                else
                {
                    dbContext.Subcategories.Add(new Subcategory()
                    {
                        Name = subcategory.SubcategoryName,
                        Id = new Guid(subcategory.SubcategoryId),
                        CategoryId = new Guid(subcategory.CategoryId)
                    });
                }
            }

            savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {subcategories.Count}, {savedItems} added or updated subcategories.");

            Console.WriteLine("Finished import of structure data.");
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }
    }

    private async Task<List<CategorySynapseData>> GetCategoriesOriginalData(bool isOldVersion)
    {
        var categories = new List<CategorySynapseData>();
        try
        {
            var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                    ?? throw new InvalidOperationException("Databricks Catalog is missing");
            var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                              ?? throw new InvalidOperationException("Databricks WarehouseId is missing");
            var sqlStatement = new SqlStatement
            {
                WarehouseId = warehouseId,
                Statement = $"""
                             SELECT DISTINCT
                                 trim(SubcatName) as SubcategoryName,
                                 trim(MainCatName) as CategoryName,
                                 trim(Level4) as Level4Name,
                                 trim(Level3) as Level3Name
                             FROM {databricksCatalog}.internal.aep_opex_alarms_info
                             WHERE SubcatName IS NOT NULL AND MainCatName IS NOT NULL AND Level4 IS NOT NULL AND Level3 IS NOT NULL;
                             """,
                Catalog = databricksCatalog,
                WaitTimeout = "50s",
                Format = StatementFormat.ARROW_STREAM,
                Disposition = SqlStatementDisposition.EXTERNAL_LINKS
            };

            var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);

            while (statement.Status.State == StatementExecutionState.PENDING || statement.Status.State == StatementExecutionState.RUNNING)
            {
                await Task.Delay(1000);
                statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);
            }

            if (statement.Status.State != StatementExecutionState.SUCCEEDED)
            {
                throw new Exception($"Query failed: {statement.Status.Error}");
            }

            if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
            {
                return categories;
            }

            using var httpClient = new HttpClient();
            foreach (var link in statement.Result.ExternalLinks)
            {
                var response = await httpClient.GetAsync(link.ExternalLink);
                if (!response.IsSuccessStatusCode)
                {
                    continue;
                }

                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new ArrowStreamReader(stream);

                while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                {
                    var schema = recordBatch.Schema;
                    for (var i = 0; i < recordBatch.Length; i++)
                    {
                        var level3Name = ((StringArray)recordBatch.Column(schema.GetFieldIndex("Level3Name"))).GetString(i);
                        var level4Name = ((StringArray)recordBatch.Column(schema.GetFieldIndex("Level4Name"))).GetString(i);
                        var categoryName = ((StringArray)recordBatch.Column(schema.GetFieldIndex("CategoryName"))).GetString(i);
                        var subcategoryName = ((StringArray)recordBatch.Column(schema.GetFieldIndex("SubcategoryName"))).GetString(i);

                        categories.Add(new CategorySynapseData
                        {
                            Level3Id = ConvertExtensions.Md5($"level3{level3Name}", isOldVersion: isOldVersion),
                            Level3Name = level3Name,
                            Level4Id = ConvertExtensions.Md5($"level4{level4Name}", isOldVersion: isOldVersion),
                            Level4Name = level4Name,
                            CategoryId = ConvertExtensions.Md5($"main{categoryName}", isOldVersion: isOldVersion),
                            CategoryName = categoryName,
                            SubcategoryId = ConvertExtensions.Md5($"sub{subcategoryName}", isOldVersion: isOldVersion),
                            SubcategoryName = subcategoryName
                        });
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }

        return categories;
    }

    private async Task<List<AlarmDescription>> GetAlarmsOriginalData()
    {
        var alarms = new List<AlarmDescription>();
        try
        {
            var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                    ?? throw new InvalidOperationException("Databricks Catalog is missing");
            var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                              ?? throw new InvalidOperationException("Databricks WarehouseId is missing");
            var sqlStatement = new SqlStatement
            {
                WarehouseId = warehouseId,
                Statement = """
                            SELECT
                                Alarm,
                                ControllerOEM,
                                Functional_System as FunctionalSystem,
                                COALESCE(Alarm_Description, 'No description') as Description
                            FROM internal.aep_opex_alarms_info
                            WHERE Alarm IS NOT NULL;
                            """,
                Catalog = databricksCatalog,
                WaitTimeout = "50s",
                Format = StatementFormat.ARROW_STREAM,
                Disposition = SqlStatementDisposition.EXTERNAL_LINKS
            };

            var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);

            while (statement.Status.State == StatementExecutionState.PENDING || statement.Status.State == StatementExecutionState.RUNNING)
            {
                await Task.Delay(1000);
                statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);
            }

            if (statement.Status.State != StatementExecutionState.SUCCEEDED)
            {
                throw new Exception($"Query failed: {statement.Status.Error}");
            }

            if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
            {
                return alarms;
            }

            using var httpClient = new HttpClient();
            foreach (var link in statement.Result.ExternalLinks)
            {
                var response = await httpClient.GetAsync(link.ExternalLink);
                if (!response.IsSuccessStatusCode)
                {
                    continue;
                }

                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new ArrowStreamReader(stream);

                while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                {
                    var schema = recordBatch.Schema;
                    for (var i = 0; i < recordBatch.Length; i++)
                    {
                        var alarm = ((Int64Array)recordBatch.Column(schema.GetFieldIndex("Alarm"))).Values[i];
                        var controllerOem = ((StringArray)recordBatch.Column(schema.GetFieldIndex("ControllerOEM"))).GetString(i);
                        var functionalSystem = ((StringArray)recordBatch.Column(schema.GetFieldIndex("FunctionalSystem"))).GetString(i);
                        var description = ((StringArray)recordBatch.Column(schema.GetFieldIndex("Description"))).GetString(i);

                        alarms.Add(new AlarmDescription
                        {
                            Alarm = $"{alarm} {controllerOem}",
                            FunctionalSystem = functionalSystem,
                            Description = description
                        });
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }

        return alarms;
    }

    private async Task<List<TurbineSynapseData>> GetTurbinesOriginalData()
    {
        var turbines = new List<TurbineSynapseData>();
        try
        {
            var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                    ?? throw new InvalidOperationException("Databricks Catalog is missing");
            var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                              ?? throw new InvalidOperationException("Databricks WarehouseId is missing");
            var sqlStatement = new SqlStatement
            {
                WarehouseId = warehouseId,
                Statement = $"""
                             SELECT TurbineId as MasterDataId,
                                    TurbineModel as TurbineModel,
                                    TurbinePlatform as TurbinePlatform,
                                    TurbineName as TurbineName,
                                    TurbineOEM as TurbineOem,
                                    Latitude as Latitude,
                                    Longitude as Longitude,
                                    ProjectParkname as SiteName,
                                    ProjectParkId as ProjectParkId,
                                    CountryName as CountryName,
                                    RegionName as RegionName,
                                    NominalPower as NominalPower,
                                    CustomerName as CustomerName,
                                    LocationTypeName as LocationTypeName,
                                    TurbineStartUpDate as TurbineStartUpDate,
                                    WarrantyStartDate as WarrantyStartDate,
                                    IsPrototype as IsPrototype,
                                    TurbineType as TurbineType,
                                    Altitude as Altitude
                             FROM {databricksCatalog}.aep_turbine_master_data.aep_turbine_master_data
                             WHERE ProjectParkId IS NOT NULL
                             ORDER BY MasterDataId
                             LIMIT 200000;;
                             """,
                Catalog = databricksCatalog,
                WaitTimeout = "50s",
                Format = StatementFormat.ARROW_STREAM,
                Disposition = SqlStatementDisposition.EXTERNAL_LINKS
            };

            var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);

            while (statement.Status.State == StatementExecutionState.PENDING || statement.Status.State == StatementExecutionState.RUNNING)
            {
                await Task.Delay(1000);
                statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);
            }

            if (statement.Status.State != StatementExecutionState.SUCCEEDED)
            {
                throw new Exception($"Query failed: {statement.Status.Error}");
            }

            if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
            {
                return turbines;
            }

            using var httpClient = new HttpClient();
            foreach (var link in statement.Result.ExternalLinks)
            {
                var response = await httpClient.GetAsync(link.ExternalLink);
                if (!response.IsSuccessStatusCode)
                {
                    continue;
                }

                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new ArrowStreamReader(stream);

                while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                {
                    var schema = recordBatch.Schema;
                    for (var i = 0; i < recordBatch.Length; i++)
                    {
                        turbines.Add(new TurbineSynapseData
                        {
                            Id = Guid.NewGuid(),
                            MasterDataId = ((Int32Array)recordBatch.Column(schema.GetFieldIndex("MasterDataId"))).Values[i],
                            TurbineName = ((StringArray)recordBatch.Column(schema.GetFieldIndex("TurbineName"))).GetString(i),
                            TurbineModel = ((StringArray)recordBatch.Column(schema.GetFieldIndex("TurbineModel"))).GetString(i),
                            TurbinePlatform = ((StringArray)recordBatch.Column(schema.GetFieldIndex("TurbinePlatform"))).GetString(i),
                            TurbineOem = ((StringArray)recordBatch.Column(schema.GetFieldIndex("TurbineOem"))).GetString(i),
                            SiteName = ((StringArray)recordBatch.Column(schema.GetFieldIndex("SiteName"))).GetString(i),
                            ProjectParkId = ((Int32Array)recordBatch.Column(schema.GetFieldIndex("ProjectParkId"))).Values[i],
                            CountryName = ((StringArray)recordBatch.Column(schema.GetFieldIndex("CountryName"))).GetString(i),
                            RegionName = ((StringArray)recordBatch.Column(schema.GetFieldIndex("RegionName"))).GetString(i),
                            Latitude = ((DoubleArray)recordBatch.Column(schema.GetFieldIndex("Latitude"))).Values[i],
                            Longitude = ((DoubleArray)recordBatch.Column(schema.GetFieldIndex("Longitude"))).Values[i],
                            Altitude = ((Int32Array)recordBatch.Column(schema.GetFieldIndex("Altitude"))).Values[i],
                            NominalPower = ((DoubleArray)recordBatch.Column(schema.GetFieldIndex("NominalPower"))).Values[i],
                            CustomerName = ((StringArray)recordBatch.Column(schema.GetFieldIndex("CustomerName"))).GetString(i),
                            LocationTypeName = ((StringArray)recordBatch.Column(schema.GetFieldIndex("LocationTypeName"))).GetString(i),
                            TurbineStartUpDate = GetTurbineStartUpDate(recordBatch, schema, i),
                            WarrantyStartDate = ((Date32Array)recordBatch.Column(schema.GetFieldIndex("WarrantyStartDate"))).IsNull(i) 
                                ? null 
                                : ((Date32Array)recordBatch.Column(schema.GetFieldIndex("WarrantyStartDate"))).GetDateTimeOffset(i)?.UtcDateTime,
                            IsPrototype = ((StringArray)recordBatch.Column(schema.GetFieldIndex("IsPrototype"))).GetString(i),
                            TurbineType = ((StringArray)recordBatch.Column(schema.GetFieldIndex("TurbineType"))).GetString(i)
                        });
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }

        return turbines;
    }

    private async Task GenerateTurbineLocationStructure(List<TurbineSynapseData> turbines)
    {
        try
        {
            var regionNames = turbines.Select(x => x.RegionName).Where(x => x != null).Distinct().ToList();
            regionNames.ForEach(x =>
            {
                var region = dbContext.Regions.FirstOrDefault(r => r.Name == x);
                if (region != null)
                {
                    return;
                }

                var (latitude, longitude) = MapHelper.GetRegionCoordinates(x);
                dbContext.Regions.Add(new Region
                {
                    Name = x ?? "Region Not Specified",
                    Latitude = latitude,
                    Longitude = longitude
                });
            });
            var savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {regionNames.Count}, {savedItems} added or updated regions.");

            var regions = await dbContext.Regions.ToListAsync();
            var countries = await dbContext.Countries.ToListAsync();
            var countryNames = turbines.Select(x => x.CountryName).Where(x => x != null).Distinct().ToList();
            countryNames.ForEach(x =>
            {
                var (latitude, longitude) = MapHelper.GetCountriesCoordinates(x);
                var regionName = turbines.First(r => r.CountryName == x).RegionName;
                var region = regions.FirstOrDefault(c => c.Name == regionName);
                var oldCountry = countries.FirstOrDefault(c => c.Name == x);

                var country = new Country
                {
                    Name = x ?? "Country Not Specified",
                    Latitude = latitude,
                    Longitude = longitude,
                    Region = region
                };

                if (oldCountry != null)
                {
                    oldCountry.Region = country.Region;
                }
                else
                {
                    dbContext.Countries.Add(country);
                }
            });
            savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {countryNames.Count}, {savedItems} added or updated countries.");
            countries = await dbContext.Countries.ToListAsync();
            var sites = await dbContext.Sites.ToListAsync();
            var siteNames = turbines.Select(x => x.SiteName).Where(x => x != null).Distinct().ToList();
            siteNames.ForEach(x =>
            {
                var turbineGeoCoordinates = turbines.Where(t => t.SiteName == x)
                    .Select(c => new GeoCoordinate(c.Latitude, c.Longitude)).ToList();
                var siteGeoCoordinate = MapHelper.GetSingleGeoCoordinate(turbineGeoCoordinates);
                var countryName = turbines.First(r => r.SiteName == x).CountryName;
                var projectParkId = turbines.First(r => r.SiteName == x).ProjectParkId;
                var country = countries.FirstOrDefault(c => c.Name == countryName);
                var oldSite = sites.FirstOrDefault(s => s.Name == x);
                var newSite = new Site
                {
                    Name = x,
                    MasterDataId = Convert.ToInt32(projectParkId),
                    Latitude = siteGeoCoordinate.Latitude,
                    Longitude = siteGeoCoordinate.Longitude,
                    Country = country
                };

                if (oldSite != null)
                {
                    oldSite.Country = newSite.Country;
                    oldSite.Latitude = siteGeoCoordinate.Latitude;
                    oldSite.Longitude = siteGeoCoordinate.Longitude;
                }
                else
                {
                    dbContext.Sites.Add(newSite);
                }
            });
            savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {siteNames.Count}, {savedItems} added or updated sites.");

            var customers = await dbContext.Customers.ToListAsync();
            var customerNames = turbines.Select(x => x.CustomerName).Where(x => x != null).Distinct().ToList();
            customerNames.ForEach(x =>
            {
                var customer = customers.FirstOrDefault(c => c.Name == x);
                if (customer != null)
                {
                    return;
                }

                dbContext.Customers.Add(new Customer
                {
                    Name = x
                });
            });
            savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {customerNames.Count}, {savedItems} added or updated customers.");

            customers = await dbContext.Customers.ToListAsync();
            sites = await dbContext.Sites.ToListAsync();
            var models = await dbContext.Models.ToListAsync();
            var savedTurbines = await dbContext.Turbines.ToListAsync();
            turbines.ForEach(x =>
            {
                var customer = customers.FirstOrDefault(c => c.Name == x.CustomerName);
                var site = sites.FirstOrDefault(c => c.Name == x.SiteName);
                var model = models.FirstOrDefault(c => c.Name == x.TurbineModel);

                if (model == null)
                {
                    Console.WriteLine(x.TurbineModel);
                }

                var existingTurbine = savedTurbines.FirstOrDefault(t => t.MasterDataId == x.MasterDataId);

                var turbine = new Turbine
                {
                    Id = x.Id,
                    Name = $"{x.TurbineName} - {x.SiteName}",
                    MasterDataId = Convert.ToInt32(x.MasterDataId),
                    Latitude = x.Latitude,
                    Longitude = x.Longitude,
                    Site = site,
                    NominalPower = x.NominalPower,
                    TurbineModel = model,
                    Customer = customer,
                    LocationTypeName = Enum.TryParse<LocationTypeNameEnum>(x.LocationTypeName, true, out var locType) ? locType : LocationTypeNameEnum.Onshore,
                    TurbineStartUpDate = x.TurbineStartUpDate,
                    WarrantyStartDate = x.WarrantyStartDate,
                    IsPrototype = x.IsPrototype,
                    TurbineType = x.TurbineType,
                    Altitude = x.Altitude
                };

                if (existingTurbine != null)
                {
                    existingTurbine.Name = turbine.Name;
                    existingTurbine.Latitude = turbine.Latitude;
                    existingTurbine.Longitude = turbine.Longitude;
                    existingTurbine.Site = turbine.Site;
                    existingTurbine.NominalPower = turbine.NominalPower;
                    existingTurbine.TurbineModel = turbine.TurbineModel;
                    existingTurbine.Customer = turbine.Customer;
                    existingTurbine.LocationTypeName = turbine.LocationTypeName;
                    existingTurbine.TurbineStartUpDate = turbine.TurbineStartUpDate;
                    existingTurbine.WarrantyStartDate = turbine.WarrantyStartDate;
                    existingTurbine.IsPrototype = turbine.IsPrototype;
                    existingTurbine.TurbineType = turbine.TurbineType;
                    existingTurbine.Altitude = turbine.Altitude;
                }
                else
                {
                    dbContext.Turbines.Add(turbine);
                }
            });
            savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {turbines.Count}, {savedItems} added or updated turbines.");
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }
    }

    private async Task GenerateTurbineTechnologyStructure(IReadOnlyCollection<TurbineSynapseData> turbinesOriginalData)
    {
        try
        {
            var oemNames = turbinesOriginalData.Select(x => x.TurbineOem).Distinct().ToList();
            oemNames.ForEach(x =>
            {
                var existingOem = dbContext.Oems.FirstOrDefault(o => o.Name == x);
                if (existingOem != null)
                {
                    return;
                }

                dbContext.Oems.Add(new TurbineOem
                {
                    Name = x
                });
            });
            var savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {oemNames.Count}, {savedItems} added or updated oems.");

            var platformNames = turbinesOriginalData.Select(x => x.TurbinePlatform).Distinct().ToList();
            platformNames.ForEach(platformName =>
            {
                var oemName = turbinesOriginalData.First(r => r.TurbinePlatform == platformName).TurbineOem;
                var oem = dbContext.Oems.FirstOrDefault(c => c.Name == oemName);
                var existingPlatform = dbContext.TurbinePlatforms.FirstOrDefault(p => p.Name == platformName);
                var newPlatform = new TurbinePlatform
                {
                    Name = platformName,
                    Oem = oem
                };

                if (existingPlatform != null)
                {
                    existingPlatform.Oem = newPlatform.Oem;
                }
                else
                {
                    dbContext.TurbinePlatforms.Add(newPlatform);
                }
            });
            savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {platformNames.Count}, {savedItems} added or updated platforms.");

            var platforms = await dbContext.TurbinePlatforms.ToListAsync();
            var savedModels = await dbContext.Models.ToListAsync();
            var modelNames = turbinesOriginalData.Select(x => x.TurbineModel).Distinct().ToList();
            modelNames.ForEach(modelName =>
            {
                var platformName = turbinesOriginalData.First(r => r.TurbineModel == modelName).TurbinePlatform;
                var platform = platforms.FirstOrDefault(c => c.Name == platformName);
                var existingModel = savedModels.FirstOrDefault(m => m.Name == modelName);
                var newModel = new TurbineModel
                {
                    Name = modelName,
                    TurbinePlatform = platform
                };

                if (existingModel != null)
                {
                    existingModel.TurbinePlatform = newModel.TurbinePlatform;
                }
                else
                {
                    dbContext.Models.Add(newModel);
                }
            });
            savedItems = await dbContext.SaveChangesAsync();
            Console.WriteLine($"Processed {modelNames.Count}, {savedItems} added or updated models.");
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }
    }

    private async Task GenerateAlarmDescription(IReadOnlyCollection<AlarmDescription> alarmDescriptions)
    {
        try
        {
            await dbContext.AlarmDescriptions.AddRangeAsync(alarmDescriptions);
            Console.WriteLine($"Added {alarmDescriptions.Count} alarm descriptions.");
            await dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }
    }

    private static DateTime? GetTurbineStartUpDate(RecordBatch recordBatch, Schema schema, int i)
    {
        var timestampArray = (TimestampArray)recordBatch.Column(schema.GetFieldIndex("TurbineStartUpDate"));

        if (timestampArray.IsNull(i))
            return null;

        var timestamp = timestampArray.GetTimestamp(i);
        if (timestamp?.DateTime.Date == new DateTime(1900, 1, 1).Date)
            return null;

        return timestamp?.UtcDateTime;
    }
}