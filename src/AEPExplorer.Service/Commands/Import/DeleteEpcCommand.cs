using AEPExplorer.Data.EF;

namespace AEPExplorer.Service.Commands.Import;

public class DeleteEpcCommand : MediatR.IRequest<bool>
{        
}

public class DeleteEpcCommandHandler(AepExplorerDbContext dbContext) : IRequestHandler<DeleteEpcCommand, bool>
{
    public async Task<bool> Handle(DeleteEpcCommand request, CancellationToken cancellationToken)
    {
        await dbContext.Database.ExecuteSqlRawAsync("TRUNCATE \"Epc\";", cancellationToken: cancellationToken);
            
        return true;
    }
}