using Apache.Arrow;

namespace AEPExplorer.Service.Commands.Import;

public static class Helper
{
    public static double GetInt32Value(Int32Array array, int index)
    {
        if (array == null || index >= array.Length)
        {
            return 0;
        }

        return array.Values[index];
    }

    public static double GetDoubleValue(DoubleArray array, int index, int decimals = 2)
    {
        if (array == null || index >= array.Length)
        {
            return 0;
        }

        var value = array.Values[index];
        return double.IsNaN(value) ? 0 : value.Round(decimals);
    }

    public static double? GetNullableDoubleValue(DoubleArray array, int index, int decimals = 2)
    {
        if (array == null || index >= array.Length)
        {
            return null;
        }

        var value = array.GetValue(index);
        return value.HasValue ? value.Round(decimals) : null;
    }
}