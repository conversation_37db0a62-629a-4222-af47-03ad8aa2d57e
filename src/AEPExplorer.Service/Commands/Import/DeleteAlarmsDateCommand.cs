using AEPExplorer.Data.EF;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Commands.Import;

public class DeleteAlarmsDataCommand : MediatR.IRequest<bool>
{        
}

public class DeleteAlarmsDataCommandHandler : IRequestHandler<DeleteAlarmsDataCommand, bool>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public DeleteAlarmsDataCommandHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<bool> Handle(DeleteAlarmsDataCommand request, CancellationToken cancellationToken)
    {
        var response = await _elasticClient.PingAsync(ct: cancellationToken);

        if (!response.ApiCall.Success)
        {
            return false;
        }
            
        await ElasticsearchImportService.DeleteIndex(_elasticClient, ElasticsearchConstants.INDEX_NAME_ALARMS);

        return true;
    }
}