﻿﻿﻿using AEPExplorer.Data.EF;
using AEPExplorer.Model.Domain;
using AEPExplorer.Model.Request;
using AEPExplorer.Service.Elasticsearch;
using Apache.Arrow;
using Apache.Arrow.Ipc;
using Microsoft.Azure.Databricks.Client;
using Microsoft.Azure.Databricks.Client.Models;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace AEPExplorer.Service.Commands.Import;

public class ImportTpcCommand : MediatR.IRequest<bool>
{
    public ImportQuery ImportQuery { get; set; }
}

public class ImportTpcHandler(IConfiguration configuration, IServiceScopeFactory serviceScopeFactory, DatabricksClient databricksClient) : IRequestHandler<ImportTpcCommand, bool>
{
    public Task<bool> Handle(ImportTpcCommand request, CancellationToken cancellationToken)
    {
        try
        {
            Thread backgroundThread = new(() => _ = GenerateTpc());
            backgroundThread.Start();
            return Task.FromResult(true);
        }
        catch (SqlException ex)
        {
            Console.WriteLine(ex.Message);
            return Task.FromResult(false);
        }
    }

    private async Task<bool> GenerateTpc()
    {
        Console.WriteLine($"Started TPC import: {DateTime.Now}");

        var tpcDict = new Dictionary<int, Tpc>();
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();

        var existingTpcIds = dbContext.Tpc.Select(x => x.TpcId).ToHashSet();

        var turbinesWithoutTpc = dbContext.Turbines
            .Where(x => !x.Tpcs.Any())
            .Select(x => new
            {
                x.Id,
                x.MasterDataId
            }).ToList();

        Console.WriteLine($"Total number of turbines without TPC: {turbinesWithoutTpc.Count}");
        
        var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                ?? throw new InvalidOperationException("Databricks Catalog is missing");
        var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                          ?? throw new InvalidOperationException("Databricks WarehouseId is missing");

        var sqlStatement = new SqlStatement
        {
            WarehouseId = warehouseId,
            Statement = $"""
                         SELECT
                              IdStation
                              ,TPC_ID
                         FROM aep_tpc.aep_tpc_metadata
                         WHERE IsValid = 1
                         """,
            Catalog = databricksCatalog,
            WaitTimeout = "50s",
            Format = StatementFormat.ARROW_STREAM,
            Disposition = SqlStatementDisposition.EXTERNAL_LINKS
        };

        var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);

        while (statement.Status.State == StatementExecutionState.PENDING ||
               statement.Status.State == StatementExecutionState.RUNNING)
        {
            await Task.Delay(1000);
            statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);
            Console.WriteLine($"Current status: {statement.Status.State}");
        }

        if (statement.Status.State != StatementExecutionState.SUCCEEDED)
        {
            throw new Exception($"Query failed: {statement.Status.Error}");
        }

        if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
        {
            return false;
        }

        var result = new List<ArrowDataBatchTpcTurbine>();
        try
        {
            using var httpClient = new HttpClient();
            foreach (var link in statement.Result.ExternalLinks)
            {
                var response = await httpClient.GetAsync(link.ExternalLink);
                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Failed to download data from external link: {response.StatusCode}");
                    continue;
                }

                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new ArrowStreamReader(stream);

                while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                {
                    var schema = recordBatch.Schema;

                    var columns = new ArrowDataBatchTpcTurbine
                    {
                        TpcId = (Int32Array)recordBatch.Column(schema.GetFieldIndex("TPC_ID")),
                        IdStation = (Int32Array)recordBatch.Column(schema.GetFieldIndex("IdStation")),
                        Length = recordBatch.Length
                    };

                    result.Add(columns);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing Arrow data: {ex.Message}");
            return false;
        }

        foreach (var batch in result)
        {
            for (var i = 0; i < batch.Length; i++)
            {
                var tpcId = batch.TpcId.Values[i];
                if (existingTpcIds.Contains(tpcId)) continue;

                var turbine = turbinesWithoutTpc.FirstOrDefault(x => x.MasterDataId == batch.IdStation.Values[i]);
                if (turbine != null)
                {
                    if (!tpcDict.TryGetValue(tpcId, out var tpc))
                    {
                        tpc = new Tpc
                        {
                            TpcId = tpcId,
                            From = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc),
                            To = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc),
                            Turbines = new List<Turbine>()
                        };
                        tpcDict[tpcId] = tpc;
                    }

                    var dbTurbine = await dbContext.Turbines.FindAsync(turbine.Id);
                    if (dbTurbine != null)
                    {
                        tpc.Turbines.Add(dbTurbine);
                    }
                }
            }
        }

        var tpcList = tpcDict.Values.ToList();
        Console.WriteLine($"Total number of TPCs: {tpcList.Count}");

        var counter = 0;
        foreach (var tpcBatch in tpcList.Batch(100))
        {
            counter += tpcBatch.Count();
            Console.WriteLine($"{counter} of {tpcList.Count}");
            var tpcLines = new List<(int, string)>();
            var tpcJoin = string.Join(',', tpcBatch.Select(x => x.TpcId));
            sqlStatement = new SqlStatement
            {
                WarehouseId = warehouseId,
                Statement = $"""
                             SELECT TPC_ID
                                 ,WindSpeed
                                 ,Power
                             FROM {databricksCatalog}.aep_tpc.aep_tpc
                             WHERE TPC_ID IN ({tpcJoin})
                             ORDER BY WindSpeed
                             """,
                Catalog = databricksCatalog,
                WaitTimeout = "50s",
                Format = StatementFormat.ARROW_STREAM,
                Disposition = SqlStatementDisposition.EXTERNAL_LINKS
            };

            statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);

            while (statement.Status.State == StatementExecutionState.PENDING ||
                   statement.Status.State == StatementExecutionState.RUNNING)
            {
                await Task.Delay(1000);
                statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);
                Console.WriteLine($"Current status: {statement.Status.State}");
            }

            if (statement.Status.State != StatementExecutionState.SUCCEEDED)
            {
                throw new Exception($"Query failed: {statement.Status.Error}");
            }

            if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
            {
                return false;
            }

            var tpcArrowDataBatches = new List<ArrowDataBatchTpc>();
            try
            {
                using var httpClient = new HttpClient();
                foreach (var link in statement.Result.ExternalLinks)
                {
                    var response = await httpClient.GetAsync(link.ExternalLink);
                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"Failed to download data from external link: {response.StatusCode}");
                        continue;
                    }

                    await using var stream = await response.Content.ReadAsStreamAsync();
                    using var reader = new ArrowStreamReader(stream);

                    while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                    {
                        var schema = recordBatch.Schema;

                        var columns = new ArrowDataBatchTpc
                        {
                            TpcId = (Int32Array)recordBatch.Column(schema.GetFieldIndex("TPC_ID")),
                            WindSpeed = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("WindSpeed")),
                            Power = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("Power")),
                            Length = recordBatch.Length
                        };

                        tpcArrowDataBatches.Add(columns);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing Arrow data: {ex.Message}");
                return false;
            }

            foreach (var batch in tpcArrowDataBatches)
            {
                for (var i = 0; i < batch.Length; i++)
                {
                    var tpcId = batch.TpcId.Values[i];
                    double? windSpeed = batch.WindSpeed.Values[i];
                    double? power = batch.Power.Values[i];
                    tpcLines.Add(new ValueTuple<int, string>(tpcId, $"{{\"windSpeed\": {windSpeed}, \"power\": {power},}}"));
                }
            }

            foreach (var tpc in tpcBatch)
            {
                try
                {
                    var turbineTpcLines = tpcLines.Where(x => x.Item1 == tpc.TpcId).Select(x => x.Item2);
                    tpc.LineCoordinates = $"[{string.Join(", ", turbineTpcLines)}]";

                    dbContext.Tpc.Add(tpc);
                    await dbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                }
            }

            if (ElasticsearchGeneralService.IsImportEnabled)
            {
                continue;
            }

            Console.WriteLine("Import is stopping.");
            return false;
        }


        Console.WriteLine($"Finished: {DateTime.Now}");
        return true;
    }

    private class ArrowDataBatchTpcTurbine
    {
        public Int32Array TpcId { get; init; }
        public Int32Array IdStation { get; init; }
        public int Length { get; init; }
    }

    private class ArrowDataBatchTpc
    {
        public Int32Array TpcId { get; init; }
        public DoubleArray WindSpeed { get; init; }
        public DoubleArray Power { get; init; }
        public int Length { get; init; }
    }
}