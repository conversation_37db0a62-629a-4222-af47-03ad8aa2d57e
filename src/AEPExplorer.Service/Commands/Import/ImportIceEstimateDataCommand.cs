using AEPExplorer.Data.EF;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;
using Apache.Arrow;
using Apache.Arrow.Ipc;
using Microsoft.Azure.Databricks.Client;
using Microsoft.Azure.Databricks.Client.Models;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace AEPExplorer.Service.Commands.Import;

public class ImportIceEstimateData : MediatR.IRequest<bool>
{
    public ImportQuery ImportQuery { get; init; }
}

public class ImportIceEstimateDataHandler(IElasticClient elasticClient, IConfiguration configuration, IServiceScopeFactory serviceScopeFactory, DatabricksClient databricksClient) : IRequestHandler<ImportIceEstimateData, bool>
{
    public Task<bool> Handle(ImportIceEstimateData request, CancellationToken cancellationToken)
    {
        var response = elasticClient.Ping();
        if (response == null || !response.ApiCall.Success)
        {
            return Task.FromResult(false);
        }

        try
        {
            if (!ElasticsearchImportService.CreateIndex<AepImportData>(elasticClient, ElasticsearchConstants.INDEX_NAME_NEXT_VERSION))
            {
                return Task.FromResult(false);
            }

            _ = Task.Run(() => BC_TryTake(request), cancellationToken);
            return Task.FromResult(true);
        }

        catch (SqlException ex)
        {
            Console.WriteLine(ex.Message);
            return Task.FromResult(false);
        }
    }

    private async Task BC_TryTake(ImportIceEstimateData request)
    {
        var startTime = DateTime.Now;
        Console.WriteLine($"Started at: {startTime.ToShortTimeString()}.");

        try
        {
            var turbines = await GetTurbines();
            Console.WriteLine($"Number of turbines to be imported: {turbines.Count}.");
            var categoryIds = GetSubcategoryIds();
            var categoryIdSet = new HashSet<Guid>(categoryIds);

            await GenerateIndexParallel(turbines, categoryIdSet, request);
            var endTime = DateTime.Now;
            Console.WriteLine($"Finished at: {endTime.ToShortTimeString()}, after {(endTime - startTime).TotalMinutes.Round(2)} minutes");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in BC_TryTake: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");

            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
        }
    }

    private class ArrowDataBatch
    {
        public Date32Array Utc { get; init; }
        public Int32Array TurbineId { get; init; }
        public Int32Array IceCode { get; init; }
        public DoubleArray ActualEnergy { get; init; }
        public DoubleArray EnergyPotential { get; init; }
        public DoubleArray DataCoverage { get; init; }
        public DoubleArray Duration { get; init; }
        public int Length { get; init; }
    }

    private static List<Guid> GetSubcategoryIds()
    {
        var subcategories = CategoryHelper.ICE_ESTIMATE_CATEGORIES.Select(x => x.Key).ToList();
        return subcategories;
    }

    private async Task<List<TurbineFlat>> GetTurbines()
    {
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();

        var turbines = await dbContext.Turbines
            .Select(x => new TurbineFlat
            {
                CustomerId = x.CustomerId,
                RegionId = x.Site.Country.RegionId,
                CountryId = x.Site.CountryId,
                SiteId = x.SiteId,
                Id = x.Id,
                TurbineMasterDataId = x.MasterDataId,
                ModelId = x.ModelId,
                PlatformId = x.TurbineModel.TurbinePlatformId,
                OemId = x.TurbineModel.TurbinePlatform.OemId,
                LocationTypeName = x.LocationTypeName
            }).ToListAsync();

        return turbines;
    }

    private async Task GenerateIndexParallel(List<TurbineFlat> turbinesAll, HashSet<Guid> categoryIdSet, ImportIceEstimateData request)
    {
        var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                ?? throw new InvalidOperationException("Databricks Catalog is missing");
        var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                          ?? throw new InvalidOperationException("Databricks WarehouseId is missing");

        for (var dt = request.ImportQuery.DateFrom; dt <= request.ImportQuery.DateTo; dt = dt.AddDays(1))
        {
            var currentDate = dt;
            try
            {
                var query = new Query
                {
                    DateFrom = currentDate,
                    DateTo = currentDate,
                    CalculationMethod = request.ImportQuery.CalculationMethod,
                    Category = [CategoryHelper.ICE_IEA_DECREASED_PRODUCTION, CategoryHelper.ICE_IEB_STANDSTILL, CategoryHelper.ICE_IEC_OVERPRODUCTION]
                };

                Console.WriteLine($"Processing date: {currentDate:yyyy-MM-dd} [{DateTime.Now.ToShortTimeString()}]");

                var importedTurbines = await ElasticsearchImportService.GetDistinctTurbinesForImportAsync(elasticClient, query);
                var turbinesToImport = turbinesAll.Where(x => !importedTurbines.Contains(x.Id.ToString())).ToList();

                if (turbinesToImport.Count == 0)
                {
                    Console.WriteLine($"No turbines to import for date {currentDate:yyyy-MM-dd}");
                    continue;
                }

                var turbineByMasterDataId = turbinesToImport.ToDictionary(t => t.TurbineMasterDataId, t => t);

                var arrowData = await GetDatabricksDataForDate(currentDate, databricksCatalog, warehouseId);
                if (arrowData == null || !arrowData.Any())
                {
                    Console.WriteLine($"No data returned for date {currentDate:yyyy-MM-dd}");
                    continue;
                }

                var aepImportData = ProcessArrowData(arrowData, turbineByMasterDataId, categoryIdSet);

                if (!ElasticsearchGeneralService.IsImportEnabled)
                {
                    Console.WriteLine("Import is stopping.");
                    return;
                }

                try
                {
                    Console.WriteLine($"Writing {aepImportData.Count} ice estimate data records to Elasticsearch");
                    foreach (var batch in aepImportData.Batch(200))
                    {
                        await ElasticsearchImportService.BulkInsertAsync(elasticClient, batch, ElasticsearchConstants.INDEX_NAME_NEXT_VERSION);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error writing to Elasticsearch: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing date {currentDate:yyyy-MM-dd}: {ex.Message}");
            }
        }
    }

    private async Task<List<ArrowDataBatch>> GetDatabricksDataForDate(DateTime dt, string databricksCatalog,
        string warehouseId)
    {
        const int maxRetries = 10;
        const int retryDelayMs = 5000;

        for (int retryCount = 0; retryCount <= maxRetries; retryCount++)
        {
            try
            {
                if (retryCount > 0)
                {
                    Console.WriteLine($"Retry attempt {retryCount} of {maxRetries} for date {dt:yyyy-MM-dd}");
                    await Task.Delay(retryDelayMs * retryCount);
                }

                var sqlStatement = new SqlStatement
                {
                    WarehouseId = warehouseId,
                    Statement = $"""
                                 SELECT aid.DateUTC as Utc
                                     ,aid.TurbineId as TurbineId
                                     ,aid.IceCode as IceCode
                                     ,aid.ActualEnergy as ActualEnergy
                                     ,aid.EnsembleEPCPEP as EnergyPotential
                                     ,aid.EnsembleEPCPEPCoverageTime as DataCoverage
                                     ,aid.EnergyCoverageTime as Duration
                                 FROM aep_agg.aep_ice_dayagg aid
                                 WHERE aid.DateUTC = '{dt:yyyy-MM-dd}';
                                 """,
                    Catalog = databricksCatalog,
                    WaitTimeout = "50s",
                    Format = StatementFormat.ARROW_STREAM,
                    Disposition = SqlStatementDisposition.EXTERNAL_LINKS
                };

                var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);

                int statusCheckCount = 0;
                while (statement.Status.State == StatementExecutionState.PENDING ||
                       statement.Status.State == StatementExecutionState.RUNNING)
                {
                    await Task.Delay(1000);
                    statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);

                    if (statusCheckCount++ % 10 == 0)
                    {
                        Console.WriteLine($"Current status: {statement.Status.State}");
                    }

                    if (statusCheckCount > 120)
                    {
                        throw new TimeoutException("Statement execution timed out after 2 minutes");
                    }
                }

                if (statement.Status.State != StatementExecutionState.SUCCEEDED)
                {
                    if (statement.Status.Error != null &&
                        statement.Status.Error.Message.Contains("Azure storage request is not authorized") &&
                        retryCount < maxRetries)
                    {
                        Console.WriteLine($"Azure storage authorization error, will retry. Details: {statement.Status.Error.Message}");
                        continue;
                    }

                    throw new Exception($"Query failed: {statement.Status.Error}");
                }

                if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
                {
                    return null;
                }

                var result = new List<ArrowDataBatch>();
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (var link in statement.Result.ExternalLinks)
                {
                    var response = await httpClient.GetAsync(link.ExternalLink);
                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"Failed to download data from external link: {response.StatusCode}");
                        continue;
                    }

                    await using var stream = await response.Content.ReadAsStreamAsync();
                    using var reader = new ArrowStreamReader(stream);

                    while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                    {
                        var schema = recordBatch.Schema;

                        var columns = new ArrowDataBatch
                        {
                            Utc = (Date32Array)recordBatch.Column(schema.GetFieldIndex("Utc")),
                            TurbineId = (Int32Array)recordBatch.Column(schema.GetFieldIndex("TurbineId")),
                            IceCode = (Int32Array)recordBatch.Column(schema.GetFieldIndex("IceCode")),
                            ActualEnergy = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("ActualEnergy")),
                            EnergyPotential = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("EnergyPotential")),
                            DataCoverage = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("DataCoverage")),
                            Duration = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("Duration")),
                            Length = recordBatch.Length
                        };

                        result.Add(columns);
                    }
                }

                return result;
            }
            catch (Exception ex) when (retryCount < maxRetries &&
                                       (ex.Message.Contains("Azure storage request is not authorized") ||
                                        ex.Message.Contains("timeout") ||
                                        ex.Message.Contains("network") ||
                                        ex.Message.Contains("connection")))
            {
                Console.WriteLine($"Retryable error on attempt {retryCount + 1}: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing Arrow data: {ex.Message}");
                return null;
            }
        }

        Console.WriteLine($"Failed to get data after {maxRetries} retry attempts for date {dt:yyyy-MM-dd}");
        return null;
    }

    private List<AepImportData> ProcessArrowData(List<ArrowDataBatch> arrowDataBatches,
        Dictionary<int, TurbineFlat> turbineByMasterDataId, HashSet<Guid> categoryIdSet)
    {
        var result = new List<AepImportData>();

        foreach (var batch in arrowDataBatches)
        {
            for (var i = 0; i < batch.Length; i++)
            {
                var iceCode = batch.IceCode.Values[i];
                Guid categoryId;

                switch (iceCode)
                {
                    case 999991:
                        categoryId = CategoryHelper.ICE_IEA_DECREASED_PRODUCTION;
                        break;
                    case 999992:
                        categoryId = CategoryHelper.ICE_IEB_STANDSTILL;
                        break;
                    case 999993:
                        categoryId = CategoryHelper.ICE_IEC_OVERPRODUCTION;
                        break;
                    default:
                        continue;
                }

                if (!categoryIdSet.Contains(categoryId))
                {
                    continue;
                }

                var turbineMasterDataId = batch.TurbineId.Values[i];
                if (!turbineByMasterDataId.TryGetValue(turbineMasterDataId, out var turbine))
                {
                    continue;
                }

                var actualEnergy = batch.ActualEnergy.Values[i];
                var energyPotential = batch.EnergyPotential.Values[i];
                var dataCoverage = batch.DataCoverage.Values[i];
                var duration = batch.Duration.Values[i];

                var utc = batch.Utc.GetDateTime(i);
                if (utc == null)
                {
                    continue;
                }

                result.Add(new AepImportData
                {
                    Utc = DateTime.SpecifyKind(utc.Value, DateTimeKind.Utc),
                    TurbineId = turbine.Id,
                    SubcategoryId = categoryId,
                    ActualEnergy = actualEnergy,
                    EnergyPotential = energyPotential,
                    DataCoverage = dataCoverage / ElasticsearchConstants.SECONDS_IN_ONE_DAY,
                    DurationInHours = duration.Round(0) / ElasticsearchConstants.SECONDS_IN_ONE_HOUR,
                    CalculationMethod = CalculationMethodEnum.EnsembleTPCPEP,
                    AggregationPeriod = AggregationPeriodEnum.Day,
                    CustomerId = turbine.CustomerId,
                    RegionId = turbine.RegionId,
                    CountryId = turbine.CountryId,
                    SiteId = turbine.SiteId,
                    TurbineModelId = turbine.ModelId,
                    TurbinePlatformId = turbine.PlatformId,
                    TurbineOemId = turbine.OemId,
                    LossesType = LossesTypeEnum.IceEstimateTopic,
                    LocationTypeName = turbine.LocationTypeName
                });
            }
        }

        return result;
    }
}

