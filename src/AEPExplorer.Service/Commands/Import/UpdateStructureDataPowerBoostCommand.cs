using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Commands.Import;

public class UpdateStructureDataPowerBoostCommand : MediatR.IRequest<bool>
{
        public ImportQuery ImportQuery { get; init; }
}

public class UpdateStructurePowerBoostCommandHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<UpdateStructureDataPowerBoostCommand, bool>
{
    public async Task<bool> Handle(UpdateStructureDataPowerBoostCommand request, CancellationToken cancellationToken)
    {
        var query = new Query
        {
            DateFrom = request.ImportQuery.DateFrom,
            DateTo = request.ImportQuery.DateTo
        };
        
        var importedTurbines = await ElasticsearchGeneralService.GetPowerBoostDistinctTurbinesAsync(elasticClient, query);
        var turbines = await dbContext.Turbines
            .Where(x => importedTurbines.Contains(x.Id))
            .ToListAsync(cancellationToken);
        turbines.ForEach(x => x.PowerBoost = true);

        var siteIds = turbines.Select(x => x.SiteId).ToList();
        var sites = await dbContext.Sites
            .Where(x => siteIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
        sites.ForEach(x => x.PowerBoost = true);

        var countryIds = sites.Select(x => x.CountryId).ToList();
        var countries = await dbContext.Countries
            .Where(x => countryIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
        countries.ForEach(x => x.PowerBoost = true);

        var regionIds = countries.Select(x => x.RegionId).ToList();
        var regions = await dbContext.Regions
            .Where(x => regionIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
        regions.ForEach(x => x.PowerBoost = true);

        var customerIds = turbines.Select(x => x.CustomerId).ToList();
        var customers = await dbContext.Customers
            .Where(x => customerIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
        customers.ForEach(x => x.PowerBoost = true);

        var modelIds = turbines.Select(x => x.ModelId).ToList();
        var models = await dbContext.Models
            .Where(x => modelIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
        models.ForEach(x => x.PowerBoost = true);

        var platformIds = models.Select(x => x.TurbinePlatformId).ToList();
        var platforms = await dbContext.TurbinePlatforms
            .Where(x => platformIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
        platforms.ForEach(x => x.PowerBoost = true);

        await dbContext.SaveChangesAsync(cancellationToken);
        return true;
    }
}