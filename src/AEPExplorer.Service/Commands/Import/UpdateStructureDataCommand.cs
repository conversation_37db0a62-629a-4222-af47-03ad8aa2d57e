using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Commands.Import;

public class UpdateStructureDataCommand : MediatR.IRequest<bool>
{
        public ImportQuery ImportQuery { get; init; }
}

public class UpdateStructureDataCommandHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<UpdateStructureDataCommand, bool>
{
    public async Task<bool> Handle(UpdateStructureDataCommand request, CancellationToken cancellationToken)
    {
        var query = new Query
        {
            DateFrom = request.ImportQuery.DateFrom,
            DateTo = request.ImportQuery.DateTo
        };
        Console.WriteLine("Update of present turbines started...");
        var importedTurbines = await ElasticsearchImportService.GetDistinctTurbinesForImportAsync(elasticClient, query);
        
        Console.WriteLine($"Total number of present turbines: {importedTurbines.Count}");
        var turbines = await dbContext.Turbines
            .Where(x => importedTurbines.Contains(x.Id.ToString()))
            .ToListAsync(cancellationToken);
        turbines.ForEach(x => x.IsPresent = true);

        var siteIds = turbines.Select(x => x.SiteId).ToList();
        var sites = await dbContext.Sites
            .Where(x => siteIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
        sites.ForEach(x => x.IsPresent = true);

        await dbContext.SaveChangesAsync(cancellationToken);
        Console.WriteLine("Update of present turbines finished...");
        return true;
    }
}