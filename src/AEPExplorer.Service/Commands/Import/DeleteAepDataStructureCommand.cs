using AEPExplorer.Data.EF;
using AEPExplorer.Model.Domain;

namespace AEPExplorer.Service.Commands.Import;

public class DeleteAepDataStructureCommand : MediatR.IRequest<bool>
{        
}

public class DeleteAepDataStructureCommandHandler(AepExplorerDbContext dbContext) : IRequestHandler<DeleteAepDataStructureCommand, bool>
{
    public async Task<bool> Handle(DeleteAepDataStructureCommand request, CancellationToken cancellationToken)
    {
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Turbine)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Site)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Country)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Region)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Customer)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(TurbineModel)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(TurbinePlatform)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(TurbineOem)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Subcategory)}\";", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Category)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Level4)}\" CASCADE;", cancellationToken: cancellationToken);
        await dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE \"{nameof(Level3)}\" CASCADE;", cancellationToken: cancellationToken);
            
        return true;
    }
}