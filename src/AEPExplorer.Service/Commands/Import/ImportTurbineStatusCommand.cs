using AEPExplorer.Data.EF;
using AEPExplorer.Model.Domain;
using AEPExplorer.Model.Request;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Apache.Arrow;
using Apache.Arrow.Ipc;
using Microsoft.Azure.Databricks.Client;
using Microsoft.Azure.Databricks.Client.Models;

namespace AEPExplorer.Service.Commands.Import;

public class ImportTurbineStatusCommand : MediatR.IRequest<bool>
{
    public ImportQuery ImportQuery { get; init; }
}

public class ImportTurbineStatusHandler(IConfiguration configuration, IServiceScopeFactory serviceScopeFactory, DatabricksClient databricksClient) : IRequestHandler<ImportTurbineStatusCommand, bool>
{
    public Task<bool> Handle(ImportTurbineStatusCommand request, CancellationToken cancellationToken)
    {
        try
        {
            Thread backgroundThread = new(() => _ = ImportTurbineStatus());
            backgroundThread.Start();
            return Task.FromResult(true);
        }
        catch (SqlException ex)
        {
            Console.WriteLine(ex.Message);
            return Task.FromResult(false);
        }
    }

    private async Task DeleteAllTurbineStatusAsync(AepExplorerDbContext dbContext)
    {
        dbContext.TurbineStatus.RemoveRange(dbContext.TurbineStatus);
        await dbContext.SaveChangesAsync();
    }

    private async Task<bool> ImportTurbineStatus()
    {
        Console.WriteLine($"Started Turbine Status Import: {DateTime.Now}");
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();

        await DeleteAllTurbineStatusAsync(dbContext);

        var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                ?? throw new InvalidOperationException("Databricks Catalog is missing");
        var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                          ?? throw new InvalidOperationException("Databricks WarehouseId is missing");

        var sqlStatement = new SqlStatement
        {
            WarehouseId = warehouseId,
            Statement = """
                SELECT
                    TurbineOEM,
                    ProjectParkname,
                    ScadaParkName,
                    ProjectParkId,
                    TurbineName,
                    TurbineId,
                    TurbinePlatform,
                    TurbineModel,
                    LocationTypeName,
                    CountryName,
                    RegionName,
                    RegionShortName,
                    TurbineStartUpDate,
                    IsTurbinePresent,
                    TurbineMissingInfo,
                    InsertTimestamp
                FROM aep_data_coverage.aep_turbine_status
                """,
            Catalog = databricksCatalog,
            WaitTimeout = "50s",
            Format = StatementFormat.ARROW_STREAM,
            Disposition = SqlStatementDisposition.EXTERNAL_LINKS
        };

        var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);
        while (statement.Status.State is StatementExecutionState.PENDING or StatementExecutionState.RUNNING)
        {
            await Task.Delay(1000);
            statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);
            Console.WriteLine($"Current status: {statement.Status.State}");
        }

        if (statement.Status.State != StatementExecutionState.SUCCEEDED)
        {
            throw new Exception($"Query failed: {statement.Status.Error}");
        }

        if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
        {
            return false;
        }

        var turbineStatusList = new List<TurbineStatus>();
        try
        {
            using var httpClient = new HttpClient();
            foreach (var link in statement.Result.ExternalLinks)
            {
                var response = await httpClient.GetAsync(link.ExternalLink);
                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Failed to download data from external link: {response.StatusCode}");
                    continue;
                }

                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new ArrowStreamReader(stream);

                while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                {
                    var schema = recordBatch.Schema;
                    var length = recordBatch.Length;
                    var turbineOem = (StringArray)recordBatch.Column(schema.GetFieldIndex("TurbineOEM"));
                    var projectParkName = (StringArray)recordBatch.Column(schema.GetFieldIndex("ProjectParkname"));
                    var scadaParkName = (StringArray)recordBatch.Column(schema.GetFieldIndex("ScadaParkName"));
                    var projectParkId = (Int32Array)recordBatch.Column(schema.GetFieldIndex("ProjectParkId"));
                    var turbineName = (StringArray)recordBatch.Column(schema.GetFieldIndex("TurbineName"));
                    var turbineId = (Int32Array)recordBatch.Column(schema.GetFieldIndex("TurbineId"));
                    var turbinePlatform = (StringArray)recordBatch.Column(schema.GetFieldIndex("TurbinePlatform"));
                    var turbineModel = (StringArray)recordBatch.Column(schema.GetFieldIndex("TurbineModel"));
                    var locationTypeName = (StringArray)recordBatch.Column(schema.GetFieldIndex("LocationTypeName"));
                    var countryName = (StringArray)recordBatch.Column(schema.GetFieldIndex("CountryName"));
                    var regionName = (StringArray)recordBatch.Column(schema.GetFieldIndex("RegionName"));
                    var regionShortName = (StringArray)recordBatch.Column(schema.GetFieldIndex("RegionShortName"));
                    var turbineStartUpDate = (TimestampArray)recordBatch.Column(schema.GetFieldIndex("TurbineStartUpDate"));
                    var isTurbinePresent = (BooleanArray)recordBatch.Column(schema.GetFieldIndex("IsTurbinePresent"));
                    var turbineMissingInfo = (StringArray)recordBatch.Column(schema.GetFieldIndex("TurbineMissingInfo"));

                    for (var i = 0; i < length; i++)
                    {
                        var status = new TurbineStatus
                        {
                            TurbineOem = turbineOem.IsNull(i) ? "-" : turbineOem.GetString(i)?.Trim(),
                            ProjectParkName = projectParkName.IsNull(i) ? "-" : projectParkName.GetString(i)?.Trim(),
                            ScadaParkName = scadaParkName.IsNull(i) ? "-" : scadaParkName.GetString(i)?.Trim(),
                            ProjectParkId = projectParkId.IsNull(i) ? "-" : projectParkId.Values[i].ToString(),
                            TurbineName = turbineName.IsNull(i) ? "-" : turbineName.GetString(i)?.Trim(),
                            TurbineId = turbineId.Values[i],
                            TurbinePlatform = turbinePlatform.IsNull(i) ? "-" : turbinePlatform.GetString(i)?.Trim(),
                            TurbineModel = turbineModel.IsNull(i) ? "-" : turbineModel.GetString(i)?.Trim(),
                            LocationTypeName = locationTypeName.IsNull(i) ? "-" : locationTypeName.GetString(i)?.Trim(),
                            CountryName = countryName.IsNull(i) ? "-" : countryName.GetString(i)?.Trim(),
                            RegionName = regionName.IsNull(i) ? "-" : regionName.GetString(i)?.Trim(),
                            RegionShortName = regionShortName.IsNull(i) ? "-" : regionShortName.GetString(i)?.Trim(),
                            TurbineStartUpDate = DateTime.SpecifyKind(turbineStartUpDate.GetTimestamp(i)?.DateTime ?? DateTime.MinValue, DateTimeKind.Utc),
                            IsTurbinePresent = isTurbinePresent.GetValue(i) ?? false,
                            TurbineMissingInfo = turbineMissingInfo.IsNull(i) ? "-" : turbineMissingInfo.GetString(i)?.Trim(),
                        };
                        turbineStatusList.Add(status);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing Arrow data: {ex.Message}");
            return false;
        }

        Console.WriteLine($"Total number of TurbineStatus records: {turbineStatusList.Count}");

        try
        {
            foreach (var batch in turbineStatusList.Batch(100))
            {
                dbContext.TurbineStatus.AddRange(batch);
                await dbContext.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving TurbineStatus batch: {ex.Message}");
            return false;
        }

        Console.WriteLine($"Finished Turbine Status Import: {DateTime.Now}");
        return true;
    }
}
