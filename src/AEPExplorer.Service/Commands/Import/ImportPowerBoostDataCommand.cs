using AEPExplorer.Data.EF;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;
using Apache.Arrow;
using Apache.Arrow.Ipc;
using Microsoft.Azure.Databricks.Client;
using Microsoft.Azure.Databricks.Client.Models;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace AEPExplorer.Service.Commands.Import;

public class ImportPowerBoostData : MediatR.IRequest<bool>
{
    public ImportQuery ImportQuery { get; init; }
}

public class ImportPowerBoostDataHandler(IElasticClient elasticClient, IConfiguration configuration, IServiceScopeFactory serviceScopeFactory, DatabricksClient databricksClient) : IRequestHandler<ImportPowerBoostData, bool>
{
    public Task<bool> Handle(ImportPowerBoostData request, CancellationToken cancellationToken)
    {
        var response = elasticClient.Ping();
        if (response == null || !response.ApiCall.Success)
        {
            return Task.FromResult(false);
        }

        try
        {
            if (!ElasticsearchImportService.CreateIndex<PowerBoostImportData>(elasticClient, ElasticsearchConstants.INDEX_NAME_POWER_BOOST_NEXT_VERSION))
            {
                return Task.FromResult(false);
            }

            Thread backgroundThread = new(() => _ = BC_TryTake(request));
            backgroundThread.Start();
            return Task.FromResult(true);
        }

        catch (SqlException ex)
        {
            Console.WriteLine(ex.Message);
            return Task.FromResult(false);
        }
    }

    private async Task BC_TryTake(ImportPowerBoostData request)
    {
        var startTime = DateTime.Now;
        Console.WriteLine($"Started at: {startTime.ToShortTimeString()}.");

        try
        {
            var turbines = await GetTurbines();
            Console.WriteLine($"Number of turbines to be imported: {turbines.Count}.");

            await GenerateIndex(turbines, request);
            var endTime = DateTime.Now;
            Console.WriteLine($"Finished at: {endTime.ToShortTimeString()}, after {(endTime - startTime).TotalMinutes.Round(2)} minutes");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in BC_TryTake: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");

            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
        }
    }

    private async Task GenerateIndex(List<TurbineFlat> allTurbines, ImportPowerBoostData request)
    {
        var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                ?? throw new InvalidOperationException("Databricks Catalog is missing");
        var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                          ?? throw new InvalidOperationException("Databricks WarehouseId is missing");

        for (var dt = request.ImportQuery.DateFrom; dt <= request.ImportQuery.DateTo; dt = dt.AddDays(1))
        {
            try
            {
                var query = new Query
                {
                    DateFrom = dt,
                    DateTo = dt,
                    CalculationMethod = request.ImportQuery.CalculationMethod,
                };

                Console.WriteLine($"Processing date: {dt:yyyy-MM-dd} [{DateTime.Now.ToShortTimeString()}]");

                var importedTurbines = await ElasticsearchImportService.GetDistinctTurbinesForImportPowerBoostAsync(elasticClient, query);
                var turbinesToImport = allTurbines.Where(x => !importedTurbines.Contains(x.Id.ToString())).ToList();

                if (turbinesToImport.Count == 0)
                {
                    Console.WriteLine($"No turbines to import for date {dt:yyyy-MM-dd}");
                    continue;
                }

                var turbineByMasterDataId = turbinesToImport.ToDictionary(t => t.TurbineMasterDataId, t => t);

                var arrowData = await GetDatabricksDataForDate(dt, databricksCatalog, warehouseId);
                if (arrowData == null || !arrowData.Any())
                {
                    Console.WriteLine($"No data returned for date {dt:yyyy-MM-dd}");
                    continue;
                }

                var powerBoostImportData = ProcessArrowData(arrowData, turbineByMasterDataId);

                if (!ElasticsearchGeneralService.IsImportEnabled)
                {
                    Console.WriteLine("Import is stopping.");
                    return;
                }

                try
                {
                    Console.WriteLine($"Writing {powerBoostImportData.Count} power boost data records to Elasticsearch");
                    foreach (var batch in powerBoostImportData.Batch(200))
                    {
                        await ElasticsearchImportService.BulkInsertAsync(elasticClient, batch, ElasticsearchConstants.INDEX_NAME_POWER_BOOST_NEXT_VERSION);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error writing to Elasticsearch: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing date {dt:yyyy-MM-dd}: {ex.Message}");
            }
        }
    }

    private async Task<List<ArrowDataBatch>> GetDatabricksDataForDate(DateTime dt, string databricksCatalog,
        string warehouseId)
    {
        const int maxRetries = 10;
        const int retryDelayMs = 5000;

        for (int retryCount = 0; retryCount <= maxRetries; retryCount++)
        {
            try
            {
                if (retryCount > 0)
                {
                    Console.WriteLine($"Retry attempt {retryCount} of {maxRetries} for date {dt:yyyy-MM-dd}");
                    await Task.Delay(retryDelayMs * retryCount);
                }

                var sqlStatement = new SqlStatement
                {
                    WarehouseId = warehouseId,
                    Statement = $"""
                                 SELECT
                                 DateUTC as Utc
                                 ,TurbineId
                                 ,BoostCriteriaOK
                                 ,PowerFactor_CNF
                                 ,CoolDown_CNF
                                 ,Turbulence_CNF
                                 ,ACS_CNF
                                 ,SensorError_CNF
                                 ,RSA_CNF
                                 ,RunningRestricted_CNF
                                 ,Pitch_CNF
                                 ,Disabled_CNF
                                 ,SafeMode_CNF
                                 ,AmbientTemp_CNF
                                 ,GridVoltage_CNF
                                 ,CoolDown_CF
                                 ,Turbulence_CF
                                 ,ACS_CF
                                 ,SensorError_CF
                                 ,RSA_CF
                                 ,NotCurtailed_CNF
                                 ,Pitch_CF
                                 ,Disabled_CF
                                 ,SafeMode_CF
                                 ,AmbientTemp_CF
                                 ,GridVoltage_CF
                                 ,PowerFactor_CF
                                 ,BoostExpMin
                                 ,BoostExpMax
                                 ,BoostExpAvg
                                 ,BoostEnergyEst
                                 ,NotCurtailed_CF
                                 ,RunningRestricted_CF
                                 ,BoostExpectedCriteriaOK
                                 ,BoostCoverage
                                 ,BoostEnergyCnt
                                 ,BoostExpected
                                 ,BoostAct
                                 ,BoostAva
                                 FROM aep_agg.aep_power_boost_dayagg
                                 WHERE DateUTC = '{dt:yyyy-MM-dd}';
                                 """,
                    Catalog = databricksCatalog,
                    WaitTimeout = "50s",
                    Format = StatementFormat.ARROW_STREAM,
                    Disposition = SqlStatementDisposition.EXTERNAL_LINKS
                };

                var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement);

                int statusCheckCount = 0;
                while (statement.Status.State == StatementExecutionState.PENDING ||
                       statement.Status.State == StatementExecutionState.RUNNING)
                {
                    await Task.Delay(1000);
                    statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId);

                    if (statusCheckCount++ % 10 == 0)
                    {
                        Console.WriteLine($"Current status: {statement.Status.State}");
                    }

                    if (statusCheckCount > 120)
                    {
                        throw new TimeoutException("Statement execution timed out after 2 minutes");
                    }
                }

                if (statement.Status.State != StatementExecutionState.SUCCEEDED)
                {
                    if (statement.Status.Error != null &&
                        statement.Status.Error.Message.Contains("Azure storage request is not authorized") &&
                        retryCount < maxRetries)
                    {
                        Console.WriteLine($"Azure storage authorization error, will retry. Details: {statement.Status.Error.Message}");
                        continue;
                    }

                    throw new Exception($"Query failed: {statement.Status.Error}");
                }

                if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
                {
                    return null;
                }

                var result = new List<ArrowDataBatch>();
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                foreach (var link in statement.Result.ExternalLinks)
                {
                    var response = await httpClient.GetAsync(link.ExternalLink);
                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"Failed to download data from external link: {response.StatusCode}");
                        continue;
                    }

                    await using var stream = await response.Content.ReadAsStreamAsync();
                    using var reader = new ArrowStreamReader(stream);

                    while (await reader.ReadNextRecordBatchAsync() is { } recordBatch)
                    {
                        var schema = recordBatch.Schema;

                        var columns = new ArrowDataBatch
                        {
                            Utc = (Date32Array)recordBatch.Column(schema.GetFieldIndex("Utc")),
                            TurbineId = (Int32Array)recordBatch.Column(schema.GetFieldIndex("TurbineId")),
                            BoostCriteriaOK = (Int32Array)recordBatch.Column(schema.GetFieldIndex("BoostCriteriaOK")),
                            PowerFactor_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("PowerFactor_CNF")),
                            CoolDown_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("CoolDown_CNF")),
                            Turbulence_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("Turbulence_CNF")),
                            ACS_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("ACS_CNF")),
                            SensorError_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("SensorError_CNF")),
                            RSA_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("RSA_CNF")),
                            RunningRestricted_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("RunningRestricted_CNF")),
                            Pitch_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("Pitch_CNF")),
                            Disabled_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("Disabled_CNF")),
                            SafeMode_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("SafeMode_CNF")),
                            AmbientTemp_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("AmbientTemp_CNF")),
                            GridVoltage_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("GridVoltage_CNF")),
                            CoolDown_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("CoolDown_CF")),
                            Turbulence_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("Turbulence_CF")),
                            ACS_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("ACS_CF")),
                            SensorError_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("SensorError_CF")),
                            RSA_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("RSA_CF")),
                            NotCurtailed_CNF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("NotCurtailed_CNF")),
                            Pitch_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("Pitch_CF")),
                            Disabled_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("Disabled_CF")),
                            SafeMode_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("SafeMode_CF")),
                            AmbientTemp_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("AmbientTemp_CF")),
                            GridVoltage_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("GridVoltage_CF")),
                            PowerFactor_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("PowerFactor_CF")),
                            BoostExpMin = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("BoostExpMin")),
                            BoostExpMax = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("BoostExpMax")),
                            BoostExpAvg = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("BoostExpAvg")),
                            BoostEnergyEst = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("BoostEnergyEst")),
                            NotCurtailed_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("NotCurtailed_CF")),
                            RunningRestricted_CF = (Int32Array)recordBatch.Column(schema.GetFieldIndex("RunningRestricted_CF")),
                            BoostExpectedCriteriaOK = (Int32Array)recordBatch.Column(schema.GetFieldIndex("BoostExpectedCriteriaOK")),
                            BoostCoverage = (Int32Array)recordBatch.Column(schema.GetFieldIndex("BoostCoverage")),
                            BoostEnergyCnt = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("BoostEnergyCnt")),
                            BoostExpected = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("BoostExpected")),
                            BoostAct = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("BoostAct")),
                            BoostAva = (DoubleArray)recordBatch.Column(schema.GetFieldIndex("BoostAva")),
                            Length = recordBatch.Length
                        };

                        result.Add(columns);
                    }
                }

                return result;
            }
            catch (Exception ex) when (retryCount < maxRetries &&
                                       (ex.Message.Contains("Azure storage request is not authorized") ||
                                        ex.Message.Contains("timeout") ||
                                        ex.Message.Contains("network") ||
                                        ex.Message.Contains("connection")))
            {
                Console.WriteLine($"Retryable error on attempt {retryCount + 1}: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing Arrow data: {ex.Message}");
                return null;
            }
        }

        Console.WriteLine($"Failed to get data after {maxRetries} retry attempts for date {dt:yyyy-MM-dd}");
        return null;
    }

    private async Task<List<TurbineFlat>> GetTurbines()
    {
        //TODO
        //Share between all three import methods
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();

        var turbines = await dbContext.Turbines
            .Select(x => new TurbineFlat
            {
                CustomerId = x.CustomerId,
                RegionId = x.Site.Country.RegionId,
                CountryId = x.Site.CountryId,
                SiteId = x.SiteId,
                Id = x.Id,
                TurbineMasterDataId = x.MasterDataId,
                ModelId = x.ModelId,
                PlatformId = x.TurbineModel.TurbinePlatformId,
                OemId = x.TurbineModel.TurbinePlatform.OemId,
                LocationTypeName = x.LocationTypeName
            }).ToListAsync();

        return turbines;
    }

    private class ArrowDataBatch
    {
        public Date32Array Utc { get; init; }
        public Int32Array TurbineId { get; init; }
        public Int32Array BoostCriteriaOK { get; init; }
        public Int32Array PowerFactor_CNF { get; init; }
        public Int32Array CoolDown_CNF { get; init; }
        public Int32Array Turbulence_CNF { get; init; }
        public Int32Array ACS_CNF { get; init; }
        public Int32Array SensorError_CNF { get; init; }
        public Int32Array RSA_CNF { get; init; }
        public Int32Array RunningRestricted_CNF { get; init; }
        public Int32Array Pitch_CNF { get; init; }
        public Int32Array Disabled_CNF { get; init; }
        public Int32Array SafeMode_CNF { get; init; }
        public Int32Array AmbientTemp_CNF { get; init; }
        public Int32Array GridVoltage_CNF { get; init; }
        public Int32Array CoolDown_CF { get; init; }
        public Int32Array Turbulence_CF { get; init; }
        public Int32Array ACS_CF { get; init; }
        public Int32Array SensorError_CF { get; init; }
        public Int32Array RSA_CF { get; init; }
        public Int32Array NotCurtailed_CNF { get; init; }
        public Int32Array Pitch_CF { get; init; }
        public Int32Array Disabled_CF { get; init; }
        public Int32Array SafeMode_CF { get; init; }
        public Int32Array AmbientTemp_CF { get; init; }
        public Int32Array GridVoltage_CF { get; init; }
        public Int32Array PowerFactor_CF { get; init; }
        public DoubleArray BoostExpMin { get; init; }
        public DoubleArray BoostExpMax { get; init; }
        public DoubleArray BoostExpAvg { get; init; }
        public DoubleArray BoostEnergyEst { get; init; }
        public Int32Array NotCurtailed_CF { get; init; }
        public Int32Array RunningRestricted_CF { get; init; }
        public Int32Array BoostExpectedCriteriaOK { get; init; }
        public Int32Array BoostCoverage { get; init; }
        public DoubleArray BoostEnergyCnt { get; init; }
        public DoubleArray BoostExpected { get; init; }
        public DoubleArray BoostAct { get; init; }
        public DoubleArray BoostAva { get; init; }
        public int Length { get; init; }
    }

    private List<PowerBoostImportData> ProcessArrowData(List<ArrowDataBatch> arrowDataBatches,
        Dictionary<int, TurbineFlat> turbineByMasterDataId)
    {
        var result = new List<PowerBoostImportData>();

        foreach (var batch in arrowDataBatches)
        {
            for (var i = 0; i < batch.Length; i++)
            {
                var turbineMasterDataId = batch.TurbineId.Values[i];
                if (!turbineByMasterDataId.TryGetValue(turbineMasterDataId, out var turbine))
                {
                    continue;
                }

                var utc = batch.Utc.GetDateTime(i);
                if (utc == null)
                {
                    continue;
                }

                result.Add(new PowerBoostImportData
                {
                    Utc = DateTime.SpecifyKind(utc.Value, DateTimeKind.Utc),
                    TurbineId = turbine.Id,
                    CustomerId = turbine.CustomerId,
                    RegionId = turbine.RegionId,
                    CountryId = turbine.CountryId,
                    SiteId = turbine.SiteId,
                    TurbineModelId = turbine.ModelId,
                    TurbinePlatformId = turbine.PlatformId,
                    TurbineOemId = turbine.OemId,
                    LocationTypeName = turbine.LocationTypeName,
                    BoostCriteriaOK = Helper.GetInt32Value(batch.BoostCriteriaOK, i),
                    PowerFactor_CNF = Helper.GetInt32Value(batch.PowerFactor_CNF, i),
                    CoolDown_CNF = Helper.GetInt32Value(batch.CoolDown_CNF, i),
                    Turbulence_CNF = Helper.GetInt32Value(batch.Turbulence_CNF, i),
                    ACS_CNF = Helper.GetInt32Value(batch.ACS_CNF, i),
                    SensorError_CNF = Helper.GetInt32Value(batch.SensorError_CNF, i),
                    RSA_CNF = Helper.GetInt32Value(batch.RSA_CNF, i),
                    RunningRestricted_CNF = Helper.GetInt32Value(batch.RunningRestricted_CNF, i),
                    Pitch_CNF = Helper.GetInt32Value(batch.Pitch_CNF, i),
                    Disabled_CNF = Helper.GetInt32Value(batch.Disabled_CNF, i),
                    SafeMode_CNF = Helper.GetInt32Value(batch.SafeMode_CNF, i),
                    AmbientTemp_CNF = Helper.GetInt32Value(batch.AmbientTemp_CNF, i),
                    GridVoltage_CNF = Helper.GetInt32Value(batch.GridVoltage_CNF, i),
                    CoolDown_CF = Helper.GetInt32Value(batch.CoolDown_CF, i),
                    Turbulence_CF = Helper.GetInt32Value(batch.Turbulence_CF, i),
                    ACS_CF = Helper.GetInt32Value(batch.ACS_CF, i),
                    SensorError_CF = Helper.GetInt32Value(batch.SensorError_CF, i),
                    RSA_CF = Helper.GetInt32Value(batch.RSA_CF, i),
                    NotCurtailed_CNF = Helper.GetInt32Value(batch.NotCurtailed_CNF, i),
                    Pitch_CF = Helper.GetInt32Value(batch.Pitch_CF, i),
                    Disabled_CF = Helper.GetInt32Value(batch.Disabled_CF, i),
                    SafeMode_CF = Helper.GetInt32Value(batch.SafeMode_CF, i),
                    AmbientTemp_CF = Helper.GetInt32Value(batch.AmbientTemp_CF, i),
                    GridVoltage_CF = Helper.GetInt32Value(batch.GridVoltage_CF, i),
                    PowerFactor_CF = Helper.GetInt32Value(batch.PowerFactor_CF, i),
                    BoostExpMin = Helper.GetNullableDoubleValue(batch.BoostExpMin, i, 4),
                    BoostExpMax = Helper.GetNullableDoubleValue(batch.BoostExpMax, i, 4),
                    BoostExpAvg = Helper.GetNullableDoubleValue(batch.BoostExpAvg, i, 4),
                    BoostEnergyEst = Helper.GetDoubleValue(batch.BoostEnergyEst, i, 4),
                    NotCurtailed_CF = Helper.GetInt32Value(batch.NotCurtailed_CF, i),
                    RunningRestricted_CF = Helper.GetInt32Value(batch.RunningRestricted_CF, i),
                    BoostExpectedCriteriaOK = Helper.GetInt32Value(batch.BoostExpectedCriteriaOK, i),
                    BoostCoverage = Helper.GetInt32Value(batch.BoostCoverage, i),
                    BoostEnergyCnt = Helper.GetDoubleValue(batch.BoostEnergyCnt, i, 4),
                    BoostExpected = Helper.GetDoubleValue(batch.BoostExpected, i, 4),
                    BoostAct = Helper.GetDoubleValue(batch.BoostAct, i, 4),
                    BoostAva = Helper.GetDoubleValue(batch.BoostAva, i, 4),
                    AggregationPeriod = AggregationPeriodEnum.Day
                });
            }
        }

        return result;
    }
}

