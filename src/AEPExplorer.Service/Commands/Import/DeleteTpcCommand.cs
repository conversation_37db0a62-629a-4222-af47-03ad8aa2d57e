using AEPExplorer.Data.EF;

namespace AEPExplorer.Service.Commands.Import;

public class DeleteTpcCommand : MediatR.IRequest<bool>
{        
}

public class DeleteTpcCommandHandler(AepExplorerDbContext dbContext) : IRequestHandler<DeleteTpcCommand, bool>
{
    public async Task<bool> Handle(DeleteTpcCommand request, CancellationToken cancellationToken)
    {
        await dbContext.Database.ExecuteSqlRawAsync("TRUNCATE \"TpcTurbine\", \"Tpc\" CASCADE;", cancellationToken: cancellationToken);
        return true;
    }
}