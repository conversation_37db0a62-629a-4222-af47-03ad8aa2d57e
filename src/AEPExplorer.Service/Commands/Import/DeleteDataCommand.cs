using AEPExplorer.Data.EF;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Commands.Import;

public class DeleteData : MediatR.IRequest<bool>
{
    public ImportQuery ImportQuery { get; set; }
}

public class DeleteDataHandler(IElasticClient elasticClient, IServiceScopeFactory serviceScopeFactory) : IRequestHandler<DeleteData, bool>
{
    public Task<bool> Handle(DeleteData request, CancellationToken cancellationToken)
    {
        var response = elasticClient.Ping();
        if (response == null || !response.ApiCall.Success)
        {
            return Task.FromResult(false);
        }

        try
        {
            if (!ElasticsearchImportService.CreateIndex<AepReadData>(elasticClient, ElasticsearchConstants.INDEX_NAME))
            {
                return Task.FromResult(false);
            }

            Thread backgroundThread = new(() => BC_TryTake(serviceScopeFactory, request));
            backgroundThread.Start();
            return Task.FromResult(true);
        }

        catch (SqlException ex)
        {
            Console.WriteLine(ex.Message);
            return Task.FromResult(false);
        }
    }

    private void BC_TryTake(IServiceScopeFactory serviceScopeFactory, DeleteData request)
    {
        var startTime = DateTime.Now;
        Console.WriteLine($"Started at: {startTime.ToShortTimeString()}.");
        var turbines = GetTurbines(serviceScopeFactory);
        var numberOfThreads = Math.Min(turbines.Count, 100);
        using (var bc = new BlockingCollection<int>())
        {
            foreach (var t in turbines)
            {
                bc.Add(t.TurbineMasterDataId);
            }

            bc.CompleteAdding();

            void Action()
            {
                while (bc.TryTake(out var turbineMasterDataId))
                {
                    if (!ElasticsearchGeneralService.IsImportEnabled)
                    {
                        Console.WriteLine($"Import is stopping.");
                        break;
                    }

                    var id = turbineMasterDataId;
                    var turbine = turbines.FirstOrDefault(x => x.TurbineMasterDataId == id);
                    if (turbine == null)
                    {
                        continue;
                    }

                    Console.WriteLine(
                        $"[{turbines.Count - bc.Count} / {turbines.Count}] time: {(DateTime.Now - startTime).TotalMinutes.Round(2)}");
                    if (DeleteTurbine(request.ImportQuery, turbine.Id))
                    {
                        // Log(turbine, serviceScopeFactory, request.ImportQuery.DateFrom,request.ImportQuery.DateTo);
                    }
                }
            }

            var actions = new Action[numberOfThreads];
            for (var i = 0; i < numberOfThreads; i++)
            {
                actions[i] = Action;
            }

            Parallel.Invoke(actions.ToArray());
        }

        var endTime = DateTime.Now;
        Console.WriteLine($"Finished at: {endTime.ToShortTimeString()}, after {(endTime - startTime).TotalMinutes.Round(2)}");
    }

    private static List<TurbineFlat> GetTurbines(IServiceScopeFactory serviceScopeFactory)
    {
        using var scope = serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();
        var turbines = dbContext.Turbines
            .Select(x => new TurbineFlat
            {
                RegionId = x.Site.Country.RegionId,
                CountryId = x.Site.CountryId,
                SiteId = x.SiteId,
                Id = x.Id,
                TurbineMasterDataId = x.MasterDataId,
                ModelId = x.ModelId,
                PlatformId = x.TurbineModel.TurbinePlatformId,
                OemId = x.TurbineModel.TurbinePlatform.OemId
            }).ToList();

        return turbines;
    }

    private bool DeleteTurbine(ImportQuery query, Guid turbineId)
    {
        try
        {
            return ElasticsearchImportService.DeleteDataRaw(elasticClient, query.DateFrom, query.DateTo, turbineId).Result;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return false;
        }
    }
}