using AEPExplorer.Data.EF;

namespace AEPExplorer.Service.Commands;

public class DeleteFilterPresetCommand : MediatR.IRequest<bool>
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }        
}

public class DeleteFilterPresetCommandHandler : IRequestHandler<DeleteFilterPresetCommand, bool>
{
    private readonly AepExplorerDbContext _dbContext;

    public DeleteFilterPresetCommandHandler(AepExplorerDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    public async Task<bool> Handle(DeleteFilterPresetCommand request, CancellationToken cancellationToken)
    {
        var filterPreset = await _dbContext.FilterPreset.FirstOrDefaultAsync(x => x.Id == request.Id && x.UserId == request.UserId, cancellationToken);
        if (filterPreset == null)
        {
            return false;
        }

        _dbContext.FilterPreset.Remove(filterPreset);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return true;
    }
}