using AEPExplorer.Data.EF;

namespace AEPExplorer.Service.Commands.FilterPreset;

public class UpdateFavoriteFilterPresetCommand : MediatR.IRequest<Guid?>
{
    public Guid Id { get; init; }
    public bool Favorite { get; init; }
    public Guid UserId { get; init; }
}

public class UpdateFavoriteFilterPresetCommandHandler(AepExplorerDbContext dbContext) : IRequestHandler<UpdateFavoriteFilterPresetCommand, Guid?>
{
    public async Task<Guid?> Handle(UpdateFavoriteFilterPresetCommand request, CancellationToken cancellationToken)
    {
        var filterPreset = await dbContext.FilterPreset.FirstOrDefaultAsync(x => x.Id == request.Id && x.UserId == request.UserId, cancellationToken);
        if (filterPreset == null)
        {
            return null;
        }

        filterPreset.Favorite = request.Favorite;
        filterPreset.UpdatedAt = DateTime.UtcNow;

        dbContext.FilterPreset.Update(filterPreset);
        await dbContext.SaveChangesAsync(cancellationToken);

        return filterPreset.Id;
    }
}