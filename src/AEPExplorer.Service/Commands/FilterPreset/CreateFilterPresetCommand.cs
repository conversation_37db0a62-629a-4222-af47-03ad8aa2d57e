using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using Newtonsoft.Json;

namespace AEPExplorer.Service.Commands.FilterPreset;

public class CreateFilterPresetCommand : MediatR.IRequest<Guid>
{
    public string Name { get; set; }
    public Guid UserId { get; set; }
    public QueryPreset Query { get; set; }
}

public class CreateFilterPresetCommandHandler : IRequestHandler<CreateFilterPresetCommand, Guid>
{
    private readonly AepExplorerDbContext _dbContext;

    public CreateFilterPresetCommandHandler(AepExplorerDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    public async Task<Guid> Handle(CreateFilterPresetCommand request, CancellationToken cancellationToken)
    {
        var filterPreset = new Model.Domain.FilterPreset
        {
            UserId = request.UserId,
            Query = JsonConvert.SerializeObject(request.Query),
            Name = request.Name,
            CreatedAt = DateTime.UtcNow
        };

        _dbContext.FilterPreset.Add(filterPreset);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return filterPreset.Id;
    }
}