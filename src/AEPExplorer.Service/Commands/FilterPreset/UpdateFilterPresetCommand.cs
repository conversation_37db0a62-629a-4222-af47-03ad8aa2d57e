using AEPExplorer.Data.EF;

namespace AEPExplorer.Service.Commands;

public class UpdateFilterPresetCommand : MediatR.IRequest<Guid?>
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public Guid UserId { get; set; }
}

public class UpdateFilterPresetCommandHandler : IRequestHandler<UpdateFilterPresetCommand, Guid?>
{
    private readonly AepExplorerDbContext _dbContext;

    public UpdateFilterPresetCommandHandler(AepExplorerDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    public async Task<Guid?> Handle(UpdateFilterPresetCommand request, CancellationToken cancellationToken)
    {
        var filterPreset = await _dbContext.FilterPreset.FirstOrDefaultAsync(x => x.Id == request.Id && x.UserId == request.UserId, cancellationToken);
        if (filterPreset == null)
        {
            return null;
        }

        filterPreset.Name = request.Name;
        filterPreset.UpdatedAt = DateTime.UtcNow;

        _dbContext.FilterPreset.Update(filterPreset);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return filterPreset.Id;
    }
}