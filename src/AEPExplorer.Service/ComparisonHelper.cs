using AEPExplorer.Model.Response;

namespace AEPExplorer.Service;

public static class ComparisonHelper
{
    public static List<FromToValue> GetDownPeriod(List<ValueDate> actual)
    {
        var downPeriod = new List<FromToValue>();

        DateTime? fromDate = null;
        DateTime? toDate = null;
        for (var i = 0; i < actual.Count; i++)
        {
            var item = actual[i];

            if (item.Value == 0)
            {
                if (fromDate == null)
                {
                    fromDate = item.Date;
                    toDate = item.Date;
                }
                else
                {
                    toDate = item.Date;
                }

                if (i == actual.Count - 1)
                {
                    downPeriod.Add(new FromToValue
                    {
                        From = fromDate.Value,
                        To = toDate.Value,
                        Value = 0,
                        Length = (toDate.Value - fromDate.Value).Days + 1
                    });
                    fromDate = toDate = null;
                }
            }
            else
            {
                if (fromDate.HasValue)
                {
                    downPeriod.Add(new FromToValue
                    {
                        From = fromDate.Value,
                        To = toDate.Value,
                        Value = 0,
                        Length = (toDate.Value - fromDate.Value).Days + 1
                    });
                    fromDate = toDate = null;
                }
            }
        }

        return downPeriod;
    }
       
    public static List<FromTo> GetSpecificValuePeriod(List<NameValueDate> actual, int specificValue)
    {
        var upPeriod = new List<FromTo>();

        DateTime? fromDate = null;
        DateTime? toDate = null;
        for (var i = 0; i < actual.Count; i++)
        {
            var item = actual[i];

            if (item.Value == specificValue)
            {
                if (fromDate == null)
                {
                    fromDate = item.Date;
                    toDate = item.Date;
                }
                else
                {
                    toDate = item.Date;
                }

                if (i == actual.Count - 1)
                {
                    upPeriod.Add(new FromTo
                    {
                        From = fromDate.Value,
                        To = toDate.Value
                    });
                    fromDate = toDate = null;
                }
            }
            else
            {
                if (fromDate.HasValue)
                {
                    upPeriod.Add(new FromTo
                    {
                        From = fromDate.Value,
                        To = toDate.Value
                    });
                    fromDate = toDate = null;
                }
            }
        }

        return upPeriod;
    }

    public static List<FromTo> GetUpPeriod(List<NameValueDate> actual)
    {
        var upPeriod = new List<FromTo>();

        DateTime? fromDate = null;
        DateTime? toDate = null;
        for (var i = 0; i < actual.Count; i++)
        {
            var item = actual[i];

            if (item.Value > 0)
            {
                if (fromDate == null)
                {
                    fromDate = item.Date;
                    toDate = item.Date;
                }
                else
                {
                    toDate = item.Date;
                }

                if (i == actual.Count - 1)
                {
                    upPeriod.Add(new FromTo
                    {
                        From = fromDate.Value,
                        To = toDate.Value
                    });
                    fromDate = toDate = null;
                }
            }
            else
            {
                if (fromDate.HasValue)
                {
                    upPeriod.Add(new FromTo
                    {
                        From = fromDate.Value,
                        To = toDate.Value
                    });
                    fromDate = toDate = null;
                }
            }
        }

        return upPeriod;
    }   
}