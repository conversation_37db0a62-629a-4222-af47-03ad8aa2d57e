using AEPExplorer.Data.EF;
using AEPExplorer.Model.Domain;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Domain;

public static class SharedDomain
{
    public static async Task<List<ValueLabel>> GetValueLabel<TEntity>(this AepExplorerDbContext context, List<Guid> ids, CancellationToken cancellationToken) where TEntity : BaseEntity
    {
        var result = await context.Set<TEntity>().Where(x => ids.Contains(x.Id))
            .AsNoTracking()
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);

        return result;
    }

    public static async Task<List<ValueLabel>> GetValueLabel<TEntity>(this DbSet<TEntity> entities, List<Guid> ids, CancellationToken cancellationToken) where TEntity : BaseEntity
    {
        var result = await entities.Where(x => ids.Contains(x.Id))
            .AsNoTracking()
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);

        return result;
    }

    public static Dictionary<Guid, double> GetNominalPowerById(this DbSet<Turbine> turbines, Expression<Func<Turbine, Guid>> predicate, Query query)
    {
        var nominalPowerData = turbines.GetFilteredTurbines(query)
            .Include(x=>x.Site.Country)
            .Select(x => new
            {
                Key = predicate.Compile()(x),
                Value = x.NominalPower,
            })
            .AsNoTracking()
            .AsEnumerable()
            .GroupBy(x => x.Key);

        return nominalPowerData.ToDictionary(x => x.Key, x => x.Sum(s => s.Value));
    }

    internal static IQueryable<Turbine> GetFilteredTurbines(this DbSet<Turbine> turbines, Query query)
    {
        return turbines
            .Where(x =>
                (query.Customer == null || query.Customer.Count == 0 || (x.CustomerId.HasValue && query.Customer.Contains(x.CustomerId.Value)))
                && (query.Region.Count == 0 || query.Region.Contains(x.Site.Country.RegionId))
                && (query.Country.Count == 0 || query.Country.Contains(x.Site.CountryId))
                && (query.Site.Count == 0 || query.Site.Contains(x.SiteId))
                && (query.TurbineId.Count == 0 || query.TurbineId.Contains(x.Id)));
    }
}