using AEPExplorer.Model.Response;
using MigraDocCore.DocumentObjectModel;

namespace AEPExplorer.Service;

public static class LossesData
{
    public static Color GetColorForCategory(string category, int level)
    {
        if (level == 2)
        {
            switch (category)
            {
                case "Operative": return new Color(123, 133, 197);
                case "Non Operative": return new Color(101, 101, 101);
                default: return new Color(0, 0, 0);
            }
        }

        if (level == 3)
        {
            switch (category)
            {
                case "Forced Outage": return new Color(151, 151, 151);
                case "Scheduled Maintenance": return new Color(151, 151, 151);
                case "Planned Corrective Action": return new Color(151, 151, 151);
                case "Out Of Service": return new Color(161, 170, 224);
                case "In Service": return new Color(161, 170, 224);
                default: return new Color(0, 0, 0);
            }
        }

        if (level == 4)
        {
            switch (category)
            {
                case "Forced Outage": return new Color(151, 151, 151);
                case "Scheduled Maintenance": return new Color(151, 151, 151);
                case "Planned Corrective Action": return new Color(151, 151, 151);
                case "Out Of Electrical Specification": return new Color(196, 204, 248);
                case "Technical Standby": return new Color(196, 204, 248);
                case "Out Of Environmental Specification": return new Color(196, 204, 248);
                case "Requested Shutdown": return new Color(196, 204, 248);
                case "Partial Performance": return new Color(196, 204, 248);
                case "Full Performance": return new Color(196, 204, 248);
                default: return new Color(0, 0, 0);
            }
        }

        if (level == 5)
        {
            switch (category)
            {
                case "Stops": return new Color(255, 125, 255);
                case "Alarms": return new Color(56, 129, 255);
                case "Other Forced Outage": return new Color(216, 222, 55);
                case "Preventive": return new Color(117, 119, 137);
                case "Large Corrective": return new Color(117, 63, 33);
                case "Planned Corrective": return new Color(250, 185, 0);                    
                case "Unplanned Corrective": return new Color(20, 205, 150);
                case "Ambient": return new Color(0, 153, 153);
                case "Ambient Standby": return new Color(7, 229, 222);
                case "Degraded": return new Color(255, 76, 91);
                case "Derated": return new Color(12, 242, 93);
                case "Regulation": return new Color(27, 21, 52);
                case "Client": return new Color(185, 187, 202);
                case "Other Standby": return new Color(112, 68, 162);
                case "Grid": return new Color(18, 78, 151);
                case "Not Production In Service": return new Color(0, 0, 0);
                case "Production Not In Service": return new Color(0, 0, 0);
                default: return new Color(0, 0, 0);
            }
        }

        return new Color(0, 0, 0);
    }

    public static iText.Kernel.Colors.DeviceRgb GetDeviceRgbForCategory(string category, int level)
    {
        if (level == 2)
        {
            switch (category)
            {
                case "Operative": return new iText.Kernel.Colors.DeviceRgb(123, 133, 197);
                case "Non Operative": return new iText.Kernel.Colors.DeviceRgb(101, 101, 101);
                default: return new iText.Kernel.Colors.DeviceRgb(0, 0, 0);
            }
        }

        if (level == 3)
        {
            switch (category)
            {
                case "Forced Outage": return new iText.Kernel.Colors.DeviceRgb(151, 151, 151);
                case "Scheduled Maintenance": return new iText.Kernel.Colors.DeviceRgb(151, 151, 151);
                case "Planned Corrective Action": return new iText.Kernel.Colors.DeviceRgb(151, 151, 151);
                case "Out Of Service": return new iText.Kernel.Colors.DeviceRgb(161, 170, 224);
                case "In Service": return new iText.Kernel.Colors.DeviceRgb(161, 170, 224);
                default: return new iText.Kernel.Colors.DeviceRgb(0, 0, 0);
            }
        }

        if (level == 4)
        {
            switch (category)
            {
                case "Forced Outage": return new iText.Kernel.Colors.DeviceRgb(151, 151, 151);
                case "Scheduled Maintenance": return new iText.Kernel.Colors.DeviceRgb(151, 151, 151);
                case "Planned Corrective Action": return new iText.Kernel.Colors.DeviceRgb(151, 151, 151);
                case "Out Of Electrical Specification": return new iText.Kernel.Colors.DeviceRgb(196, 204, 248);
                case "Technical Standby": return new iText.Kernel.Colors.DeviceRgb(196, 204, 248);
                case "Out Of Environmental Specification": return new iText.Kernel.Colors.DeviceRgb(196, 204, 248);
                case "Requested Shutdown": return new iText.Kernel.Colors.DeviceRgb(196, 204, 248);
                case "Partial Performance": return new iText.Kernel.Colors.DeviceRgb(196, 204, 248);
                case "Full Performance": return new iText.Kernel.Colors.DeviceRgb(196, 204, 248);
                default: return new iText.Kernel.Colors.DeviceRgb(0, 0, 0);
            }
        }

        if (level == 5)
        {
            switch (category)
            {
                case "Stops": return new iText.Kernel.Colors.DeviceRgb(255, 125, 255);
                case "Alarms": return new iText.Kernel.Colors.DeviceRgb(56, 129, 255);
                case "Other Forced Outage": return new iText.Kernel.Colors.DeviceRgb(216, 222, 55);
                case "Preventive": return new iText.Kernel.Colors.DeviceRgb(117, 119, 137);
                case "Large Corrective": return new iText.Kernel.Colors.DeviceRgb(117, 63, 33);
                case "Planned Corrective": return new iText.Kernel.Colors.DeviceRgb(250, 185, 0);
                case "Unplanned Corrective": return new iText.Kernel.Colors.DeviceRgb(20, 205, 150);
                case "Ambient": return new iText.Kernel.Colors.DeviceRgb(0, 153, 153);
                case "Ambient Standby": return new iText.Kernel.Colors.DeviceRgb(7, 229, 222);
                case "Degraded": return new iText.Kernel.Colors.DeviceRgb(255, 76, 91);
                case "Derated": return new iText.Kernel.Colors.DeviceRgb(12, 242, 93);
                case "Regulation": return new iText.Kernel.Colors.DeviceRgb(27, 21, 52);
                case "Client": return new iText.Kernel.Colors.DeviceRgb(185, 187, 202);
                case "Other Standby": return new iText.Kernel.Colors.DeviceRgb(112, 68, 162);
                case "Grid": return new iText.Kernel.Colors.DeviceRgb(18, 78, 151);
                case "Not Production In Service": return new iText.Kernel.Colors.DeviceRgb(0, 0, 0);
                case "Production Not In Service": return new iText.Kernel.Colors.DeviceRgb(0, 0, 0);
                default: return new iText.Kernel.Colors.DeviceRgb(0, 0, 0);
            }
        }

        return new iText.Kernel.Colors.DeviceRgb(0, 0, 0);
    }

    public static System.Drawing.Color GetSystemDrawingColorForCategory(string category, int level)
    {
        if (level == 2)
        {
            switch (category)
            {
                case "Operative": return System.Drawing.Color.FromArgb(123, 133, 197);
                case "Non Operative": return System.Drawing.Color.FromArgb(101, 101, 101);
                default: return System.Drawing.Color.FromArgb(0, 0, 0);
            }
        }

        if (level == 3)
        {
            switch (category)
            {
                case "Forced Outage": return System.Drawing.Color.FromArgb(151, 151, 151);
                case "Scheduled Maintenance": return System.Drawing.Color.FromArgb(151, 151, 151);
                case "Planned Corrective Action": return System.Drawing.Color.FromArgb(151, 151, 151);
                case "Out Of Service": return System.Drawing.Color.FromArgb(161, 170, 224);
                case "In Service": return System.Drawing.Color.FromArgb(161, 170, 224);
                default: return System.Drawing.Color.FromArgb(0, 0, 0);
            }
        }

        if (level == 4)
        {
            switch (category)
            {
                case "Forced Outage": return System.Drawing.Color.FromArgb(151, 151, 151);
                case "Scheduled Maintenance": return System.Drawing.Color.FromArgb(151, 151, 151);
                case "Planned Corrective Action": return System.Drawing.Color.FromArgb(151, 151, 151);
                case "Out Of Electrical Specification": return System.Drawing.Color.FromArgb(196, 204, 248);
                case "Technical Standby": return System.Drawing.Color.FromArgb(196, 204, 248);
                case "Out Of Environmental Specification": return System.Drawing.Color.FromArgb(196, 204, 248);
                case "Requested Shutdown": return System.Drawing.Color.FromArgb(196, 204, 248);
                case "Partial Performance": return System.Drawing.Color.FromArgb(196, 204, 248);
                case "Full Performance": return System.Drawing.Color.FromArgb(196, 204, 248);
                default: return System.Drawing.Color.FromArgb(0, 0, 0);
            }
        }

        if (level == 5)
        {
            switch (category)
            {
                case "Stops": return System.Drawing.Color.FromArgb(255, 125, 255);
                case "Alarms": return System.Drawing.Color.FromArgb(56, 129, 255);
                case "Other Forced Outage": return System.Drawing.Color.FromArgb(216, 222, 55);
                case "Preventive": return System.Drawing.Color.FromArgb(117, 119, 137);
                case "Large Corrective": return System.Drawing.Color.FromArgb(117, 63, 33);
                case "Planned Corrective": return System.Drawing.Color.FromArgb(250, 185, 0);
                case "Unplanned Corrective": return System.Drawing.Color.FromArgb(20, 205, 150);
                case "Ambient": return System.Drawing.Color.FromArgb(0, 153, 153);
                case "Ambient Standby": return System.Drawing.Color.FromArgb(7, 229, 222);
                case "Degraded": return System.Drawing.Color.FromArgb(255, 76, 91);
                case "Derated": return System.Drawing.Color.FromArgb(12, 242, 93);
                case "Regulation": return System.Drawing.Color.FromArgb(27, 21, 52);
                case "Client": return System.Drawing.Color.FromArgb(185, 187, 202);
                case "Other Standby": return System.Drawing.Color.FromArgb(112, 68, 162);
                case "Grid": return System.Drawing.Color.FromArgb(18, 78, 151);
                case "Not Production In Service": return System.Drawing.Color.FromArgb(0, 0, 0);
                case "Production Not In Service": return System.Drawing.Color.FromArgb(0, 0, 0);
                default: return System.Drawing.Color.FromArgb(0, 0, 0);
            }
        }

        return System.Drawing.Color.FromArgb(0, 0, 0);
    }

    public class NameValuePercentageWithChildrenColor : NameValuePercentageWithChildren
    {
        public Color Color { get; set; }
    }
}