using System.Collections.ObjectModel;
using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service;

public static class CategoryHelper
{
    public static readonly Guid RUN_CATEGORY_ID = ConvertExtensions.GuidMd5("mainRun");
    public static readonly Guid RUN_CATEGORY_ID_FULL_PERFORMANCE = new("*************-5b89-820f-f4da6418dd10");
    public static readonly Guid RUN_SUBCATEGORY_ID = ConvertExtensions.GuidMd5("subRun");
    public static readonly Guid LEVEL3_OUT_OF_SERVICE_ID = ConvertExtensions.GuidMd5("level3OUT OF SERVICE (IAOOS)");
    public static readonly Guid LEVEL3_IN_SERVICE_ID = ConvertExtensions.GuidMd5("level3IN SERVICE (IAOS)");
    public static readonly ReadOnlyCollection<string> OEM_LIST = new(["Siemens", "Gamesa", "SGRE"]);
    public static readonly string OPERATIVE_CATEGORY_LABEL = "OPERATIVE (IAO)";
    public static readonly string NONOPERATIVE_CATEGORY_LABEL = "NON-OPERATIVE (IANO)";
    public static readonly Guid ICE_IEA_DECREASED_PRODUCTION = ConvertExtensions.GuidMd5("subIce. [IEa] Decreased production");
    public static readonly Guid ICE_IEB_STANDSTILL = ConvertExtensions.GuidMd5("subIce. [IEb] Standstill");
    public static readonly Guid ICE_IEC_OVERPRODUCTION = ConvertExtensions.GuidMd5("subIce. [IEc] Overproduction");

    public static readonly ReadOnlyCollection<Guid> ICE_TURBINE_CATEGORIES = new([
        ConvertExtensions.GuidMd5("subIce. No alarm - Operating with OWI-AO"),
        ConvertExtensions.GuidMd5("subIce. Operating with anti-icing"),
        ConvertExtensions.GuidMd5("subIce. Operating with OWI-AO"),
        ConvertExtensions.GuidMd5("subIce. Other"),
        ConvertExtensions.GuidMd5("subIce. Stopped"),
        ConvertExtensions.GuidMd5("subIce. Stopped and de-icing"),
        ConvertExtensions.GuidMd5("subIce. Stopped by OWI strategy"),
        ConvertExtensions.GuidMd5("subIce. Stopped. Blades shake"),
        ConvertExtensions.GuidMd5("subIce. Stopped. Fault in nacelle wind sensors"),
        ConvertExtensions.GuidMd5("subIce. Stopped. Waiting for release"),
        ConvertExtensions.GuidMd5("subIce. Work Order"),
        ConvertExtensions.GuidMd5("subRemote stop Ice")
    ]);

    public static readonly ReadOnlyDictionary<Guid, string> ICE_ESTIMATE_CATEGORIES = new(new Dictionary<Guid, string>
    {
        { ConvertExtensions.GuidMd5("subIce. [IEa] Decreased production"),"Ice. [IEa] Decreased production" }, //[999991] Ice. [IEa] Decreased production
        { ConvertExtensions.GuidMd5("subIce. [IEb] Standstill"), "Ice. [IEb] Standstill" }, //[999992] Ice. [IEb] Standstill
        { ConvertExtensions.GuidMd5("subIce. [IEc] Overproduction"),"Ice. [IEc] Overproduction" } //[999993] Ice. [IEc] Overproduction
    });

    public static readonly ReadOnlyDictionary<Guid, string> ICE_ESTIMATE_CATEGORIES_ONLY_LOSSES = new(new Dictionary<Guid, string>
    {
        { ConvertExtensions.GuidMd5("subIce. [IEa] Decreased production"),"Ice. [IEa] Decreased production" }, //[999991] Ice. [IEa] Decreased production
        { ConvertExtensions.GuidMd5("subIce. [IEb] Standstill"), "Ice. [IEb] Standstill" }, //[999992] Ice. [IEb] Standstill
    });

    public static readonly Guid OPERATIVE_CATEGORY = ConvertExtensions.GuidMd5("operative");
    public static readonly Guid NON_OPERATIVE_CATEGORY = ConvertExtensions.GuidMd5("nonOperative");
    public static readonly Guid DERATED_CATEGORY = ConvertExtensions.GuidMd5("mainDerated");
    public static readonly Guid DEGRADED_CATEGORY = ConvertExtensions.GuidMd5("mainDegraded");
    public static readonly Guid ALARMS_CATEGORY = ConvertExtensions.GuidMd5("mainAlarms");
    public static readonly Guid STOPS_CATEGORY = ConvertExtensions.GuidMd5("mainStops");
    public static readonly IReadOnlyList<Guid> RunSubcategories = new List<Guid>
    {
        ConvertExtensions.GuidMd5("subRun"),
        ConvertExtensions.GuidMd5("subPowerRed - PowerBoost"),
        ConvertExtensions.GuidMd5("subPowerRed - YewOffset"),
        ConvertExtensions.GuidMd5("subPowerRed - Others")
    };


    public static List<NameValuePercentageWithChildren> SumLossesPerCategory(List<SubcategoryProduction> subcategoryData, List<BasicCategoryData> categoriesStructure, List<Guid> selectedCategoryIds,
        double totalEnergyPotential)
    {
        var result = new List<NameValuePercentageWithChildren>();

        foreach (var category in categoriesStructure)
        {
            if (selectedCategoryIds.Count == 0 || selectedCategoryIds.Contains(category.Id) || category.Subcategories.Any(sub => selectedCategoryIds.Contains(sub.Id)))
            {
                var loss = subcategoryData
                    .Where(x => category.Id == x.Id || category.Subcategories.Select(y => y.Id).Contains(x.Id))
                    .Sum(x => x.EnergyPotential - x.ActualProduction);

                result.Add(new NameValuePercentageWithChildren
                {
                    Id = category.Id,
                    Name = category.Name,
                    Value = loss,
                    Percentage = ConvertExtensions.CalculatePercentage(loss, totalEnergyPotential)
                });
            }
        }

        return result;
    }

    public static List<LossLevel> CreateLossLevel(List<LossLevel> lossLevelList, List<LossLevel> children)
    {
        var result = new List<LossLevel>();
        foreach (var lossLevel in lossLevelList)
        {
            var lossLevelChildren = children.Where(x => x.ParentId == lossLevel.Id).ToList();
            if (lossLevelChildren.Count > 0)
            {
                result.Add(new LossLevel
                {
                    Id = lossLevel.Id,
                    Name = lossLevel.Name,
                    ParentId = lossLevel.ParentId,
                    Value = 0,
                    Percentage = 0,
                    DurationInHours = 0,
                    Children = lossLevelChildren
                });
            }
        }

        return result;
    }

    public static List<Guid> GetCategories(List<Guid> categoryQuery, AepExplorerDbContext dbContext)
    {
        if (categoryQuery.Count == 0)
        {
            return dbContext.Categories.Select(x => x.Id).ToList();
        }

        if (categoryQuery.Contains(OPERATIVE_CATEGORY))
        {
            var operative = dbContext.Categories
                .Where(x => x.Level4.Level3Id == LEVEL3_IN_SERVICE_ID
                            || x.Level4.Level3Id == LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            categoryQuery.Remove(OPERATIVE_CATEGORY);
            categoryQuery.AddRange(operative);
        }

        if (categoryQuery.Contains(NON_OPERATIVE_CATEGORY))
        {
            var nonOperative = dbContext.Categories
                .Where(x => x.Level4.Level3Id != LEVEL3_IN_SERVICE_ID
                            && x.Level4.Level3Id != LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            categoryQuery.Remove(NON_OPERATIVE_CATEGORY);
            categoryQuery.AddRange(nonOperative);
        }

        return categoryQuery;
    }

    public static List<Guid> GetSubCategories(List<Guid> categoryQuery, AepExplorerDbContext dbContext)
    {
        if (categoryQuery.Count == 0)
        {
            return dbContext.Subcategories.Select(x => x.Id).ToList();
        }

        if (categoryQuery.Contains(OPERATIVE_CATEGORY))
        {
            var operative = dbContext.Subcategories
                .Where(x => x.Category.Level4.Level3Id == LEVEL3_IN_SERVICE_ID
                            || x.Category.Level4.Level3Id == LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            categoryQuery.Remove(OPERATIVE_CATEGORY);
            categoryQuery.AddRange(operative);
        }

        if (categoryQuery.Contains(NON_OPERATIVE_CATEGORY))
        {
            var nonOperative = dbContext.Subcategories
                .Where(x => x.Category.Level4.Level3Id != LEVEL3_IN_SERVICE_ID
                            && x.Category.Level4.Level3Id != LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            categoryQuery.Remove(NON_OPERATIVE_CATEGORY);
            categoryQuery.AddRange(nonOperative);
        }

        var categories = dbContext.Categories.Where(x => categoryQuery.Contains(x.Id)).Select(x => x.Id).ToList();
        var subcategories = dbContext.Subcategories.Where(x => categories.Contains(x.CategoryId)).Select(x => x.Id).ToList();

        foreach (var category in categories)
        {
            categoryQuery.Remove(category);
        }

        categoryQuery.AddRange(subcategories);
        return categoryQuery;
    }
}