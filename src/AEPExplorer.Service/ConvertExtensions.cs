using System.Security.Cryptography;
using System.Text;
using AEPExplorer.Model.Enums;

namespace AEPExplorer.Service;

public static class ConvertExtensions
{
    public static double Round(this double value, int decimals)
    {
        return Math.Round(value, decimals);
    }

    public static double Round(this double? value, int decimals)
    {
        return Math.Round(value ?? 0, decimals);
    }

    public static double ToPercentage(this double value, int decimals = 2)
    {
        return Math.Round(value * 100, decimals);
    }

    /// <summary>
    /// Calculates percentage with optional limiting to 100%
    /// </summary>
    /// <param name="numerator">Value to calculate percentage from</param>
    /// <param name="denominator">Total value</param>
    /// <param name="decimals">Number of decimal places</param>
    /// <param name="limited">If true, the result is limited to 100%</param>
    /// <returns>Calculated percentage or 0 if denominator is 0 or numerator is null</returns>
    public static double CalculatePercentage(double? numerator, double denominator, int decimals = 2, bool limited = false)
    {
        if (!numerator.HasValue || Math.Abs(denominator) < double.Epsilon)
        {
            return 0.0;
        }

        var result = numerator.Value / denominator * 100;
        return limited ? Math.Min(100.0, Math.Round(result, decimals)) : Math.Round(result, decimals);
    }

    /// <summary>
    /// Calculates percentage with optional limiting to 100% and returns null for invalid inputs
    /// </summary>
    /// <param name="numerator">Value to calculate percentage from</param>
    /// <param name="denominator">Total value</param>
    /// <param name="decimals">Number of decimal places</param>
    /// <param name="limited">If true, the result is limited to 100%</param>
    /// <returns>Calculated percentage or null if denominator is 0 or numerator is null</returns>
    public static double? CalculatePercentageNullable(double? numerator, double denominator, int decimals = 2, bool limited = false)
    {
        if (!numerator.HasValue || Math.Abs(denominator) < double.Epsilon)
        {
            return null;
        }
    
        // Reuse the calculation logic from CalculatePercentage
        return CalculatePercentage(numerator, denominator, decimals, limited);
    }

    public static string ToDateInterval(this DateTime value, DateInterval dateInterval)
    {
        var cultureInfo = DateTimeFormatInfo.CurrentInfo;
        var calendar = cultureInfo.Calendar;

        return dateInterval switch
        {
            DateInterval.Day => value.ToShortDateString(),
            DateInterval.Week => $"{calendar.GetWeekOfYear(value, cultureInfo.CalendarWeekRule, cultureInfo.FirstDayOfWeek):00}/{value:yyyy}",
            DateInterval.Month => value.ToString("MM/yyyy"),
            DateInterval.Quarter => value.ToString("MM/yyyy"),
            DateInterval.Year => value.ToString("yyyy"),
            _ => ""
        };
    }

    public static string Md5(string input, bool isLowercase = false, bool isOldVersion = true)
    {
        if (isOldVersion)
        {
            var byteHash = MD5.HashData(Encoding.Unicode.GetBytes(input));
            var hash = BitConverter.ToString(byteHash).Replace("-", "");
            return (isLowercase) ? hash.ToLower() : hash;
        }
        else
        {
            var cleanInput = new string(input.ToUpperInvariant()
                .Where(char.IsLetterOrDigit)
                .ToArray());
            var hash = BitConverter.ToString(MD5.HashData(Encoding.UTF8.GetBytes(cleanInput))).Replace("-", "");
            return isLowercase ? hash.ToLower() : hash;
        }
    }

    public static Guid GuidMd5(string input, bool isLowercase = false, bool isOldVersion = true)
    {
        return new Guid(Md5(input, isLowercase, isOldVersion));
    }

    public static string GetDelimiter(DelimiterEnum delimiter)
    {
        return delimiter switch
        {
            DelimiterEnum.Comma => ",",
            DelimiterEnum.Tab => "\t",
            DelimiterEnum.Semicolon => ";",
            _ => ";"
        };
    }

    public static double? GetAge(this DateTime? date)
    {
        if (!date.HasValue || date.Value == DateTime.MinValue || date.Value > DateTime.Today)
        {
            return null;
        }
        
        var today = DateTime.Today;
        var totalDays = (today - date.Value.Date).TotalDays;
        var age = totalDays / 365.25; 
        
        return age.Round(2);
    }
}