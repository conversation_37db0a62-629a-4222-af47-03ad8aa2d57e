using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Table;

public class TableCategoriesQuery : MediatR.IRequest<TableCategories>
{
    public Guid Id { get; set; }
    public Query Query { get; set; }
    public FilterByEnum FilterBy { get; set; }
}

public class TableCategoriesQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<TableCategoriesQuery, TableCategories>
{
    public async Task<TableCategories> Handle(TableCategoriesQuery request, CancellationToken cancellationToken)
    {
        var result = new TableCategories
        {
            Id = request.Id,
            Categories = []
        };

        request.Query.AppendFilterQuery(request.FilterBy, request.Id);

        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };
        var aggregations = new Dictionary<string, IAggregationContainer>();
        foreach (var propertyName in sumPropertyNames)
        {
            aggregations.Add(propertyName, new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });
        }
        
        var categoryStructure = await dbContext.Categories.Select(x => new
            {
                x.Id,
                x.Name,
                Subcategories = x.Subcategories.Select(s => s.Id)
            })
            .ToListAsync(cancellationToken);

        if (request.Query.Category.Contains(CategoryHelper.OPERATIVE_CATEGORY))
        {
            request.Query.Category.Remove(CategoryHelper.OPERATIVE_CATEGORY);
            var operative = dbContext.Categories
                .Where(x => x.Level4.Level3Id == CategoryHelper.LEVEL3_IN_SERVICE_ID
                            || x.Level4.Level3Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            request.Query.Category.AddRange(operative);
        }

        if (request.Query.Category.Contains(CategoryHelper.NON_OPERATIVE_CATEGORY))
        {
            request.Query.Category.Remove(CategoryHelper.NON_OPERATIVE_CATEGORY);
            var nonOperative = dbContext.Categories
                .Where(x => x.Level4.Level3Id != CategoryHelper.LEVEL3_IN_SERVICE_ID
                            && x.Level4.Level3Id != CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            request.Query.Category.AddRange(nonOperative);
        }

        var categorySumItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, nameof(AepReadData.SubcategoryId), aggregations);

        var totalEnergyRequest = request.Query.DeepCopy();
        totalEnergyRequest.Category = [];
        var totalEnergyPotential = await ElasticsearchSumService.SumAsync(elasticClient, totalEnergyRequest, p => p.Field(x => x.EnergyPotential), lossesType: LossesTypeEnum.TotalLosses);
        var subcategoryIds = categorySumItems.Where(x => x.Key != CategoryHelper.OPERATIVE_CATEGORY
                                                         && x.Key != CategoryHelper.NON_OPERATIVE_CATEGORY
                                                         && x.Key != CategoryHelper.RUN_CATEGORY_ID)
            .Select(x => new
            {
                Id = x.Key,
                ActualProduction = x.Value[nameof(AepReadData.ActualEnergy)],
                EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)]
            }).ToList();


        foreach (var category in categoryStructure)
        {
            var categorySumItem = subcategoryIds.FirstOrDefault(x => x.Id == category.Id);
            var losses = categorySumItem?.EnergyPotential - categorySumItem?.ActualProduction
                         ?? subcategoryIds.Where(x => category.Subcategories.Contains(x.Id)).Sum(x => x.EnergyPotential - x.ActualProduction);

            if (losses > 0)
            {
                result.Categories.Add(new NameValuePercentageWithChildren
                {
                    Id = category.Id,
                    Name = category.Name,
                    Value = losses.Round(2),
                    Percentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential),
                    Children = []
                });
            }
        }

        return result;
    }
}