using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Domain;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Table;

public class TableTotalLossesCountryQuery : MediatR.IRequest<TableTotalModelResponse<TableTotalLossesCountryModel>>
{
    public Query Query { get; init; }
}

public class TableTotalLossesCountryQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<TableTotalLossesCountryQuery, TableTotalModelResponse<TableTotalLossesCountryModel>>
{
    public async Task<TableTotalModelResponse<TableTotalLossesCountryModel>> Handle(TableTotalLossesCountryQuery request, CancellationToken cancellationToken)
    {
        var data = new List<TableTotalLossesCountryModel>();
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });

        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE,
                Field = new Field("turbineId")
            }
        });

        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, nameof(AepReadData.CountryId), aggregations);

        var fieldPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage),
        };

        aggregations = fieldPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });

        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE,
                Field = new Field("turbineId")
            }
        });

        var queryTotal = request.Query.DeepCopy();
        queryTotal.Category = [];
        var totalItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, queryTotal, nameof(AepReadData.CountryId), aggregations,
            lossesType: LossesTypeEnum.TotalLosses
        );

        var fullPerformanceItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, queryTotal, nameof(AepReadData.CountryId), aggregations,
            lossesType: LossesTypeEnum.RunTopic
        );

        var itemPropertyIds = items.Select(x => x.Key).ToList();
        var countries = await dbContext.Countries.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
        {
            x.Id,
            x.Name,
            RegionName = x.Region.Name
        }).ToListAsync(cancellationToken: cancellationToken);

        var nominalPowerData = dbContext.Turbines.GetNominalPowerById(x => x.Site.CountryId, request.Query);

        foreach (var item in items)
        {
            var actualEnergy = item.Value[nameof(AepReadData.ActualEnergy)];
            var energyPotential = item.Value[nameof(AepReadData.EnergyPotential)];
            var losses = energyPotential - actualEnergy;
            fullPerformanceItems.TryGetValue(item.Key, out var fullPerformance);
            var actualEnergyInFullPerformance = fullPerformance != null ? fullPerformance[nameof(AepReadData.ActualEnergy)] : 0;
            var energyPotentialInFullPerformance = fullPerformance != null ? fullPerformance[nameof(AepReadData.EnergyPotential)] : 0;
            var numberOfTurbines = (int)item.Value["NumberOfTurbines"];
            var totalNumberOfTurbines = totalItems[item.Key]["NumberOfTurbines"];
            var totalDataCoverage = totalItems[item.Key][nameof(AepReadData.DataCoverage)] / (totalNumberOfTurbines * request.Query.TotalDays);
            var totalActualEnergy = totalItems[item.Key][nameof(AepReadData.ActualEnergy)];
            var totalEnergyPotential = totalItems[item.Key][nameof(AepReadData.EnergyPotential)];
            var country = countries.FirstOrDefault(x => x.Id == item.Key);
            var nominalPower = nominalPowerData[item.Key];
            var capacityFactorActual = nominalPower > 0 ? totalActualEnergy / (request.Query.TotalDays * 24) / nominalPower : 0;
            var capacityFactorPotential = nominalPower > 0 ? totalEnergyPotential / (request.Query.TotalDays * 24) / nominalPower : 0;
            data.Add(new TableTotalLossesCountryModel
            {
                Id = item.Key.ToString(),
                Region = country != null ? country.RegionName : "Unknown",
                Country = country != null ? country.Name : "Unknown",
                ActualProduction = totalActualEnergy,
                ActualProductionPercentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential),
                Losses = losses,
                LossesPercentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential),
                Coverage = totalDataCoverage.ToPercentage(),
                CapacityFactorActual = capacityFactorActual,
                CapacityFactorPotential = capacityFactorPotential,
                ProductionRatio = ConvertExtensions.CalculatePercentage(actualEnergyInFullPerformance, energyPotentialInFullPerformance),
                NumberOfTurbines = numberOfTurbines
            });
        }

        return new TableTotalModelResponse<TableTotalLossesCountryModel>
        {
            Data = data.SortBy(request.Query.SortColumn, request.Query.SortDirection).Skip(ElasticsearchConstants.PAGE_SIZE * (request.Query.Page - 1)).Take(ElasticsearchConstants.PAGE_SIZE).ToList(),
            TotalRows = items.Count,
            TotalPages = items.Count / ElasticsearchConstants.PAGE_SIZE + 1
        };
    }
}