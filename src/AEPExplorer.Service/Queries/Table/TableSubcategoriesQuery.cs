using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Table;

public class TableSubcategoriesQuery : MediatR.IRequest<TableSubcategories>
{
    public Guid Id { get; init; }
    public Guid CategoryId { get; init; }
    public Query Query { get; init; }
    public FilterByEnum FilterBy { get; init; }
}

public class TableSubcategoriesQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<TableSubcategoriesQuery, TableSubcategories>
{
    public async Task<TableSubcategories> Handle(TableSubcategoriesQuery request, CancellationToken cancellationToken)
    {
        var result = new TableSubcategories
        {
            Id = request.Id,
            Category = request.CategoryId,
            Subcategories = []
        };
        
        request.Query.AppendFilterQuery(request.FilterBy, request.Id);

        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };
        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var totalEnergyRequest = request.Query.DeepCopy();
        totalEnergyRequest.Category = [];
        var totalEnergyPotential = await ElasticsearchSumService.SumAsync(elasticClient, totalEnergyRequest, p => p.Field(x => x.EnergyPotential), lossesType: LossesTypeEnum.TotalLosses);

        var subCategoryStructure = await dbContext.Subcategories
            .Where(x => x.CategoryId == request.CategoryId &&
                        (!request.Query.FilteredCategories || request.Query.Category.Contains(request.CategoryId) || request.Query.Category.Contains(x.Id)
                         || request.Query.Category.Contains(CategoryHelper.OPERATIVE_CATEGORY) || request.Query.Category.Contains(CategoryHelper.NON_OPERATIVE_CATEGORY)))
            .Select(x => new
            {
                x.Id,
                x.Name
            })
            .ToListAsync(cancellationToken);

        var subCategoryIds = subCategoryStructure.Select(x => x.Id).ToList();

        var subcategoryIds = new List<SimpleCategory>();
        foreach (var subCategoryIdGroup in subCategoryIds.Batch(ElasticsearchConstants.SEARCH_LIMIT))
        {
            request.Query.Category = subCategoryIdGroup.ToList();
            var categorySumItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, nameof(AepReadData.SubcategoryId), aggregations,
                lossesType: LossesTypeEnum.Detailed);
            subcategoryIds.AddRange(categorySumItems.Where(x => x.Key != CategoryHelper.OPERATIVE_CATEGORY
                                                                && x.Key != CategoryHelper.NON_OPERATIVE_CATEGORY
                                                                && x.Key != CategoryHelper.RUN_CATEGORY_ID)
                .Select(x => new SimpleCategory
                {
                    Id = x.Key,
                    ActualProduction = x.Value[nameof(AepReadData.ActualEnergy)],
                    EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)]
                }).ToList());
        }

        foreach (var category in subCategoryStructure)
        {
            var subcategory = subcategoryIds.FirstOrDefault(x => x.Id == category.Id);
            var losses = subcategory != null ? subcategory.EnergyPotential - subcategory.ActualProduction : 0;
            result.Subcategories.Add(new NameValuePercentageWithChildren
            {
                Id = category?.Id ?? Guid.NewGuid(),
                Name = category?.Name ?? "Unknown",
                Value = losses.Round(2),
                Percentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential),
            });
        }

        return result;
    }
}

internal class SimpleCategory
{
    public Guid Id { get; init; }
    public double ActualProduction { get; init; }
    public double EnergyPotential { get; init; }
}