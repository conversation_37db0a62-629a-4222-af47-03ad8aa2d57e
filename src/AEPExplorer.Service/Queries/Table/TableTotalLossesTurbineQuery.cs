using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Table;

public class TableTotalLossesTurbineQuery : MediatR.IRequest<TableTotalModelResponse<TableTotalLossesTurbineModel>>
{
    public Query Query { get; init; }
}

public class TableTotalLossesTurbineQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<TableTotalLossesTurbineQuery, TableTotalModelResponse<TableTotalLossesTurbineModel>>
{
    public async Task<TableTotalModelResponse<TableTotalLossesTurbineModel>> Handle(TableTotalLossesTurbineQuery request, CancellationToken cancellationToken)
    {
        var data = new List<TableTotalLossesTurbineModel>();
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });

        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, nameof(AepReadData.TurbineId), aggregations,
            lossesType: request.Query.FilteredCategories ? LossesTypeEnum.Detailed : LossesTypeEnum.TotalLosses);

        var fieldPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage),
        };

        aggregations = fieldPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });
                
        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE,
                Field = new Field("turbineId")
            }
        });

        var queryTotal = request.Query.DeepCopy();
        queryTotal.Category = [];
        var totalItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, queryTotal, nameof(AepReadData.TurbineId), aggregations,
            lossesType: LossesTypeEnum.TotalLosses
        );

        var fullPerformanceItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, queryTotal, nameof(AepReadData.TurbineId), aggregations,
            lossesType: LossesTypeEnum.RunTopic
        );

        var itemPropertyIds = items.Select(x => x.Key).ToList();
        var turbines = await dbContext.Turbines.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
        {
            x.Id,
            TurbineId = x.MasterDataId,
            x.Name,
            Platform = x.TurbineModel.TurbinePlatform.Name,
            SiteName = x.Site.Name,
            CountryName = x.Site.Country.Name,
            RegionName = x.Site.Country.Region.Name,
            CustomerName = x.Customer.Name,
            x.NominalPower
        }).ToListAsync(cancellationToken: cancellationToken);
        foreach (var item in items)
        {
            var actualEnergy = item.Value[nameof(AepReadData.ActualEnergy)];
            var energyPotential = item.Value[nameof(AepReadData.EnergyPotential)];
            var losses = energyPotential - actualEnergy;
            fullPerformanceItems.TryGetValue(item.Key, out var fullPerformance);
            var actualEnergyInFullPerformance = fullPerformance != null ? fullPerformance[nameof(AepReadData.ActualEnergy)] : 0;
            var energyPotentialInFullPerformance = fullPerformance != null ? fullPerformance[nameof(AepReadData.EnergyPotential)] : 0;
            var totalNumberOfTurbines = totalItems[item.Key]["NumberOfTurbines"];
            var totalDataCoverage = totalItems[item.Key][nameof(AepReadData.DataCoverage)] / (totalNumberOfTurbines * request.Query.TotalDays);
            var totalActualEnergy = totalItems[item.Key][nameof(AepReadData.ActualEnergy)];
            var totalEnergyPotential = totalItems[item.Key][nameof(AepReadData.EnergyPotential)];
            var turbine = turbines.FirstOrDefault(x => x.Id == item.Key);
            var nominalPower = turbines.Where(x => x.Id == item.Key).Sum(x => x.NominalPower);
            var capacityFactorActual = nominalPower > 0 ? totalActualEnergy / (request.Query.TotalDays * 24) / nominalPower : 0;
            var capacityFactorPotential = nominalPower > 0 ? totalEnergyPotential / (request.Query.TotalDays * 24) / nominalPower : 0;
            data.Add(new TableTotalLossesTurbineModel
            {
                Id = item.Key.ToString(),
                TurbineId = turbine?.TurbineId ?? 0,
                Customer = turbine != null && turbine.CustomerName != null ? turbine.CustomerName : "Unknown",
                Region = turbine != null ? turbine.RegionName : "Unknown",
                Country = turbine != null ? turbine.CountryName : "Unknown",
                Site = turbine != null ? turbine.SiteName : "Unknown",
                Turbine = turbine != null ? turbine.Name : "Unknown",
                Platform = turbine != null ? turbine.Platform : "Unknown",
                ActualProduction = totalActualEnergy,
                ActualProductionPercentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential),
                Losses = losses,
                LossesPercentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential),
                Coverage = totalDataCoverage.ToPercentage(),
                CapacityFactorActual = capacityFactorActual,
                CapacityFactorPotential = capacityFactorPotential,
                ProductionRatio = ConvertExtensions.CalculatePercentage(actualEnergyInFullPerformance, energyPotentialInFullPerformance)
            });
        }

        return new TableTotalModelResponse<TableTotalLossesTurbineModel>
        {
            Data = data.SortBy(request.Query.SortColumn, request.Query.SortDirection).Skip(ElasticsearchConstants.PAGE_SIZE * (request.Query.Page - 1)).Take(ElasticsearchConstants.PAGE_SIZE).ToList(),
            TotalRows = items.Count,
            TotalPages = items.Count / ElasticsearchConstants.PAGE_SIZE + 1
        };
    }
}