using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Service.Domain;

namespace AEPExplorer.Service.Queries.Table.Export;

public interface IExportFilter
{
    public Task<byte[]> GetCsv(ExportQuery query, CultureInfo cultureInfo, CancellationToken cancellationToken);
}

public class ExportFilter(AepExplorerDbContext dbContext) : IExportFilter
{
    public async Task<byte[]> GetCsv(ExportQuery query, CultureInfo cultureInfo, CancellationToken cancellationToken)
    {
        var filterCustomers = await dbContext.Customers.GetValueLabel(query.Customer, cancellationToken);
        var filterRegions = await dbContext.Regions.GetValueLabel(query.Region, cancellationToken);
        var filterCountries = await dbContext.Countries.GetValueLabel(query.Country, cancellationToken);
        var filterSites = await dbContext.Sites.GetValueLabel(query.Site, cancellationToken);
        var filterTurbines = await dbContext.Turbines.GetValueLabel(query.TurbineId, cancellationToken);
        var filterPlatforms = await dbContext.TurbinePlatforms.GetValueLabel(query.Platform, cancellationToken);
        var filterModels = await dbContext.Models.GetValueLabel(query.Model, cancellationToken);
        var filterCategories = await dbContext.Categories.GetValueLabel(query.Category, cancellationToken);
        var filterSubcategories = await dbContext.Subcategories.GetValueLabel(query.Category, cancellationToken);
        var delimiter = ConvertExtensions.GetDelimiter(query.Delimiter);
        var customers = filterCustomers.Count != 0 ? string.Join(delimiter, filterCustomers.Select(x => x.Label).ToArray()) : null;
        var regions = filterRegions.Count != 0 ? string.Join(delimiter, filterRegions.Select(x => x.Label).ToArray()) : null;
        var countries = filterCountries.Count != 0 ? string.Join(delimiter, filterCountries.Select(x => x.Label).ToArray()) : null;
        var sites = filterSites.Count != 0 ? string.Join(delimiter, filterSites.Select(x => x.Label).ToArray()) : null;
        var turbines = filterTurbines.Count != 0 ? string.Join(delimiter, filterTurbines.Select(x => x.Label).ToArray()) : null;
        var platforms = filterPlatforms.Count != 0 ? string.Join(delimiter, filterPlatforms.Select(x => x.Label).ToArray()) : null;
        var models = filterModels.Count != 0 ? string.Join(delimiter, filterModels.Select(x => x.Label).ToArray()) : null;
        var categories = filterCategories.Count != 0 ? string.Join(delimiter, filterCategories.Select(x => x.Label).ToArray()) : null;
        var subcategories = filterSubcategories.Count != 0 ? string.Join(delimiter, filterSubcategories.Select(x => x.Label).ToArray()) : null;
        
        using var filtersMemoryStream = new MemoryStream();
        await using (var writer = new StreamWriter(filtersMemoryStream))
        {
            await writer.WriteLineAsync($"Date from:{delimiter}{query.DateFrom}");
            await writer.WriteLineAsync($"Date to:{delimiter}{query.DateTo}");
            await writer.WriteLineAsync($"{nameof(query.Customer)}:{delimiter}{customers}");
            await writer.WriteLineAsync($"{nameof(query.Region)}:{delimiter}{regions}");
            await writer.WriteLineAsync($"{nameof(query.Country)}:{delimiter}{countries}");
            await writer.WriteLineAsync($"{nameof(query.Site)}:{delimiter}{sites}");
            await writer.WriteLineAsync($"Turbine:{delimiter}{turbines}");
            await writer.WriteLineAsync($"{nameof(query.Platform)}:{delimiter}{platforms}");
            await writer.WriteLineAsync($"{nameof(query.Model)}:{delimiter}{models}");
            await writer.WriteLineAsync($"{nameof(query.Category)}:{delimiter}{categories}");
            await writer.WriteLineAsync($"Subcategory:{delimiter}{subcategories}");
        }

        return filtersMemoryStream.ToArray();
    }
}