using System.Dynamic;
using System.IO.Compression;
using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;
using AEPExplorer.Service.Queries.Filter;
using CsvHelper;
using CsvHelper.Configuration;

namespace AEPExplorer.Service.Queries.Table.Export;

public class TableTotalLossesTurbineExportQuery : MediatR.IRequest<(string fileName, byte[] file)>
{
    public ExportQuery Query { get; init; }
}

public class TableTotalLossesTurbineExportQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext, IMediator mediator, IExportFilter exportFilter) : IRequestHandler<TableTotalLossesTurbineExportQuery, (string, byte[])>
{
    public async Task<(string, byte[])> Handle(TableTotalLossesTurbineExportQuery request, CancellationToken cancellationToken)
    {
        var originalQuery = request.Query.DeepCopy();
        var fieldPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage),
        };

        var aggregations = fieldPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });

        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, nameof(AepReadData.TurbineId), aggregations);

        var turbines = await dbContext.Turbines.Where(x => items.Keys.Contains(x.Id)).Select(x => new
        {
            x.Id,
            TurbineId = x.MasterDataId,
            x.Name,
            Platform = x.TurbineModel.TurbinePlatform.Name,
            SiteName = x.Site.Name,
            CountryName = x.Site.Country.Name,
            RegionName = x.Site.Country.Region.Name,
            CustomerName = x.Customer.Name,
            x.NominalPower
        }).ToListAsync(cancellationToken);

        var categoryNames = new List<ValueLabel>();
        var itemsLosses = new Dictionary<Guid, Dictionary<Guid, Dictionary<string, double>>>();
        if (request.Query.ExportLevel != ExportLevelEnum.Total)
        {
            request.Query.Category = request.Query.ExportLevel switch
            {
                ExportLevelEnum.Categories => CategoryHelper.GetCategories(request.Query.Category, dbContext),
                ExportLevelEnum.Subcategories => CategoryHelper.GetSubCategories(request.Query.Category, dbContext),
                _ => request.Query.Category
            };

            fieldPropertyNames =
            [
                nameof(AepReadData.ActualEnergy),
                nameof(AepReadData.EnergyPotential)
            ];
            aggregations = fieldPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
            {
                Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
            });

            var turbineIds = items.Keys;
            var lossesQuery = request.Query.DeepCopy();
            foreach (var turbineList in turbineIds.Batch(2000))
            {
                lossesQuery.TurbineId = turbineList.ToList();
                itemsLosses = itemsLosses.Union(await ElasticsearchSumService.SumByGroupAsync(elasticClient, lossesQuery, nameof(AepReadData.TurbineId), 
                    nameof(AepReadData.SubcategoryId), aggregations, lossesType: LossesTypeEnum.Detailed)).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);   
            }

            var categoryFilter = await mediator.Send(new CategoryFilterQuery(), cancellationToken);
            var level2List = categoryFilter.Select(x => new
            {
                SubcategoryId = x.Value,
                Name = x.Label,
            }).ToList();

            var categoryList = categoryFilter.SelectMany(x => x.Children.Select(s => new
            {
                SubcategoryId = s.Value,
                Name = s.Label,
            })).ToList();

            var subcategoryList = categoryFilter.SelectMany(x => x.Children.SelectMany(c => c.Children.Select(s => new
            {
                SubcategoryId = s.Value,
                Name = s.Label,
            }))).ToList();

            subcategoryList.AddRange(categoryList);
            subcategoryList.AddRange(level2List);
            var filteredList = subcategoryList.Where(x => request.Query.Category.Contains(x.SubcategoryId));
            categoryNames = filteredList.Select(x => new ValueLabel()
            {
                Value = x.SubcategoryId,
                Label = x.Name
            }).Distinct().ToList();
        }

        fieldPropertyNames =
        [
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage)
        ];

        aggregations = fieldPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });

        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE,
                Field = new Field("turbineId")
            }
        });

        var queryTotal = request.Query.DeepCopy();
        queryTotal.Category = [];

        var totalItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, queryTotal, nameof(AepReadData.TurbineId), aggregations,
            lossesType: LossesTypeEnum.TotalLosses
        );

        var fullPerformanceItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, queryTotal, nameof(AepReadData.TurbineId), aggregations,
            lossesType: LossesTypeEnum.RunTopic
        );

        var records = new List<dynamic>();
        foreach (var item in items)
        {
            var actualEnergy = item.Value[nameof(AepReadData.ActualEnergy)];
            var energyPotential = item.Value[nameof(AepReadData.EnergyPotential)];
            var losses = energyPotential - actualEnergy;
            fullPerformanceItems.TryGetValue(item.Key, out var fullPerformance);
            var totalActualEnergy = totalItems[item.Key][nameof(AepReadData.ActualEnergy)];
            var totalEnergyPotential = totalItems[item.Key][nameof(AepReadData.EnergyPotential)];
            var totalLosses = totalEnergyPotential - totalActualEnergy;
            var actualEnergyInFullPerformance = fullPerformance != null ? fullPerformance[nameof(AepReadData.ActualEnergy)] : 0;
            var energyPotentialInFullPerformance = fullPerformance != null ? fullPerformance[nameof(AepReadData.EnergyPotential)] : 0;
            var totalNumberOfTurbines = totalItems[item.Key]["NumberOfTurbines"];
            var totalDataCoverage = totalItems[item.Key][nameof(AepReadData.DataCoverage)] / (totalNumberOfTurbines * request.Query.TotalDays);
            var dataCoverage = item.Value[nameof(AepReadData.DataCoverage)] / request.Query.TotalDays;
            var nominalPower = turbines.Where(x => x.Id == item.Key).Sum(x => x.NominalPower);
            var capacityFactorActual = nominalPower > 0 ? actualEnergy / (request.Query.TotalDays * 24) / nominalPower : 0;
            var capacityFactorPotential = nominalPower > 0 ? energyPotential / (request.Query.TotalDays * 24) / nominalPower : 0;
            var totalCapacityFactorActual = nominalPower > 0 ? totalActualEnergy / (request.Query.TotalDays * 24) / nominalPower : 0;
            var totalCapacityFactorPotential = nominalPower > 0 ? totalEnergyPotential / (request.Query.TotalDays * 24) / nominalPower : 0;
            var turbine = turbines.FirstOrDefault(x => x.Id == item.Key);
            var obj = new ExpandoObject();
            var store = (IDictionary<string, object>)obj;
            store.Add("Customer", turbine != null ? turbine.CustomerName : "Unknown");
            store.Add("Region", turbine != null ? turbine.RegionName : "Unknown");
            store.Add("Country", turbine != null ? turbine.CountryName : "Unknown");
            store.Add("Site", turbine != null ? turbine.SiteName : "Unknown");
            store.Add("Turbine Name", turbine != null ? turbine.Name : "Unknown");
            store.Add("Turbine Id", turbine?.TurbineId ?? 0);
            store.Add("Platform", turbine != null ? turbine.Platform : "Unknown");
            store.Add("Total Data Coverage [%]", totalDataCoverage.ToPercentage());
            store.Add("Total Production [MWh]", totalActualEnergy);
            store.Add("Total Losses [MWh]", totalLosses);
            store.Add("Total Losses [%]", ConvertExtensions.CalculatePercentage(totalLosses, totalEnergyPotential));
            store.Add("Total Capacity Factor", totalCapacityFactorActual);
            store.Add("Total Potential Capacity Factor", totalCapacityFactorPotential);
            store.Add("Full Performance Production [MWh]", actualEnergyInFullPerformance);
            store.Add("Production Ratio", ConvertExtensions.CalculatePercentage(actualEnergyInFullPerformance, energyPotentialInFullPerformance));
            store.Add("Selection Data Coverage [%]", dataCoverage.ToPercentage());
            store.Add("Selection Production [MWh]", actualEnergy);
            store.Add("Selection Losses [MWh]", losses);
            store.Add("Selection Losses [%]", ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential));
            store.Add("Selection Capacity Factor", capacityFactorActual);
            store.Add("Selection Potential Capacity Factor", capacityFactorPotential);

            if (itemsLosses.Count > 0)
            {
                var itemLosses = itemsLosses[item.Key];
                foreach (var categoryId in request.Query.Category)
                {
                    var categoryName = categoryNames.FirstOrDefault(x => x.Value == categoryId);
                    if (categoryName == null)
                    {
                        continue;
                    }
                    
                    var subcategoryLoss = 0.0;
                    if (itemLosses.TryGetValue(categoryId, out var loss))
                    {
                        var itemActualProduction = loss[nameof(AepReadData.ActualEnergy)];
                        var itemEnergyPotential = loss[nameof(AepReadData.EnergyPotential)];
                        subcategoryLoss = itemEnergyPotential - itemActualProduction;
                    }

                    store.Add($"{categoryName.Label} [%]", ConvertExtensions.CalculatePercentage(subcategoryLoss, totalEnergyPotential, decimals: 8));
                }
            }

            dynamic record = obj;
            records.Add(record);
        }

        var invariantCulture = new CultureInfo(CultureInfo.InvariantCulture.LCID);
        invariantCulture.NumberFormat.NumberDecimalSeparator = request.Query.DecimalSeparator switch
        {
            DecimalSeparatorEnum.Comma => ",",
            DecimalSeparatorEnum.Dot => ".",
            _ => invariantCulture.NumberFormat.NumberDecimalSeparator
        };

        using var resultsMemoryStream = new MemoryStream();
        await using (var writer = new StreamWriter(resultsMemoryStream))
        await using (var csvWriter = new CsvWriter(writer, new CsvConfiguration(invariantCulture)
                     {
                         Delimiter = ConvertExtensions.GetDelimiter(request.Query.Delimiter)
                     }))
            await csvWriter.WriteRecordsAsync(records, cancellationToken);

        var filtersMemoryStream = await exportFilter.GetCsv(originalQuery, invariantCulture, cancellationToken);

        var timestamp = $"{DateTime.Now:yyyy-MM-dd_hh-mm-ss}";
        using var zipMemoryStream = new MemoryStream();
        using (var archive = new ZipArchive(zipMemoryStream, ZipArchiveMode.Create, false))
        {
            try
            {
                var entry = archive.CreateEntry($"results_{timestamp}.csv", CompressionLevel.Fastest);
                await using (var entryStream = entry.Open())
                {
                    await entryStream.WriteAsync(resultsMemoryStream.ToArray(), cancellationToken);
                }

                var filtersEntry = archive.CreateEntry($"filters_{timestamp}.csv", CompressionLevel.Fastest);
                await using (var filtersEntryStream = filtersEntry.Open())
                {
                    await filtersEntryStream.WriteAsync(filtersMemoryStream, cancellationToken);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        var fileName = $"aep_table_{timestamp}.zip";
        return (fileName, zipMemoryStream.ToArray());
    }
}