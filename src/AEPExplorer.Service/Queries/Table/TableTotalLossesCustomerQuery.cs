using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Table;

public class TableTotalLossesCustomerQuery : MediatR.IRequest<TableTotalModelResponse<TableTotalLossesCustomerModel>>
{
    public Query Query { get; init; }
}

public class TableTotalLossesCustomerQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<TableTotalLossesCustomerQuery, TableTotalModelResponse<TableTotalLossesCustomerModel>>
{
    public async Task<TableTotalModelResponse<TableTotalLossesCustomerModel>> Handle(TableTotalLossesCustomerQuery request, CancellationToken cancellationToken)
    {
        var data = new List<TableTotalLossesCustomerModel>();
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });

        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE,
                Field = new Field("turbineId")
            }
        });

        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, nameof(AepReadData.CustomerId), aggregations,
            lossesType: request.Query.FilteredCategories ? LossesTypeEnum.Detailed : LossesTypeEnum.TotalLosses);

        var fieldPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage),
        };

        aggregations = fieldPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });

        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE,
                Field = new Field("turbineId")
            }
        });

        var queryTotal = request.Query.DeepCopy();
        queryTotal.Category = [];
        var totalItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, queryTotal, nameof(AepReadData.CustomerId), aggregations,
            lossesType: LossesTypeEnum.TotalLosses
        );

        var fullPerformanceItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, queryTotal, nameof(AepReadData.CustomerId), aggregations,
            lossesType: LossesTypeEnum.RunTopic
        );

        var itemPropertyIds = items.Select(x => x.Key).ToList();
        var turbines = await dbContext.Turbines
            .Where(x => (request.Query.Customer.Count == 0 || (x.CustomerId.HasValue && request.Query.Customer.Contains(x.CustomerId.Value)))
                && (request.Query.Region.Count == 0 || request.Query.Region.Contains(x.Site.Country.RegionId))
                && (request.Query.Country.Count == 0 || request.Query.Country.Contains(x.Site.CountryId))
                && (request.Query.Site.Count == 0 || request.Query.Site.Contains(x.SiteId))
                && (request.Query.TurbineId.Count == 0) || request.Query.TurbineId.Contains(x.Id))
            .Select(x => new
            {
                x.CustomerId,
                x.NominalPower
            })
            .ToListAsync(cancellationToken);
        var customers = await dbContext.Customers.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
        {
            x.Id,
            x.Name,
        }).ToListAsync(cancellationToken: cancellationToken);

        foreach (var item in items)
        {
            var actualEnergy = item.Value[nameof(AepReadData.ActualEnergy)];
            var energyPotential = item.Value[nameof(AepReadData.EnergyPotential)];
            var losses = energyPotential - actualEnergy;
            fullPerformanceItems.TryGetValue(item.Key, out var fullPerformance);
            var actualEnergyInFullPerformance = fullPerformance != null ? fullPerformance[nameof(AepReadData.ActualEnergy)] : 0;
            var energyPotentialInFullPerformance = fullPerformance != null ? fullPerformance[nameof(AepReadData.EnergyPotential)] : 0;
            var numberOfTurbines = (int)item.Value["NumberOfTurbines"];
            var totalNumberOfTurbines = totalItems[item.Key]["NumberOfTurbines"];
            var totalDataCoverage = totalItems[item.Key][nameof(AepReadData.DataCoverage)] / (totalNumberOfTurbines * request.Query.TotalDays);
            var totalActualEnergy = totalItems[item.Key][nameof(AepReadData.ActualEnergy)];
            var totalEnergyPotential = totalItems[item.Key][nameof(AepReadData.EnergyPotential)];
            var customer = customers.FirstOrDefault(x => x.Id == item.Key);
            var nominalPower = turbines.Where(x => x.CustomerId == item.Key).Sum(x => x.NominalPower);
            var capacityFactorActual = nominalPower > 0 ? totalActualEnergy / (request.Query.TotalDays * 24) / nominalPower : 0;
            var capacityFactorPotential = nominalPower > 0 ? totalEnergyPotential / (request.Query.TotalDays * 24) / nominalPower : 0;
            data.Add(new TableTotalLossesCustomerModel
            {
                Id = item.Key.ToString(),
                Customer = customer != null ? customer.Name : "Unknown",
                ActualProduction = totalActualEnergy,
                ActualProductionPercentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential),
                Losses = losses,
                LossesPercentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential),
                Coverage = totalDataCoverage.ToPercentage(),
                CapacityFactorActual = capacityFactorActual,
                CapacityFactorPotential = capacityFactorPotential,
                ProductionRatio = ConvertExtensions.CalculatePercentage(actualEnergyInFullPerformance, energyPotentialInFullPerformance),
                NumberOfTurbines = numberOfTurbines
            });
        }

        return new TableTotalModelResponse<TableTotalLossesCustomerModel>
        {
            Data = data.SortBy(request.Query.SortColumn, request.Query.SortDirection).Skip(ElasticsearchConstants.PAGE_SIZE * (request.Query.Page - 1)).Take(ElasticsearchConstants.PAGE_SIZE).ToList(),
            TotalRows = items.Count,
            TotalPages = items.Count / ElasticsearchConstants.PAGE_SIZE + 1
        };
    }
}