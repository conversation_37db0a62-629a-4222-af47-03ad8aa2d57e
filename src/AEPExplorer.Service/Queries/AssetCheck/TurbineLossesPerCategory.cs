using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.AssetCheck;

public class TurbineLossesPerCategoryQuery : MediatR.IRequest<List<LossesByGroups>>
{
    public Query Query { get; init; }
}

public class TurbineLossesPerCategoryHandler : IRequestHandler<TurbineLossesPerCategoryQuery, List<LossesByGroups>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public TurbineLossesPerCategoryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext, IMediator mediator)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<LossesByGroups>> Handle(TurbineLossesPerCategoryQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DurationInHours)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer
            {
                Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
            });

        var turbinesPerformance = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, request.Query, nameof(AepReadData.TurbineId), nameof(AepReadData.SubcategoryId), aggregations,
            lossesType: LossesTypeEnum.AggregatedLosses);

        var selectedTurbines = await _dbContext.Turbines
            .Where(x => (request.Query.Site.Count == 0 || request.Query.Site.Contains(x.SiteId)) &&
                        (request.Query.TurbineId.Count == 0 || request.Query.TurbineId.Contains(x.Id)) &&
                        (request.Query.Model.Count == 0 || request.Query.Model.Contains(x.ModelId)))
            .ToListAsync(cancellationToken);

        var results = new List<LossesByGroups>();

        var categoryStructure = await _dbContext.Categories.Where(x => x.Id != CategoryHelper.RUN_CATEGORY_ID).Select(x => new
        {
            x.Id,
            x.Name
        }).ToListAsync(cancellationToken);

        foreach (var turbinePerformance in turbinesPerformance)
        {
            var categoriesPerformance = turbinePerformance.Value.Where(x => x.Key != CategoryHelper.OPERATIVE_CATEGORY && x.Key != CategoryHelper.NON_OPERATIVE_CATEGORY).ToList();
            var categoriesLosses = new List<NameValuePercentageWithChildren>();
            var totalActualEnergy = categoriesPerformance.Sum(x => x.Value[nameof(AepReadData.ActualEnergy)]);
            var totalEnergyPotential = categoriesPerformance.Sum(x => x.Value[nameof(AepReadData.EnergyPotential)]);

            foreach (var categoryPerformance in categoriesPerformance)
            {
                var energyPotential = categoryPerformance.Value[nameof(AepReadData.EnergyPotential)];
                var actualEnergy = categoryPerformance.Value[nameof(AepReadData.ActualEnergy)];
                var losses = energyPotential - actualEnergy;

                categoriesLosses.Add(new NameValuePercentageWithChildren
                {
                    Id = categoryPerformance.Key,
                    Name = categoryStructure.FirstOrDefault(x => x.Id == categoryPerformance.Key)?.Name ?? "Unknown",
                    Value = losses,
                    Percentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential),
                    Children = null
                });
            }

            var categories = categoryStructure.Select(x => new NameValuePercentageWithChildren
            {
                Id = x.Id,
                Name = x.Name,
                Value = categoriesLosses.FirstOrDefault(y => y.Id == x.Id)?.Value.Round(2) ?? 0,
                Percentage = categoriesLosses.FirstOrDefault(y => y.Id == x.Id)?.Percentage.Round(2) ?? 0,
                Children = null
            }).OrderBy(x => x.Name).ToList();

            var totalLoss = totalEnergyPotential - totalActualEnergy;

            categories.Insert(0, new NameValuePercentageWithChildren
            {
                Id = new Guid(),
                Name = "Total Loss",
                Value = totalLoss,
                Percentage = ConvertExtensions.CalculatePercentage(totalLoss, totalEnergyPotential),
                Children = null
            });

            results.Add(new LossesByGroups
            {
                Id = turbinePerformance.Key,
                ItemName = selectedTurbines.FirstOrDefault(x => x.Id == turbinePerformance.Key)?.Name ?? "Unknown",
                Categories = categories
            });
        }

        return results;
    }
}