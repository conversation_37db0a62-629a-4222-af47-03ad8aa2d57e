using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Model.Enums;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.AssetCheck;

public class ProductionAndLossesPerTurbineQuery : MediatR.IRequest<List<ProductionAndLossesPerTurbine>>
{
    public Query Query { get; init; }
}

public class ProductionAndLossesPerTurbineHandler : IRequestHandler<ProductionAndLossesPerTurbineQuery, List<ProductionAndLossesPerTurbine>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public ProductionAndLossesPerTurbineHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext, IMediator mediator)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<ProductionAndLossesPerTurbine>> Handle(ProductionAndLossesPerTurbineQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer
            {
                Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
            });

        var items = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, request.Query, nameof(AepReadData.TurbineId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION, LossesTypeEnum.TotalLosses);

        var turbineData = items.Select(x => new
        {
            Id = x.Key,
            ActualEnergy = x.Value[nameof(AepReadData.ActualEnergy)],
            EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)],
        }).ToList();

        var turbineIds = turbineData.Select(x => x.Id);

        var turbineStructure = await _dbContext.Turbines.Where(x => turbineIds.Contains(x.Id)).Select(y => new
        {
            y.Id,
            y.Name,
        }).ToListAsync(cancellationToken);

        var results = new List<ProductionAndLossesPerTurbine>();

        foreach (var turbine in turbineData)
        {
            var losses = turbine.EnergyPotential - turbine.ActualEnergy;
            results.Add(new ProductionAndLossesPerTurbine
            {
                Id = turbine.Id,
                Name = turbineStructure.FirstOrDefault(x => x.Id == turbine.Id)?.Name ?? "Unknown",
                Losses = new ValuePercentage { Value = losses.Round(2), Percentage = ConvertExtensions.CalculatePercentage(losses, turbine.EnergyPotential, 2) },
                Production = new ValuePercentage { Value = turbine.ActualEnergy.Round(2), Percentage = ConvertExtensions.CalculatePercentage(turbine.ActualEnergy, turbine.EnergyPotential, 2) }
            });
        }

        return results;
    }
}