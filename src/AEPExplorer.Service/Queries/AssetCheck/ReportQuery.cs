#nullable enable
using AEPExplorer.Model.Request;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Presentation;
using Drawing = DocumentFormat.OpenXml.Drawing;
using Index = DocumentFormat.OpenXml.Drawing.Charts.Index;
using Shape = DocumentFormat.OpenXml.Presentation.Shape;
using ShapeProperties = DocumentFormat.OpenXml.Presentation.ShapeProperties;

namespace AEPExplorer.Service.Queries.AssetCheck;

public class ReportQuery : MediatR.IRequest<MemoryStream>
{
    public Query Query { get; set; }
    public string UserFullName { get; set; }
}

public class ReportQueryHandler() : IRequestHandler<ReportQuery, MemoryStream>
{
    private const string FILE_PATH = "Assets/AssetCheckTemplate.pptx";

    public async Task<MemoryStream> Handle(ReportQuery request, CancellationToken cancellationToken)
    {
        await using var fsSource = new FileStream(FILE_PATH, FileMode.Open, FileAccess.Read);
        var stream = new MemoryStream();
        await fsSource.CopyToAsync(stream, cancellationToken);
        stream.Position = 0;
        var document = await ReadPresentation(stream);
        var data = new Dictionary<string, int>()
        {
            {"Regular Production", 83},
            {"Losses", 17}
        };
        
        InsertNewSlideFromPresentation(document, 1, "Test", data);
        document.Dispose();
        stream.Position = 0;
        return stream;
    }

    private static Task<PresentationDocument> ReadPresentation(Stream stream)
    {
        var presentationDoc = PresentationDocument.Open(stream, isEditable: true);
        return Task.FromResult(presentationDoc);
    }


    private static void InsertNewSlideFromPresentation(PresentationDocument presentationDocument, int position, string slideTitle, Dictionary<string, int> data)
    {
        var presentationPart = presentationDocument.PresentationPart;

        // Verify that the presentation is not empty.
        if (presentationPart is null)
        {
            throw new InvalidOperationException("The presentation document is empty.");
        }

        // Declare and instantiate a new slide.
        var slide = new Slide(new CommonSlideData(new ShapeTree()));
        uint drawingObjectId = 1;

        // Construct the slide content.            
        // Specify the non-visual properties of the new slide.
        var commonSlideData = slide.CommonSlideData ?? slide.AppendChild(new CommonSlideData());
        var shapeTree = commonSlideData.ShapeTree ?? commonSlideData.AppendChild(new ShapeTree());
        var nonVisualProperties = shapeTree.AppendChild(new NonVisualGroupShapeProperties());
        nonVisualProperties.NonVisualDrawingProperties = new NonVisualDrawingProperties() { Id = 1, Name = "" };
        nonVisualProperties.NonVisualGroupShapeDrawingProperties = new NonVisualGroupShapeDrawingProperties();
        nonVisualProperties.ApplicationNonVisualDrawingProperties = new ApplicationNonVisualDrawingProperties();

        // Specify the group shape properties of the new slide.
        shapeTree.AppendChild(new GroupShapeProperties());

        // Declare and instantiate the title shape of the new slide.
        var titleShape = shapeTree.AppendChild(new Shape());

        drawingObjectId++;

        // Specify the required shape properties for the title shape. 
        titleShape.NonVisualShapeProperties = new NonVisualShapeProperties
        (new NonVisualDrawingProperties() { Id = drawingObjectId, Name = "Energy Production" },
            new NonVisualShapeDrawingProperties(new Drawing.ShapeLocks() { NoGrouping = true }),
            new ApplicationNonVisualDrawingProperties(new PlaceholderShape() { Type = PlaceholderValues.Title }));
        titleShape.ShapeProperties = new ShapeProperties();

        // Specify the text of the title shape.
        titleShape.TextBody = new TextBody(new Drawing.BodyProperties(),
            new Drawing.ListStyle(),
            new Drawing.Paragraph(new Drawing.Run(new Drawing.Text() { Text = slideTitle })));

        // Declare and instantiate the body shape of the new slide.
        var bodyShape = shapeTree.AppendChild(new Shape());
        drawingObjectId++;

        // Specify the required shape properties for the body shape.

        bodyShape.NonVisualShapeProperties = new NonVisualShapeProperties(new NonVisualDrawingProperties() { Id = drawingObjectId, Name = "Content Placeholder" },
            new NonVisualShapeDrawingProperties(new Drawing.ShapeLocks() { NoGrouping = true }),
            new ApplicationNonVisualDrawingProperties(new PlaceholderShape() { Index = 1 }));
        bodyShape.ShapeProperties = new ShapeProperties();

        // Specify the text of the body shape.
        bodyShape.TextBody = new TextBody(new Drawing.BodyProperties(),
            new Drawing.ListStyle(),
            new Drawing.Paragraph());


        // Create the slide part for the new slide.
        var slidePart = presentationPart.AddNewPart<SlidePart>();
        // DrawingsPart drawingsPart = slidePart.AddNewPart<DrawingsPart>();
        // presentationPart.Presentation.Append(new DocumentFormat.OpenXml.Spreadsheet.Drawing()
        //     { Id = presentationPart.GetIdOfPart(drawingsPart) });
        // slide.Save(slidePart);
        // presentationPart.Presentation.Save();

        ChartPart chartPart = slidePart.AddNewPart<ChartPart>();
        chartPart.ChartSpace = new ChartSpace();
        chartPart.ChartSpace.Append(new EditingLanguage() { Val = new StringValue("en-US") });
        DocumentFormat.OpenXml.Drawing.Charts.Chart chart = chartPart.ChartSpace.AppendChild<DocumentFormat.OpenXml.Drawing.Charts.Chart>(
            new DocumentFormat.OpenXml.Drawing.Charts.Chart());

        PlotArea plotArea = chart.AppendChild<PlotArea>(new PlotArea());
        Layout layout = plotArea.AppendChild<Layout>(new Layout());
        BarChart barChart = plotArea.AppendChild<BarChart>(new BarChart(new BarDirection()
                { Val = new EnumValue<BarDirectionValues>(BarDirectionValues.Column) },
            new BarGrouping() { Val = new EnumValue<BarGroupingValues>(BarGroupingValues.Clustered) }));

        uint i = 0;

        foreach (string key in data.Keys)
        {
            BarChartSeries barChartSeries = barChart.AppendChild<BarChartSeries>(new BarChartSeries(new Index()
                {
                    Val = new UInt32Value(i)
                },
                new Order() { Val = new UInt32Value(i) },
                new SeriesText(new NumericValue() { Text = key })));

            StringLiteral strLit = barChartSeries.AppendChild<CategoryAxisData>(new CategoryAxisData()).AppendChild<StringLiteral>(new StringLiteral());
            strLit.Append(new PointCount() { Val = new UInt32Value(1U) });
            strLit.AppendChild<StringPoint>(new StringPoint() { Index = new UInt32Value(0U) }).Append(new NumericValue("TITTLE"));

            NumberLiteral numLit = barChartSeries.AppendChild<DocumentFormat.OpenXml.Drawing.Charts.Values>(
                new DocumentFormat.OpenXml.Drawing.Charts.Values()).AppendChild<NumberLiteral>(new NumberLiteral());
            numLit.Append(new FormatCode("General"));
            numLit.Append(new PointCount() { Val = new UInt32Value(1U) });
            numLit.AppendChild<NumericPoint>(new NumericPoint() { Index = new UInt32Value(0u) }).Append
                (new NumericValue(data[key].ToString()));

            i++;
        }

        barChart.Append(new AxisId() { Val = new UInt32Value(48650112u) });
        barChart.Append(new AxisId() { Val = new UInt32Value(48672768u) });

        // Add the Category Axis.
        CategoryAxis catAx = plotArea.AppendChild<CategoryAxis>(new CategoryAxis(new AxisId()
                { Val = new UInt32Value(48650112u) }, new Scaling(new Orientation()
            {
                Val = new EnumValue<DocumentFormat.OpenXml.Drawing.Charts.OrientationValues>(DocumentFormat.OpenXml.Drawing.Charts.OrientationValues.MinMax)
            }),
            new AxisPosition() { Val = new EnumValue<AxisPositionValues>(AxisPositionValues.Bottom) },
            new TickLabelPosition() { Val = new EnumValue<TickLabelPositionValues>(TickLabelPositionValues.NextTo) },
            new CrossingAxis() { Val = new UInt32Value(48672768U) },
            new Crosses() { Val = new EnumValue<CrossesValues>(CrossesValues.AutoZero) },
            new AutoLabeled() { Val = new BooleanValue(true) },
            new LabelAlignment() { Val = new EnumValue<LabelAlignmentValues>(LabelAlignmentValues.Center) },
            new LabelOffset() { Val = new UInt16Value((ushort)100) }));

        // Add the Value Axis.
        ValueAxis valAx = plotArea.AppendChild<ValueAxis>(new ValueAxis(new AxisId() { Val = new UInt32Value(48672768u) },
            new Scaling(new Orientation()
            {
                Val = new EnumValue<DocumentFormat.OpenXml.Drawing.Charts.OrientationValues>(
                    DocumentFormat.OpenXml.Drawing.Charts.OrientationValues.MinMax)
            }),
            new AxisPosition() { Val = new EnumValue<AxisPositionValues>(AxisPositionValues.Left) },
            new MajorGridlines(),
            new DocumentFormat.OpenXml.Drawing.Charts.NumberingFormat()
            {
                FormatCode = new StringValue("General"),
                SourceLinked = new BooleanValue(true)
            }, new TickLabelPosition()
            {
                Val = new EnumValue<TickLabelPositionValues>
                    (TickLabelPositionValues.NextTo)
            }, new CrossingAxis() { Val = new UInt32Value(48650112U) },
            new Crosses() { Val = new EnumValue<CrossesValues>(CrossesValues.AutoZero) },
            new CrossBetween() { Val = new EnumValue<CrossBetweenValues>(CrossBetweenValues.Between) }));

        // Add the chart Legend.
        Legend legend = chart.AppendChild<Legend>(new Legend(new LegendPosition() { Val = new EnumValue<LegendPositionValues>(LegendPositionValues.Right) },
            new Layout()));

        chart.Append(new PlotVisibleOnly() { Val = new BooleanValue(true) });

// Save the chart part.
        chartPart.ChartSpace.Save();

        // // Position the chart on the worksheet using a TwoCellAnchor object.
        // drawingsPart.WorksheetDrawing = new WorksheetDrawing();
        // TwoCellAnchor twoCellAnchor = drawingsPart.WorksheetDrawing.AppendChild<TwoCellAnchor>(new TwoCellAnchor());
        // twoCellAnchor.Append(new DocumentFormat.OpenXml.Drawing.Spreadsheet.FromMarker(new ColumnId("9"),
        //     new ColumnOffset("581025"),
        //     new RowId("17"),
        //     new RowOffset("114300")));
        // twoCellAnchor.Append(new DocumentFormat.OpenXml.Drawing.Spreadsheet.ToMarker(new ColumnId("17"),
        //     new ColumnOffset("276225"),
        //     new RowId("32"),
        //     new RowOffset("0")));
        //
        // // Append a GraphicFrame to the TwoCellAnchor object.
        // DocumentFormat.OpenXml.Drawing.Spreadsheet.GraphicFrame graphicFrame =
        //     twoCellAnchor.AppendChild<DocumentFormat.OpenXml.
        //         Drawing.Spreadsheet.GraphicFrame>(new DocumentFormat.OpenXml.Drawing.
        //         Spreadsheet.GraphicFrame());
        // graphicFrame.Macro = "";
        //
        // graphicFrame.Append(new DocumentFormat.OpenXml.Drawing.Spreadsheet.NonVisualGraphicFrameProperties(
        //     new DocumentFormat.OpenXml.Drawing.Spreadsheet.NonVisualDrawingProperties() { Id = new UInt32Value(2u), Name = "Chart 1" },
        //     new DocumentFormat.OpenXml.Drawing.Spreadsheet.NonVisualGraphicFrameDrawingProperties()));
        //
        // graphicFrame.Append(new Transform(new Offset() { X = 0L, Y = 0L },
        //     new Extents() { Cx = 0L, Cy = 0L }));
        //
        // graphicFrame.Append(new Graphic(new GraphicData(new ChartReference() { Id = drawingsPart.GetIdOfPart(chartPart) })
        //     { Uri = "https://schemas.openxmlformats.org/drawingml/2006/chart" }));
        //
        // twoCellAnchor.Append(new ClientData());

        // Save the WorksheetDrawing object.
        
        
        // Modify the slide ID list in the presentation part.
        // The slide ID list should not be null.
        var slideIdList = presentationPart.Presentation.SlideIdList;

        // Find the highest slide ID in the current list.
        uint maxSlideId = 1;
        SlideId? prevSlideId = null;

        var slideIds = slideIdList?.ChildElements ?? default;

        foreach (SlideId slideId in slideIds)
        {
            if (slideId.Id is not null && slideId.Id > maxSlideId)
            {
                maxSlideId = slideId.Id;
            }

            position--;
            if (position == 0)
            {
                prevSlideId = slideId;
            }
        }

        maxSlideId++;

        // Get the ID of the previous slide.
        SlidePart lastSlidePart;

        if (prevSlideId is not null && prevSlideId.RelationshipId is not null)
        {
            lastSlidePart = (SlidePart)presentationPart.GetPartById(prevSlideId.RelationshipId!);
        }
        else
        {
            string? firstRelId = ((SlideId)slideIds[0]).RelationshipId;
            // If the first slide does not contain a relationship ID, throw an exception.
            if (firstRelId is null)
            {
                throw new ArgumentNullException(nameof(firstRelId));
            }

            lastSlidePart = (SlidePart)presentationPart.GetPartById(firstRelId);
        }

        // Use the same slide layout as that of the previous slide.
        if (lastSlidePart.SlideLayoutPart is not null)
        {
            slidePart.AddPart(lastSlidePart.SlideLayoutPart);
        }

        // Insert the new slide into the slide list after the previous slide.
        var newSlideId = slideIdList!.InsertAfter(new SlideId(), prevSlideId);
        newSlideId.Id = maxSlideId;
        newSlideId.RelationshipId = presentationPart.GetIdOfPart(slidePart);

        // Save the modified presentation.
        presentationPart.Presentation.Save();
    }
}