using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Model.Enums;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.AssetCheck;

public class EstimatedEnergyLossesQuery : MediatR.IRequest<List<LossByCategoryModel>>
{
    public Query Query { get; init; }
}

public class EstimatedEnergyLossesHandler : IRequestHandler<EstimatedEnergyLossesQuery, List<LossByCategoryModel>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public EstimatedEnergyLossesHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext, IMediator mediator)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<LossByCategoryModel>> Handle(EstimatedEnergyLossesQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DurationInHours)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer
            {
                Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
            });

        var items = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, request.Query, nameof(AepReadData.SubcategoryId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION, LossesTypeEnum.AggregatedLosses);
        
        request.Query.Category = new List<Guid>();
        var totalEnergyPotential = await ElasticsearchSumService.SumAsync(_elasticClient, request.Query, p => p.Field(x => x.EnergyPotential), lossesType: LossesTypeEnum.TotalLosses);

        var categoryStructure = await _dbContext.Categories.Select(x => new
        {
            x.Id,
            x.Name
        }).ToListAsync(cancellationToken);

        var categoryValues = items.Where(x => x.Key != CategoryHelper.OPERATIVE_CATEGORY
                                                        && x.Key != CategoryHelper.NON_OPERATIVE_CATEGORY
                                                        && x.Key != CategoryHelper.RUN_CATEGORY_ID).Select(x => new
                                                        {
                                                            Id = x.Key,
                                                            Name = categoryStructure.FirstOrDefault(y => y.Id == x.Key)?.Name ?? "Unknown",
                                                            ActualProduction = x.Value[nameof(AepReadData.ActualEnergy)],
                                                            EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)],
                                                            DurationInHours = x.Value[nameof(AepReadData.DurationInHours)]
                                                        }).ToList();

        var results = new List<LossByCategoryModel>();

        foreach (var categoryValue in categoryValues)
        {

            var losses = categoryValue.EnergyPotential - categoryValue.ActualProduction;

            results.Add(new LossByCategoryModel
            {
                Id = categoryValue.Id,
                Name = categoryValue.Name,
                Value = losses.Round(2),
                Percentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential, 2),
                DurationInHours = categoryValue.DurationInHours,
                Children = null
            });

        }
        return results;
    }
}
