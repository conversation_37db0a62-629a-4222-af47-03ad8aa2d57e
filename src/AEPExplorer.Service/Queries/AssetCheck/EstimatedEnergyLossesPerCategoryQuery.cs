using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Model.Enums;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.AssetCheck;

public class EstimatedEnergyLossesPerCategoryQuery : MediatR.IRequest<List<LossByCategoryModel>>
{
    public Query Query { get; init; }

    public Guid CategoryId { get; set; }
}

public class EstimatedEnergyLossesPerCategoryHandler : IRequestHandler<EstimatedEnergyLossesPerCategoryQuery, List<LossByCategoryModel>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public EstimatedEnergyLossesPerCategoryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext, IMediator mediator)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<LossByCategoryModel>> Handle(EstimatedEnergyLossesPerCategoryQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DurationInHours)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer
            {
                Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
            });

        var results = new List<LossByCategoryModel>();

        var subcategoryStructure = await _dbContext.Subcategories.Where(x => x.CategoryId == request.CategoryId).Select(x => new
        {
            x.Id,
            x.Name,
        }).ToListAsync(cancellationToken);

        if (subcategoryStructure.Count == 0)
        {
            return results;
        }

        request.Query.Category = subcategoryStructure.Select(x => x.Id).ToList();
        var items = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, request.Query, nameof(AepReadData.SubcategoryId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION, LossesTypeEnum.Detailed);

        var subcategoryValues = items.Select(x => new
        {
            Id = x.Key,
            Name = subcategoryStructure.FirstOrDefault(y => y.Id == x.Key)?.Name ?? "Unknown",
            ActualProduction = x.Value[nameof(AepReadData.ActualEnergy)],
            EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)],
            DurationInHours = x.Value[nameof(AepReadData.DurationInHours)]
        }).ToList();

        request.Query.Category = new List<Guid>();
        var totalEnergyPotential = await ElasticsearchSumService.SumAsync(_elasticClient, request.Query, p => p.Field(x => x.EnergyPotential), lossesType: LossesTypeEnum.TotalLosses);

        foreach (var subcategoryValue in subcategoryValues)
        {
            var losses = subcategoryValue.EnergyPotential - subcategoryValue.ActualProduction;
            results.Add(new LossByCategoryModel
            {
                Id = subcategoryValue.Id,
                Name = subcategoryValue.Name,
                Value = losses.Round(2),
                Percentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential, 2),
                DurationInHours = subcategoryValue.DurationInHours.Round(2),
                Children = null
            });
        }

        return results;
    }
}