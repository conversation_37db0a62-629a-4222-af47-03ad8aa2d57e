using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class LossByAlarmQuery : MediatR.IRequest<List<LossesBaseModel>>
{
    public AlarmQuery AlarmQuery { get; init; }
}

public class LossByAlarmQueryHandler(IElasticClient elasticClient) : IRequestHandler<LossByAlarmQuery, List<LossesBaseModel>>
{
    public async Task<List<LossesBaseModel>> Handle(LossByAlarmQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string> {
            nameof(AlarmsReadData.ActualEnergy),
            nameof(AlarmsReadData.EnergyPotential),
            nameof(AlarmsReadData.AlarmDurationInHours)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName, 
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var items = await ElasticsearchSumService.SumByGroupAlarmsAsync(
            elasticClient, request.AlarmQuery, nameof(AlarmsReadData.Alarm), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);
        
        var totalLosses = items.Values.Sum(x => x[nameof(AlarmsReadData.EnergyPotential)] - x[nameof(AlarmsReadData.ActualEnergy)]);
        
        return (from item in items
            let actualProduction = item.Value[nameof(AlarmsReadData.ActualEnergy)]
            let energyPotential = item.Value[nameof(AlarmsReadData.EnergyPotential)]
            let losses = energyPotential - actualProduction
            select new LossesBaseModel
            {
                Id = ConvertExtensions.GuidMd5(item.Key),
                Name = item.Key,
                Value = losses,
                Percentage = ConvertExtensions.CalculatePercentage(losses, totalLosses),
                DurationInHours = item.Value[nameof(AlarmsReadData.AlarmDurationInHours)].Round(2)
            }).ToList();
    }
}