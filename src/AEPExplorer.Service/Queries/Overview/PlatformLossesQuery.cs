using AEPExplorer.Data.EF;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Response;
using AEPExplorer.Model;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class PlatformLossesQuery : MediatR.IRequest<List<PlatformLosses>>
{
    public Query Query { get; init; }
}

public class PlatformLossesQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<PlatformLossesQuery, List<PlatformLosses>>
{
    public async Task<List<PlatformLosses>> Handle(PlatformLossesQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string> {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        request.Query.Category = FilterHelper.MapToCategories(request.Query.Category, dbContext);

        var items = await ElasticsearchSumService.SumByGroupAsync(
                   elasticClient, request.Query, nameof(AepReadData.TurbinePlatformId), nameof(AepReadData.SubcategoryId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);

        var turbinePlatformIds = items.Select(x => x.Key).ToList();
        var turbinePlatforms = await dbContext.TurbinePlatforms.Where(x => turbinePlatformIds.Contains(x.Id)).ToListAsync(cancellationToken);

        request.Query.Category = [];
        var totalEnergyPotential = await ElasticsearchSumService.SumAsync(elasticClient, request.Query, p => p.Field(x => x.EnergyPotential), lossesType: LossesTypeEnum.TotalLosses);
        var categoriesStructure = await dbContext.Categories.Where(x => x.Id != CategoryHelper.RUN_CATEGORY_ID).Select(x => new BasicCategoryData
        {
            Id = x.Id,
            Name = x.Name,
            Subcategories = x.Subcategories
        }).ToListAsync(cancellationToken);

        var result = new List<PlatformLosses>();

        foreach (var item in items)
        {
            var subcategoryData = item.Value.Select(x => new SubcategoryProduction
            {
                Id = x.Key,
                ActualProduction = x.Value[nameof(AepReadData.ActualEnergy)],
                EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)]
            }).ToList();

            result.Add(new PlatformLosses
            {
                Id = item.Key,
                Name = turbinePlatforms.FirstOrDefault(x => x.Id == item.Key)?.Name ?? "Unknown",
                Children = CategoryHelper.SumLossesPerCategory(subcategoryData, categoriesStructure, request.Query.Category, totalEnergyPotential)
            });
        }

        return result;
    }
}
