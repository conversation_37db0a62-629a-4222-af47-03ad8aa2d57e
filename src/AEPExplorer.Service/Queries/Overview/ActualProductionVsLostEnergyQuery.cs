using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class ActualProductionVsLostEnergyQuery : MediatR.IRequest<List<NameValuePercentage>>
{
    public Query Query { get; init; }
}

public class ActualProductionVsLostEnergyQueryHandler(IElasticClient elasticClient) : IRequestHandler<ActualProductionVsLostEnergyQuery, List<NameValuePercentage>>
{
    public async Task<List<NameValuePercentage>> Handle(ActualProductionVsLostEnergyQuery request, CancellationToken cancellationToken)
    {
        var aggregations = new Dictionary<string, IAggregationContainer>
        {
            { nameof(AepReadData.ActualEnergy), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.ActualEnergy), nameof(AepReadData.ActualEnergy).ToLowerFirstChar()) } },
            { nameof(AepReadData.EnergyPotential), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.EnergyPotential), nameof(AepReadData.EnergyPotential).ToLowerFirstChar()) } }
        };
        var totalQuery = request.Query.DeepCopy();
        totalQuery.Category = [];
        var aggregationResult = await ElasticsearchSumService.SumAsync(elasticClient, totalQuery, aggregations, lossesType: LossesTypeEnum.TotalLosses);
        var totalActualEnergy = aggregationResult[nameof(AepReadData.ActualEnergy)];
        var totalEnergyPotential = aggregationResult[nameof(AepReadData.EnergyPotential)];
        var totalLostEnergy = totalEnergyPotential - totalActualEnergy;
        totalQuery.Category = [CategoryHelper.DERATED_CATEGORY, CategoryHelper.DEGRADED_CATEGORY, CategoryHelper.RUN_CATEGORY_ID];
        aggregations = new Dictionary<string, IAggregationContainer>
        {
            { nameof(AepReadData.ActualEnergy), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.ActualEnergy), nameof(AepReadData.ActualEnergy).ToLowerFirstChar()) } },
        };
        var groupedAggregationResult = await ElasticsearchSumService.SumByGroupAsync(elasticClient, totalQuery, nameof(AepReadData.SubcategoryId), aggregations);
        var degraded = groupedAggregationResult.TryGetValue(CategoryHelper.DEGRADED_CATEGORY, out var degradedItem) ? degradedItem[nameof(AepReadData.ActualEnergy)] : 0;
        var derated = groupedAggregationResult.TryGetValue(CategoryHelper.DERATED_CATEGORY, out var deratedItem) ? deratedItem[nameof(AepReadData.ActualEnergy)] : 0;
        var run = groupedAggregationResult.TryGetValue(CategoryHelper.RUN_CATEGORY_ID, out var runtItem) ? runtItem[nameof(AepReadData.ActualEnergy)] : 0;
        var other = totalActualEnergy - (degraded + derated + run);

        var result = new List<NameValuePercentage>
        {
            new()
            {
                Id = new Guid(ConvertExtensions.Md5("Total Actual Energy")),
                Name = "Total Actual Energy",
                Percentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential),
                Value = totalActualEnergy.Round(2)
            },
            new()
            {
                Id = new Guid(ConvertExtensions.Md5("Total Lost Energy")),
                Name = "Total Lost Energy",
                Percentage = ConvertExtensions.CalculatePercentage(totalLostEnergy, totalEnergyPotential),
                Value = totalLostEnergy.Round(2)
            },
            new()
            {
                Id = new Guid(ConvertExtensions.Md5("Total Potential Energy")),
                Name = "Total Potential Energy",
                Percentage = 100,
                Value = totalEnergyPotential.Round(2)
            },
            new()
            {
                Id = CategoryHelper.DERATED_CATEGORY,
                Name = "Derated",
                Percentage = ConvertExtensions.CalculatePercentage(derated, totalEnergyPotential),
                Value = derated.Round(2)
            },
            new()
            {
                Id = CategoryHelper.DEGRADED_CATEGORY,
                Name = "Degraded",
                Percentage = ConvertExtensions.CalculatePercentage(degraded, totalEnergyPotential),
                Value = degraded.Round(2)
            },
            new()
            {
                Id = CategoryHelper.RUN_CATEGORY_ID_FULL_PERFORMANCE,
                Name = "Full Performance",
                Percentage = ConvertExtensions.CalculatePercentage(run, totalEnergyPotential),
                Value = run.Round(2)
            },
            new()
            {
                Id = new Guid(ConvertExtensions.Md5("Other")),
                Name = "Other",
                Percentage = ConvertExtensions.CalculatePercentage(other, totalEnergyPotential),
                Value = other.Round(2)
            }
        };


        if (!request.Query.FilteredCategories)
        {
            return result;
        }

        aggregations = new Dictionary<string, IAggregationContainer>
        {
            { nameof(AepReadData.ActualEnergy), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.ActualEnergy), nameof(AepReadData.ActualEnergy).ToLowerFirstChar()) } },
            { nameof(AepReadData.EnergyPotential), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.EnergyPotential), nameof(AepReadData.EnergyPotential).ToLowerFirstChar()) } }
        };
        var selectedAggregationResult = await ElasticsearchSumService.SumAsync(elasticClient, request.Query, aggregations,
            lossesType: request.Query.FilteredCategories ? LossesTypeEnum.Detailed : LossesTypeEnum.TotalLosses);
        var selectedActualEnergy = selectedAggregationResult[nameof(AepReadData.ActualEnergy)];
        var selectedEnergyPotential = selectedAggregationResult[nameof(AepReadData.EnergyPotential)];
        var selectedLostEnergy = selectedEnergyPotential - selectedActualEnergy;
        result.AddRange(
        [
            new NameValuePercentage
            {
                Id = new Guid(ConvertExtensions.Md5("Selected Actual Energy")),
                Name = "Selected Actual Energy",
                Percentage = ConvertExtensions.CalculatePercentage(selectedActualEnergy, totalEnergyPotential),
                Value = selectedActualEnergy.Round(2)
            },
            new NameValuePercentage
            {
                Id = new Guid(ConvertExtensions.Md5("Selected Lost Energy")),
                Name = "Selected Lost Energy",
                Percentage = ConvertExtensions.CalculatePercentage(selectedLostEnergy, totalEnergyPotential),
                Value = selectedLostEnergy.Round(2)
            },
            new NameValuePercentage
            {
                Id = new Guid(ConvertExtensions.Md5("Selected Potential Energy")),
                Name = "Selected Potential Energy",
                Percentage = ConvertExtensions.CalculatePercentage(selectedEnergyPotential, totalEnergyPotential),
                Value = selectedEnergyPotential.Round(2)
            }
        ]);

        return result;
    }
}