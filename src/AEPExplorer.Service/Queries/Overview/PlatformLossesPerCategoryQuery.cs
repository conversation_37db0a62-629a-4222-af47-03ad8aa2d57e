using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class PlatformLossesPerCategoryQuery : MediatR.IRequest<List<NameValuePercentageWithChildren>>
{
    public Query Query { get; init; }
}

public class PlatformLossesPerCategoryQueryHandler : IRequestHandler<PlatformLossesPerCategoryQuery, List<NameValuePercentageWithChildren>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public PlatformLossesPerCategoryQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<NameValuePercentageWithChildren>> Handle(PlatformLossesPerCategoryQuery request, CancellationToken cancellationToken)
    {

        var sumPropertyNames = new List<string> {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        request.Query.Category = FilterHelper.MapToCategories(request.Query.Category, _dbContext);

        var items = await ElasticsearchSumService.SumByGroupAsync(
            _elasticClient, request.Query, nameof(AepReadData.SubcategoryId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);

        request.Query.Category = new List<Guid>();
        var totalEnergyPotential = await ElasticsearchSumService.SumAsync(_elasticClient, request.Query, p => p.Field(x => x.EnergyPotential), lossesType: LossesTypeEnum.TotalLosses);
        
        var categoriesStructure = await _dbContext.Categories.Where(x => x.Id != CategoryHelper.RUN_CATEGORY_ID).Select(x => new BasicCategoryData
        {
            Id = x.Id,
            Name = x.Name,
            Subcategories = x.Subcategories
        }).ToListAsync(cancellationToken);

        var subcategoryData = items.Select(x => new SubcategoryProduction
        {
            Id = x.Key,
            ActualProduction = x.Value[nameof(AepReadData.ActualEnergy)],
            EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)]
        }).ToList();

        return CategoryHelper.SumLossesPerCategory(subcategoryData, categoriesStructure, request.Query.Category, totalEnergyPotential);
    }
}