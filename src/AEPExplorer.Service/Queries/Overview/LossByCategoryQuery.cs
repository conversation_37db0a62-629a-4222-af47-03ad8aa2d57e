using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class LossByCategoryQuery : MediatR.IRequest<List<LossLevel>>
{
    public Query Query { get; init; }
}

public class LossByCategoryQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<LossByCategoryQuery, List<LossLevel>>
{
    public async Task<List<LossLevel>> Handle(LossByCategoryQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DurationInHours)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var OPERATIVE_CATEGORY_ID = ConvertExtensions.GuidMd5(CategoryHelper.OPERATIVE_CATEGORY_LABEL);
        var NONOPERATIVE_CATEGORY_ID = ConvertExtensions.GuidMd5(CategoryHelper.NONOPERATIVE_CATEGORY_LABEL);

        var subcategories = await dbContext.Subcategories.Where(x => !CategoryHelper.RunSubcategories.Contains(x.Id)).ToListAsync(cancellationToken);
        var categories = await dbContext.Categories.Select(x => new LossLevel { Id = x.Id, Name = x.Name, ParentId = x.Level4Id }).ToListAsync(cancellationToken);
        var level4List = await dbContext.Level4s.Select(x => new LossLevel { Id = x.Id, Name = x.Name, ParentId = x.Level3Id }).ToListAsync(cancellationToken);
        var level3List = await dbContext.Level3s.Select(x => new LossLevel { Id = x.Id, Name = x.Name, ParentId = x.Id == CategoryHelper.LEVEL3_IN_SERVICE_ID || x.Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID ? OPERATIVE_CATEGORY_ID : NONOPERATIVE_CATEGORY_ID }).ToListAsync(cancellationToken); ;

        request.Query.Category = FilterHelper.MapToSubcategories(request.Query.Category, dbContext);

        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, nameof(AepReadData.SubcategoryId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);
            
        request.Query.Category = new List<Guid>();
        var totalEnergyPotential = await ElasticsearchSumService.SumAsync(elasticClient, request.Query, p => p.Field(x => x.EnergyPotential), lossesType: LossesTypeEnum.TotalLosses);

        var lossesPerSubcategory = items
       .Select(x =>
       {
           var energyPotential = x.Value[nameof(AepReadData.EnergyPotential)];
           var actualEnergy = x.Value[nameof(AepReadData.ActualEnergy)];
           var duration = x.Value[nameof(AepReadData.DurationInHours)];
           var loss = energyPotential - actualEnergy;
           var percentage = ConvertExtensions.CalculatePercentage(loss, totalEnergyPotential, 4);

           return new LossLevel
           {
               Id = x.Key,
               Name = subcategories.FirstOrDefault(s => s.Id == x.Key)?.Name ?? "Unknown",
               ParentId = subcategories.FirstOrDefault(s => s.Id == x.Key)?.CategoryId ?? new Guid(),
               Value = loss,
               Percentage = loss > 0 && percentage == 0 ? 0.0001 : percentage,
               DurationInHours = duration
           };
       })
       .ToList();

        var categoriesWithChildren = CategoryHelper.CreateLossLevel(categories, lossesPerSubcategory);
        var level4ListWithChildren = CategoryHelper.CreateLossLevel(level4List, categoriesWithChildren);
        var level3ListWithChildren = CategoryHelper.CreateLossLevel(level3List, level4ListWithChildren);

        var result = new List<LossLevel>
        {
            new()
            {
                Id =OPERATIVE_CATEGORY_ID,
                Name = CategoryHelper.OPERATIVE_CATEGORY_LABEL,
                ParentId=new Guid(),
                Value=0,
                Percentage=0,
                DurationInHours = 0,
                Children=level3ListWithChildren.Where(x=>x.ParentId==OPERATIVE_CATEGORY_ID).ToList()
            },
            new()
            {
                Id = NONOPERATIVE_CATEGORY_ID,
                Name = CategoryHelper.NONOPERATIVE_CATEGORY_LABEL,
                ParentId=new Guid(),
                Value=0,
                Percentage=0,
                DurationInHours = 0,
                Children=level3ListWithChildren.Where(x=>x.ParentId==NONOPERATIVE_CATEGORY_ID).ToList()
            }
        };

        return result.Where(item => item.Children.Count > 0).ToList();
    }
}