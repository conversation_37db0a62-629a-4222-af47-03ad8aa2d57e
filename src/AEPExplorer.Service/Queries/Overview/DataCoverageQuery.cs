using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class DataCoverageQuery : MediatR.IRequest<List<CoverageTimelineModel>>
{
    public Query Query { get; init; }
}

public class DataCoverageQueryHandler(IElasticClient elasticClient) : IRequestHandler<DataCoverageQuery, List<CoverageTimelineModel>>
{
    public async Task<List<CoverageTimelineModel>> Handle(DataCoverageQuery request, CancellationToken cancellationToken)
    {
        request.Query.Category = [];
        var aggregations = new Dictionary<string, IAggregationContainer>
        {
            {
                nameof(AepReadData.DataCoverage), new AggregationContainer
                {
                    Sum = new SumAggregation(nameof(AepReadData.DataCoverage), nameof(AepReadData.DataCoverage).ToLowerFirstChar())
                }
            },
            {
                nameof(AepReadData.DurationInHours), new AggregationContainer
                {
                    Sum = new SumAggregation(nameof(AepReadData.DurationInHours), nameof(AepReadData.DurationInHours).ToLowerFirstChar())
                }
            }
        };
        var items = await ElasticsearchSumService.SumByDateHistogramAsync(elasticClient, request.Query, aggregations, DateInterval.Day, lossesType: LossesTypeEnum.TotalLosses);
        var turbines = await ElasticsearchGeneralService.GetDistinctValuesAsync(elasticClient, x => x.TurbineId, request.Query);
        var data = new List<CoverageTimelineModel>();
        for (var dt = request.Query.DateFrom.Value; dt <= request.Query.DateTo.Value; dt = dt.AddDays(1))
        {
            items.TryGetValue(dt, out var item);
            if (item != null)
            {
                item.TryGetValue(nameof(AepReadData.DurationInHours), out var duration);
                item.TryGetValue(nameof(AepReadData.DataCoverage), out var dataCoverage);

                data.Add(new CoverageTimelineModel()
                {
                    Date = dt,
                    ActualEnergy = ConvertExtensions.CalculatePercentage(duration / 24, turbines.Count),
                    DataCoverage = ConvertExtensions.CalculatePercentage(dataCoverage, turbines.Count),
                    PotentialEnergy = ConvertExtensions.CalculatePercentage(dataCoverage, duration / 24)
                });
            }
            else
            {
                data.Add(new CoverageTimelineModel()
                {
                    Date = dt,
                    ActualEnergy = 0,
                    DataCoverage = 0,
                    PotentialEnergy = 0
                });
            }
        }

        return data;
    }
}