using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class AlarmsAndStopsQuery : MediatR.IRequest<double>
{
    public Query Query { get; init; }
}

public class AlarmsAndStopsQueryHandler(IElasticClient elasticClient) : IRequestHandler<AlarmsAndStopsQuery, double>
{
    public async Task<double> Handle(AlarmsAndStopsQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var alarmsCategoryId = CategoryHelper.ALARMS_CATEGORY;
        var stopsCategoryId = CategoryHelper.STOPS_CATEGORY;

        request.Query.Category = [alarmsCategoryId, stopsCategoryId];

        var items = await ElasticsearchSumService.SumAsync(elasticClient, request.Query, aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);
        var alarmsAndStops = items[nameof(AepReadData.EnergyPotential)] - items[nameof(AepReadData.ActualEnergy)];
        request.Query.Category = [];
        var totalEnergyPotential = await ElasticsearchSumService.SumAsync(elasticClient, request.Query, p => p.Field(x => x.EnergyPotential), lossesType: LossesTypeEnum.TotalLosses);

        return ConvertExtensions.CalculatePercentage(alarmsAndStops, totalEnergyPotential);
    }
}