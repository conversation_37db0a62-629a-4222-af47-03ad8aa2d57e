using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class LossByPlatformAndCategoryQuery : MediatR.IRequest<List<NameValuePercentageWithChildren>>
{
    public Query Query { get; init; }
}

public class LossByPlatformAndCategoryQueryHandler : IRequestHandler<LossByPlatformAndCategoryQuery, List<NameValuePercentageWithChildren>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public LossByPlatformAndCategoryQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<NameValuePercentageWithChildren>> Handle(LossByPlatformAndCategoryQuery request, CancellationToken cancellationToken)
    {
        var aggregations = new Dictionary<string, IAggregationContainer>();
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        foreach (var propertyName in sumPropertyNames)
        {
            aggregations.Add(propertyName, new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });
        }

        if (request.Query.Category.Contains(CategoryHelper.OPERATIVE_CATEGORY))
        {
            var operative = _dbContext.Categories
                .Where(x => x.Level4.Level3Id == CategoryHelper.LEVEL3_IN_SERVICE_ID
                            || x.Level4.Level3Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            request.Query.Category.Remove(CategoryHelper.OPERATIVE_CATEGORY);
            request.Query.Category.AddRange(operative);
        }

        if (request.Query.Category.Contains(CategoryHelper.NON_OPERATIVE_CATEGORY))
        {
            var nonOperative = _dbContext.Categories
                .Where(x => x.Level4.Level3Id != CategoryHelper.LEVEL3_IN_SERVICE_ID
                            && x.Level4.Level3Id != CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            request.Query.Category.Remove(CategoryHelper.NON_OPERATIVE_CATEGORY);
            request.Query.Category.AddRange(nonOperative);
        }

        var items = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, request.Query, nameof(AepReadData.SubcategoryId), aggregations,
            ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);
        var totalEnergyPotential = items.Sum(x => x.Value[nameof(AepReadData.EnergyPotential)]);
        var subcategoryIds = items.Select(x => x.Key).ToList();
        var categoryStructure = await _dbContext.Categories.Where(x => x.Id != CategoryHelper.RUN_CATEGORY_ID)
            .Select(x => new NameValuePercentageWithChildren
            {
                Id = x.Id,
                Name = x.Name,
                Value = items.ContainsKey(x.Id) ? (items[x.Id][nameof(AepReadData.EnergyPotential)] - items[x.Id][nameof(AepReadData.ActualEnergy)]).Round(2) : 0.0,
                Percentage = items.ContainsKey(x.Id)
                    ? ConvertExtensions.CalculatePercentage(
                        items[x.Id][nameof(AepReadData.EnergyPotential)] - items[x.Id][nameof(AepReadData.ActualEnergy)],
                        totalEnergyPotential,
                        ElasticsearchConstants.DECIMAL_PLACES, false)
                    : 0.0,
                Children = x.Subcategories.Where(s => subcategoryIds.Contains(s.Id))
                    .Select(s => new NameValuePercentageWithChildren
                    {
                        Id = s.Id,
                        Name = s.Name,
                        Value = (items[s.Id][nameof(AepReadData.EnergyPotential)] - items[s.Id][nameof(AepReadData.ActualEnergy)]).Round(2),
                        Percentage = ConvertExtensions.CalculatePercentage(
                            items[s.Id][nameof(AepReadData.EnergyPotential)] - items[s.Id][nameof(AepReadData.ActualEnergy)],
                            totalEnergyPotential,
                            ElasticsearchConstants.DECIMAL_PLACES, false)
                    })
                    .ToList()
            }).ToListAsync(cancellationToken: cancellationToken);
        foreach (var category in categoryStructure)
        {
            category.Value += category.Children.Sum(x => x.Value);
            category.Percentage += category.Children.Sum(x => x.Percentage);
        }

        return categoryStructure;
    }
}