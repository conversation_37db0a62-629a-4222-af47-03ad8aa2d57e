using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;
using AEPExplorer.Service.Queries.Filter;

namespace AEPExplorer.Service.Queries.Overview;

public class LossByCategoryReportQuery : MediatR.IRequest<List<NameValuePercentageWithChildren>>
{
    public Query Query { get; init; }
}

public class LossByCategoryReportQueryHandler(IElasticClient elasticClient, IMediator mediator, AepExplorerDbContext dbContext) : IRequestHandler<LossByCategoryReportQuery, List<NameValuePercentageWithChildren>>
{
    public async Task<List<NameValuePercentageWithChildren>> Handle(LossByCategoryReportQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        if (request.Query.FilteredCategories)
        {
            var categoryFilter = await mediator.Send(new CategoryFilterQuery(), cancellationToken);
            var level2List = categoryFilter.SelectMany(x => x.Children.SelectMany(c => c.Children.Select(s => new
            {
                SubcategoryId = s.Value,
                ParentId = x.Value
            }))).ToList();

            var categoryList = categoryFilter.SelectMany(x => x.Children.SelectMany(c => c.Children.Select(s => new
            {
                SubcategoryId = s.Value,
                ParentId = c.Value
            }))).ToList();

            var subcategoryList = categoryFilter.SelectMany(x => x.Children.SelectMany(c => c.Children.Select(s => new
            {
                SubcategoryId = s.Value,
                ParentId = s.Value
            }))).ToList();

            subcategoryList.AddRange(categoryList);
            subcategoryList.AddRange(level2List);
            var filteredList = subcategoryList.Where(x => request.Query.Category.Contains(x.ParentId));
            request.Query.Category = filteredList.Select(x => x.SubcategoryId).Distinct().ToList();
        }

        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, nameof(AepReadData.SubcategoryId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);

        var subcategoryIds = items.Select(x => x.Key).ToList();
        var categoryStructure = await dbContext.Subcategories.Where(x => subcategoryIds.Contains(x.Id) && !CategoryHelper.RunSubcategories.Contains(x.Id))
            .Select(x => new
            {
                x.Id,
                x.Name,
                x.CategoryId,
                CategoryName = x.Category.Name,
                x.Category.Level4Id,
                Level4Name = x.Category.Level4.Name,
                x.Category.Level4.Level3Id,
                Level3Name = x.Category.Level4.Level3.Name
            })
            .ToListAsync(cancellationToken);

        var totalEnergyPotential = items.Sum(x => x.Value[nameof(AepReadData.EnergyPotential)]);

        var categories = categoryStructure
            .Select(x => new NameValuePercentageWithChildren
            {
                Id = x.CategoryId,
                Name = x.CategoryName,
                Percentage = 0.0,
                Value = 0.0
            }).DistinctBy(x => x.Id).ToList();

        foreach (var category in categories)
        {
            category.Children = categoryStructure.Where(x => x.CategoryId == category.Id).Select(x => new NameValuePercentageWithChildren
            {
                Id = x.Id,
                Name = x.Name,
                Value = (items[x.Id][nameof(AepReadData.EnergyPotential)] - items[x.Id][nameof(AepReadData.ActualEnergy)]).Round(2),
                Percentage = ConvertExtensions.CalculatePercentage(items[x.Id][nameof(AepReadData.EnergyPotential)] - items[x.Id][nameof(AepReadData.ActualEnergy)], totalEnergyPotential)
            }).ToList();
            category.Value = category.Children.Sum(x => x.Value);
            category.Percentage = category.Children.Sum(x => x.Percentage);
        }

        var level4List = categoryStructure.Select(x => new NameValuePercentageWithChildren
        {
            Id = x.Level4Id,
            Name = x.Level4Name,
            Percentage = 0.0,
            Value = 0.0,
            Children = new List<NameValuePercentageWithChildren>()
        }).DistinctBy(x => x.Id).ToList();

        foreach (var level4 in level4List)
        {
            var categoryIds = categoryStructure.Where(x => x.Level4Id == level4.Id).Select(x => x.CategoryId).ToList();
            level4.Children = categories.Where(x => categoryIds.Contains(x.Id)).ToList();
            level4.Value = level4.Children.Sum(x => x.Value);
            level4.Percentage = level4.Children.Sum(x => x.Percentage);
        }

        var level3List = categoryStructure.Select(x => new NameValuePercentageWithChildren
        {
            Id = x.Level3Id,
            Name = x.Level3Name,
            Percentage = 0.0,
            Value = 0.0,
            Children = new List<NameValuePercentageWithChildren>()
        }).DistinctBy(x => x.Id).ToList();

        foreach (var level3 in level3List)
        {
            var level4Ids = categoryStructure.Where(x => x.Level3Id == level3.Id).Select(x => x.Level4Id).ToList();
            level3.Children = level4List.Where(x => level4Ids.Contains(x.Id)).ToList();
            level3.Value = level3.Children.Sum(x => x.Value);
            level3.Percentage = level3.Children.Sum(x => x.Percentage);
        }

        var result = new List<NameValuePercentageWithChildren>
        {
            new()
            {
                Id = new Guid(ConvertExtensions.Md5("OPERATIVE (IAO)")),
                Name = "OPERATIVE (IAO)",
                Value = level3List
                    .Where(x => x.Id == CategoryHelper.LEVEL3_IN_SERVICE_ID || x.Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                    .Sum(x=>x.Value),
                Percentage = level3List
                    .Where(x => x.Id == CategoryHelper.LEVEL3_IN_SERVICE_ID || x.Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                    .Sum(x=>x.Percentage),
                Children = level3List
                    .Where(x => x.Id == CategoryHelper.LEVEL3_IN_SERVICE_ID || x.Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                    .ToList()
            },
            new()
            {
                Id = new Guid(ConvertExtensions.Md5("NON-OPERATIVE (IANO)")),
                Name = "NON-OPERATIVE (IANO)",
                Value = level3List
                    .Where(x => x.Id != CategoryHelper.LEVEL3_IN_SERVICE_ID && x.Id != CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                    .Sum(x=>x.Value),
                Percentage = level3List
                    .Where(x => x.Id != CategoryHelper.LEVEL3_IN_SERVICE_ID && x.Id != CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                    .Sum(x=>x.Percentage),
                Children = level3List
                    .Where(x => x.Id != CategoryHelper.LEVEL3_IN_SERVICE_ID && x.Id != CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                    .ToList()
            }
        };

        return result;
    }
}