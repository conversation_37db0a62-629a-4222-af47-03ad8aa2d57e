using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class PlatformLossesPerSubcategoryQuery : MediatR.IRequest<List<NameValuePercentageWithChildren>>
{
    public Query Query { get; init; }
}

public class PlatformLossesPerSubcategoryQueryHandler : IRequestHandler<PlatformLossesPerSubcategoryQuery, List<NameValuePercentageWithChildren>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public PlatformLossesPerSubcategoryQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<NameValuePercentageWithChildren>> Handle(PlatformLossesPerSubcategoryQuery request, CancellationToken cancellationToken)
    {
        var aggregations = new Dictionary<string, IAggregationContainer>();
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        foreach (var propertyName in sumPropertyNames)
        {
            aggregations.Add(propertyName, new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });
        }

        request.Query.Category = FilterHelper.MapToCategories(request.Query.Category, _dbContext);

        var allCategoriesRequest = request.Query.DeepCopy();
        allCategoriesRequest.Category = new List<Guid>();

        var items = await ElasticsearchSumService.SumByGroupAsync(
            _elasticClient, allCategoriesRequest, nameof(AepReadData.SubcategoryId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);

        var subcategoriesStructure = await _dbContext.Subcategories.Where(x => !CategoryHelper.RunSubcategories.Contains(x.Id)).ToListAsync(cancellationToken);
        var subcategoryIdsFromQueryCategories = await _dbContext.Categories.Where(x => request.Query.Category.Contains(x.Id)).SelectMany(y => y.Subcategories.Select(z => z.Id)).ToListAsync(cancellationToken);
        var subcategoryIdsFromQuery = subcategoriesStructure.Where(x => request.Query.Category.Contains(x.Id)).Select(y => y.Id).ToList();

        var subcategoriesData = items.Select(x => new SubcategoryProduction
        {
            Id = x.Key,
            ActualProduction = x.Value[nameof(AepReadData.ActualEnergy)],
            EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)]
        }).ToList();

        var result = new List<NameValuePercentageWithChildren>();

        var energyPotential = subcategoriesData.Sum(x => x.EnergyPotential);

        var subcategoriesToShow = subcategoriesStructure;

        if (request.Query.Category.Count > 0)
        {
            var allSubcategories = subcategoryIdsFromQuery.Concat(subcategoryIdsFromQueryCategories).ToList();
            energyPotential = subcategoriesData.Where(x => allSubcategories.Contains(x.Id)).Sum(y => y.EnergyPotential);
            subcategoriesToShow = subcategoriesToShow.Where(x => allSubcategories.Contains(x.Id)).ToList();
        }

        foreach (var subcategory in subcategoriesToShow)
        {
            var targetSubcategory = subcategoriesData.FirstOrDefault(x => x.Id == subcategory.Id);
            var loss = (targetSubcategory?.EnergyPotential ?? 0) - (targetSubcategory?.ActualProduction ?? 0);

            result.Add(new NameValuePercentageWithChildren
            {
                Id = subcategory.Id,
                Name = subcategory.Name,
                Value = loss,
                Percentage = ConvertExtensions.CalculatePercentage(loss, energyPotential, 2)
            });
        }

        return result;
    }
}