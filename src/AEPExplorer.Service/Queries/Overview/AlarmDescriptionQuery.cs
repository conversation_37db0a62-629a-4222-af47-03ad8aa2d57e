using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Overview;

public class AlarmDescriptionQuery : MediatR.IRequest<DescriptionModel>
{
    public string AlarmId { get; init; }
}

public class AlarmDescriptionQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<AlarmDescriptionQuery, DescriptionModel>
{
    public async Task<DescriptionModel> Handle(AlarmDescriptionQuery request, CancellationToken cancellationToken)
    {
        var alarm = await dbContext.AlarmDescriptions.FirstOrDefaultAsync(x => x.Alarm == request.AlarmId, cancellationToken: cancellationToken);

        return new DescriptionModel
        {
            Description = alarm?.Description ?? "Unknown",
            AlarmId = request.AlarmId,
            FunctionalSystem = alarm?.FunctionalSystem ?? "Unknown",
        };
    }
}