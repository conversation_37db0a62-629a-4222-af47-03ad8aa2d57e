using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class LossByPlatformQuery : MediatR.IRequest<List<LossesByPlatformModel>>
{
    public Query Query { get; init; }
}

public class LossByPlatformQueryHandler : IRequestHandler<LossByPlatformQuery, List<LossesByPlatformModel>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public LossByPlatformQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<LossesByPlatformModel>> Handle(LossByPlatformQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE, Field = new Field(nameof(AepReadData.TurbineId).ToLowerFirstChar())
            }
        });
        var items = await ElasticsearchSumService.SumByGroupAsync(
            _elasticClient, request.Query, nameof(AepReadData.TurbinePlatformId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION,
            lossesType: request.Query.FilteredCategories ? LossesTypeEnum.Detailed : LossesTypeEnum.TotalLosses);

        request.Query.Category = new List<Guid>();
        aggregations = new Dictionary<string, IAggregationContainer>
        {
            { nameof(AepReadData.EnergyPotential), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.EnergyPotential), nameof(AepReadData.EnergyPotential).ToLowerFirstChar()) } },
            { nameof(AepReadData.DataCoverage), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.DataCoverage), nameof(AepReadData.DataCoverage).ToLowerFirstChar()) } }
        };

        var dataCoverageItems = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, request.Query, nameof(AepReadData.TurbinePlatformId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION,
            lossesType: LossesTypeEnum.TotalLosses);
        var turbinePlatformIds = items.Select(x => x.Key).ToList();
        var turbinePlatforms = await _dbContext.TurbinePlatforms.Where(x => turbinePlatformIds.Contains(x.Id)).ToListAsync(cancellationToken);

        return (from item in items
            let actualEnergy = item.Value[nameof(AepReadData.ActualEnergy)]
            let energyPotential = item.Value[nameof(AepReadData.EnergyPotential)]
            let losses = energyPotential - actualEnergy
            let numberOfTurbines = (int)item.Value["NumberOfTurbines"]
            let dataCoverage = dataCoverageItems[item.Key][nameof(AepReadData.DataCoverage)] / (numberOfTurbines * request.Query.TotalDays)
            let totalEnergyPotential = dataCoverageItems[item.Key][nameof(AepReadData.EnergyPotential)]
            select new LossesByPlatformModel
            {
                Id = item.Key,
                Name = turbinePlatforms.FirstOrDefault(x => x.Id == item.Key)?.Name ?? "Unknown",
                Value = losses,
                Percentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential),
                DataCoverage = dataCoverage.ToPercentage(),
                TurbinesCount = numberOfTurbines,
                Children = new List<NameValuePercentageWithChildren>()
            }).ToList();
    }
}