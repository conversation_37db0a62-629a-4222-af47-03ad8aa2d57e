using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Overview;

public class PlatformProductionAndLossesQuery : MediatR.IRequest<List<PlatformProdutionAndLosses>>
{
    public Query Query { get; init; }
}

public class PlatformProductionAndLossesQueryHandler : IRequestHandler<PlatformProductionAndLossesQuery, List<PlatformProdutionAndLosses>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public PlatformProductionAndLossesQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<PlatformProdutionAndLosses>> Handle(PlatformProductionAndLossesQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string> {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE,
                Field = new Field(nameof(AepReadData.TurbineId).ToLowerFirstChar())
            }
        });

        request.Query.Category = FilterHelper.MapToCategories(request.Query.Category, _dbContext);

        var items = await ElasticsearchSumService.SumByGroupAsync(
            _elasticClient, request.Query, nameof(AepReadData.TurbinePlatformId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION,
            lossesType: LossesTypeEnum.TotalLosses);

        request.Query.Category = new List<Guid>();
        aggregations = new Dictionary<string, IAggregationContainer>
        {
            { nameof(AepReadData.ActualEnergy), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.ActualEnergy), nameof(AepReadData.ActualEnergy).ToLowerFirstChar()) } },
            { nameof(AepReadData.EnergyPotential), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.EnergyPotential), nameof(AepReadData.EnergyPotential).ToLowerFirstChar()) } },
            { nameof(AepReadData.DataCoverage), new AggregationContainer { Sum = new SumAggregation(nameof(AepReadData.DataCoverage), nameof(AepReadData.DataCoverage).ToLowerFirstChar()) } }
        };

        var totalItems = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, request.Query, nameof(AepReadData.TurbinePlatformId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION,
            lossesType: LossesTypeEnum.TotalLosses);
        var turbinePlatformIds = items.Select(x => x.Key).ToList();
        var turbinePlatforms = await _dbContext.TurbinePlatforms.Where(x => turbinePlatformIds.Contains(x.Id)).ToListAsync(cancellationToken);


        var result = new List<PlatformProdutionAndLosses>();

        foreach (var item in items)
        {
            var actualProduction = item.Value[nameof(AepReadData.ActualEnergy)];
            var energyPotential = item.Value[nameof(AepReadData.EnergyPotential)];
            var losses = energyPotential - actualProduction;
            var numberOfTurbines = (int)item.Value["NumberOfTurbines"];
            var dataCoverage = totalItems[item.Key][nameof(AepReadData.DataCoverage)] / (numberOfTurbines * request.Query.TotalDays);
            var totalEnergyPotential = totalItems[item.Key][nameof(AepReadData.EnergyPotential)];
            var totalActualEnergy = totalItems[item.Key][nameof(AepReadData.ActualEnergy)];
            result.Add(new PlatformProdutionAndLosses
            {
                Id = item.Key,
                Name = turbinePlatforms.FirstOrDefault(x => x.Id == item.Key)?.Name ?? "Unknown",
                EnergyPotential = new ValuePercentage { Value = energyPotential, Percentage = 100 },
                Production = new ValuePercentage { Value = totalActualEnergy, Percentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential, 2) },
                Losses = new ValuePercentage { Value = losses, Percentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential, 2) },
                TurbinesCount=numberOfTurbines,
                DataCoverage= dataCoverage
            });
        }

        return result;
    }
}