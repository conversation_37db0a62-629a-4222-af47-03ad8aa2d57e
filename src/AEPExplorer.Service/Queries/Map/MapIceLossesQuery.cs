using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Map;

public class MapIceLossesQuery : MediatR.IRequest<List<MapModelIceLosses>>
{
    public Query Query { get; init; }
    public string AepElasticsearchDataProperty { get; init; }
    public Guid UserId { get; init; }
}

public class MapIceLossesQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<MapIceLossesQuery, List<MapModelIceLosses>>
{
    public async Task<List<MapModelIceLosses>> Handle(MapIceLossesQuery request, CancellationToken cancellationToken)
    {
        var performanceSetting = dbContext.Performance.FirstOrDefault(x => x.UserId == request.UserId);
        double limit1, limit2;
        if (performanceSetting != null)
        {
            limit1 = performanceSetting.Limit1;
            limit2 = performanceSetting.Limit2;
        }
        else
        {
            (limit1, limit2) = MapHelper.GetDefaultPerformanceLimit();
        }

        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var queryIce = request.Query;
        queryIce.Category = CategoryHelper.ICE_TURBINE_CATEGORIES.ToList();
        
        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, queryIce,
            request.AepElasticsearchDataProperty, nameof(AepReadData.SubcategoryId), aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);

        request.Query.Category = new List<Guid>();
        var totalItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, request.AepElasticsearchDataProperty, aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION,
            lossesType: LossesTypeEnum.TotalLosses);
        
        var itemPropertyIds = items.Select(x => x.Key).ToList();

        var itemsCount = new Dictionary<Guid, Dictionary<string, double>>(); 
        var childName = MapHelper.GetChildProperty(request.AepElasticsearchDataProperty);
        if (!string.IsNullOrWhiteSpace(childName))
        {
            aggregations = new Dictionary<string, IAggregationContainer>();
            aggregations.Add("NumberOfChildren", new AggregationContainer
            {
                Terms = new TermsAggregation("NumberOfChildren") { Size = ElasticsearchConstants.BUCKET_SIZE, Field = new Field(childName.ToLowerFirstChar()) }
            });
            itemsCount = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, request.AepElasticsearchDataProperty, aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION);
        }

        var markers = request.AepElasticsearchDataProperty switch
        {
            nameof(AepReadData.RegionId) => await dbContext.Regions.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                x.Longitude,
                x.Latitude
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.CountryId) => await dbContext.Countries.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                x.Longitude,
                x.Latitude
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.SiteId) => await dbContext.Sites.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                x.Longitude,
                x.Latitude
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.TurbineId) => await dbContext.Turbines.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                x.Longitude,
                x.Latitude
            }).ToListAsync(cancellationToken: cancellationToken),
            _ => throw new ArgumentOutOfRangeException()
        };
        var result = new List<MapModelIceLosses>();
        var categoryStructure = await dbContext.Subcategories
            .Where(x => CategoryHelper.ICE_TURBINE_CATEGORIES.Contains(x.Id))
            .Select(x => new
            {
                x.Id,
                x.Name,
            })
            .ToListAsync(cancellationToken);
        foreach (var item in items)
        {
            var subcategoryIds = item.Value.Where(x => !CategoryHelper.RunSubcategories.Contains(x.Key)).Select(x => new
            {
                Id = x.Key,
                ActualProduction = x.Value[nameof(AepReadData.ActualEnergy)],
                EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)]
            }).ToList();
            var actualProduction = subcategoryIds.Sum(x => x.ActualProduction);
            var energyPotential = subcategoryIds.Sum(x => x.EnergyPotential);
            var losses = energyPotential - actualProduction;
            var totalEnergyPotential = totalItems[item.Key][nameof(AepReadData.EnergyPotential)];
            var totalActualEnergy = totalItems[item.Key][nameof(AepReadData.ActualEnergy)];
            var categories = new List<NameValuePercentageWithChildren>();
            foreach (var itemCategory in categoryStructure)
            {
                var loss = subcategoryIds.Where(x => x.Id ==itemCategory.Id).Sum(x => x.EnergyPotential - x.ActualProduction);
                categories.Add(new NameValuePercentageWithChildren
                {
                    Id = itemCategory.Id,
                    Name = itemCategory.Name,
                    Value = loss,
                    Percentage = ConvertExtensions.CalculatePercentage(loss, totalEnergyPotential)
                });
            }

            var marker = markers.FirstOrDefault(x => x.Id == item.Key);
            itemsCount.TryGetValue(item.Key, out var itemCount);
            var numberOfChildren = itemCount != null ? (itemCount.TryGetValue("NumberOfChildren", out var value) ? (int)value : 0) : 0;
            var performancePercentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential);
            var performance = MapHelper.GetPerformance(performancePercentage, limit1, limit2);
            result.Add(new MapModelIceLosses
            {
                Id = item.Key,
                Name = marker?.Name ?? "Unknown",
                Losses = losses,
                Latitude = marker?.Latitude ?? 0.0,
                Longitude = marker?.Longitude ?? 0.0,
                Performance = performance,
                PerformanceValue = performancePercentage,
                NumberOfChildren = numberOfChildren,
                Data = categories
            });
        }

        return result;
    }
}