using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Map;

public class MapTotalLossesQuery : MediatR.IRequest<List<MapModelTotalLosses>>
{
    public Query Query { get; init; }
    public string AepElasticsearchDataProperty { get; init; }
    public Guid UserId { get; init; }
}

public class MapQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<MapTotalLossesQuery, List<MapModelTotalLosses>>
{
    public async Task<List<MapModelTotalLosses>> Handle(MapTotalLossesQuery request, CancellationToken cancellationToken)
    {
        var performanceSetting = dbContext.Performance.FirstOrDefault(x => x.UserId == request.UserId);
        double limit1, limit2;
        if (performanceSetting != null)
        {
            limit1 = performanceSetting.Limit1;
            limit2 = performanceSetting.Limit2;
        }
        else
        {
            (limit1, limit2) = MapHelper.GetDefaultPerformanceLimit();
        }

        var childName = MapHelper.GetChildProperty(request.AepElasticsearchDataProperty);

        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        if (!string.IsNullOrWhiteSpace(childName))
        {
            aggregations.Add("NumberOfChildren", new AggregationContainer
            {
                Terms = new TermsAggregation("NumberOfChildren") { Size = ElasticsearchConstants.BUCKET_SIZE, Field = new Field(childName.ToLowerFirstChar()) }
            });
        }

        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, request.AepElasticsearchDataProperty, aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION,
            lossesType: request.Query.FilteredCategories ? LossesTypeEnum.Detailed : LossesTypeEnum.TotalLosses);

        request.Query.Category = new List<Guid>();
        var totalItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, request.AepElasticsearchDataProperty, aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION,
            lossesType: LossesTypeEnum.TotalLosses);
        
        var itemPropertyIds = items.Select(x => x.Key).ToList();

        var markers = request.AepElasticsearchDataProperty switch
        {
            nameof(AepReadData.RegionId) => await dbContext.Regions.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                x.Longitude,
                x.Latitude
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.CountryId) => await dbContext.Countries.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                x.Longitude,
                x.Latitude
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.SiteId) => await dbContext.Sites.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                x.Longitude,
                x.Latitude
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.TurbineId) => await dbContext.Turbines.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                x.Longitude,
                x.Latitude
            }).ToListAsync(cancellationToken: cancellationToken),
            _ => throw new ArgumentOutOfRangeException()
        };

        var result = new List<MapModelTotalLosses>();

        foreach (var item in items)
        {
            var actualEnergy = item.Value[nameof(AepReadData.ActualEnergy)];
            var energyPotential = item.Value[nameof(AepReadData.EnergyPotential)];
            var losses = energyPotential - actualEnergy;
            var numberOfChildren = item.Value.TryGetValue("NumberOfChildren", out var value) ? (int)value : 0;
            var totalEnergyPotential = totalItems[item.Key][nameof(AepReadData.EnergyPotential)];
            var totalActualEnergy = totalItems[item.Key][nameof(AepReadData.ActualEnergy)];
            var performancePercentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential);
            var performance = MapHelper.GetPerformance(performancePercentage, limit1, limit2);
            var marker = markers.FirstOrDefault(x => x.Id == item.Key);
            result.Add(new MapModelTotalLosses
            {
                Id = item.Key,
                Name = marker?.Name ?? "Unknown",
                NumberOfChildren = numberOfChildren,
                Latitude = marker?.Latitude ?? 0.0,
                Longitude = marker?.Longitude ?? 0.0,
                Performance = performance,
                PerformanceValue = performancePercentage,
                Data = new List<NameValuePercentageWithChildren>
                {
                    new()
                    {
                        Id = new Guid(ConvertExtensions.Md5(nameof(AepReadData.ActualEnergy))),
                        Name = nameof(AepReadData.ActualEnergy).ToSeparateWordsByCapitalLetter(),
                        Percentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential),
                        Value = actualEnergy
                    },
                    new()
                    {
                        Id = new Guid(ConvertExtensions.Md5("Lost Energy")),
                        Name = "Lost Energy",
                        Percentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential),
                        Value = losses
                    }
                }
            });
        }

        return result;
    }
}