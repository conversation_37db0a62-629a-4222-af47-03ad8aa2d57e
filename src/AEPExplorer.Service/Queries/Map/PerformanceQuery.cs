using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries;

public class PerformanceQuery : MediatR.IRequest<PerformanceModel>
{
    public bool DefaultValue { get; set; }
    public Guid UserId { get; set; }
}

public class PerformanceQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<PerformanceQuery, PerformanceModel>
{
    public async Task<PerformanceModel> Handle(PerformanceQuery request, CancellationToken cancellationToken)
    {
        var (limit1, limit2) = MapHelper.GetDefaultPerformanceLimit();

        if (!request.DefaultValue)
        {
            var performance = await dbContext.Performance.FirstOrDefaultAsync(x => x.UserId == request.UserId, cancellationToken);
            if (performance != null)
            {
                limit1 = performance.Limit1;
                limit2 = performance.Limit2;
            }
        }

        return new PerformanceModel
        {
            Limit1 = Math.Round(limit1 * 100, 0),
            Limit2 = Math.Round(limit2 * 100, 0)
        };
    }
}