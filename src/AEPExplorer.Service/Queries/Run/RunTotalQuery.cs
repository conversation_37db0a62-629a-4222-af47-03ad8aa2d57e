using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Run;

public class RunTotalQuery : MediatR.IRequest<List<RunTotal>>
{
    public Query Query { get; set; }
}

public class RunTotalQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<RunTotalQuery, List<RunTotal>>
{
    public async Task<List<RunTotal>> Handle(RunTotalQuery request, CancellationToken cancellationToken)
    {
        var (filterBy, groupProperty) = FilterHelper.GetFilterGroupProperty(request.Query);

        if (filterBy == null)
        {
            return [];
        }

        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage),
            nameof(AepReadData.DurationInHours)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer
            {
                Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
            });

        var result = new List<RunTotal>();
        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query,
            groupProperty,
            aggregations,
            lossesType: LossesTypeEnum.RunTopic);
        var itemPropertyIds = items.Select(x => x.Key).ToList();
        var bars = groupProperty switch
        {
            nameof(AepReadData.CustomerId) => await dbContext.Customers.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.RegionId) => await dbContext.Regions.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.CountryId) => await dbContext.Countries.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.SiteId) => await dbContext.Sites.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.TurbineId) => await dbContext.Turbines.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name
            }).ToListAsync(cancellationToken: cancellationToken),
            _ => throw new ArgumentOutOfRangeException()
        };

        foreach (var item in items)
        {
            var actualEnergy = item.Value[nameof(AepReadData.ActualEnergy)];
            var energyPotential = item.Value[nameof(AepReadData.EnergyPotential)];
            var durationInHours = item.Value[nameof(AepReadData.DurationInHours)];
            var loss = energyPotential - actualEnergy;

            result.Add(new RunTotal
            {
                Name = bars.FirstOrDefault(x => x.Id == item.Key)?.Name ?? "Unknown",
                Value = loss,
                Percentage = ConvertExtensions.CalculatePercentage(loss, energyPotential),
                DurationInHours = durationInHours,
                ProductionRatio = ConvertExtensions.CalculatePercentage(actualEnergy, energyPotential)
            });
        }

        return result;
    }
}