using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Run;

public class RunCumulativeQuery : MediatR.IRequest<ItemsDates>
{
    public Query Query { get; init; }
}

public class RunCumulativeQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<RunCumulativeQuery, ItemsDates>
{
    public async Task<ItemsDates> Handle(RunCumulativeQuery request, CancellationToken cancellationToken)
    {
        var (filterBy, comparisonItems) = FilterHelper.GetFilterBy(request.Query);
        var result = new ItemsDates();

        if (filterBy == null && !comparisonItems.Any())
        {
            return result;
        }

        var lines = filterBy switch
        {
            FilterByEnum.Customer => await dbContext.Customers.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Region => await dbContext.Regions.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Country => await dbContext.Countries.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
                {
                    x.Id,
                    x.Name,
                }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Site => await dbContext.Sites.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Turbine => await dbContext.Turbines.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            _ => throw new ArgumentOutOfRangeException()
        };
        result.Items = [];
        foreach (var comparisonItem in comparisonItems)
        {
            result.Items.Add(lines.FirstOrDefault(x => x.Id == comparisonItem)?.Name ?? "Unknown");
        }

        result.Dates = new List<DateValues>();
        if (request.Query.DateFrom == null || request.Query.DateTo == null)
        {
            var defaultDateRange = await ElasticsearchGeneralService.GetDateRange(elasticClient);
            request.Query.DateFrom = defaultDateRange.From;
            request.Query.DateTo = defaultDateRange.To;
        }

        for (var dt = request.Query.DateFrom.Value; dt <= request.Query.DateTo.Value; dt = dt.AddDays(1))
        {
            result.Dates.Add(new DateValues
            {
                D = dt,
                V = []
            });
        }

        var runCumulativeQuery = request.Query;

        for (var i = 0; i < comparisonItems.Count; i++)
        {
            var comparisonItem = comparisonItems[i];
            runCumulativeQuery = FilterHelper.GetSingleFilterQuery(runCumulativeQuery, filterBy.Value, comparisonItem);
            var script = $"doc['{nameof(AepReadData.EnergyPotential).ToLowerFirstChar()}'].value - doc['{nameof(AepReadData.ActualEnergy).ToLowerFirstChar()}'].value";
            var itemRunValues = await ElasticsearchSumService.CumulateSumByDateHistogramAsync(
                elasticClient, runCumulativeQuery, script,
                lossesType: LossesTypeEnum.RunTopic);

            foreach (var d in result.Dates)
            {
                var value = itemRunValues.FirstOrDefault(x => x.Date == d.D);
                if (value != null)
                {
                    d.V.Add(value.Value);
                }
                else
                {
                    d.V.Add(null);
                }
            }
        }

        return result;
    }
}