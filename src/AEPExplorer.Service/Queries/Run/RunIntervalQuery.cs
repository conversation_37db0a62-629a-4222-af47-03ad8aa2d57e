using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Run;

public class RunIntervalQuery : MediatR.IRequest<ItemsIntervals>
{
    public Query Query { get; set; }
    public DateInterval DateInterval { get; set; }
}

public class RunIntervalQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<RunIntervalQuery, ItemsIntervals>
{
    public async Task<ItemsIntervals> Handle(RunIntervalQuery request, CancellationToken cancellationToken)
    {
        var (filterBy, comparisonItems) = FilterHelper.GetFilterBy(request.Query);
        var result = new ItemsIntervals();

        if (filterBy == null && !comparisonItems.Any())
        {
            return result;
        }

        var bars = filterBy switch
        {            
            FilterByEnum.Customer => await dbContext.Customers.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),

            FilterByEnum.Region => await dbContext.Regions.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Country => await dbContext.Countries.Where(x => comparisonItems.Contains(x.Id)).Select(x =>
                new
                {
                    x.Id,
                    x.Name,
                }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Site => await dbContext.Sites.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Turbine => await dbContext.Turbines.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            _ => throw new ArgumentOutOfRangeException()
        };
        result.Items = new List<string>();
        foreach (var comparisonItem in comparisonItems)
        {
            result.Items.Add(bars.FirstOrDefault(x=>x.Id == comparisonItem)?.Name ?? "Unknown"); 
        }
        
        result.Dates = new List<IntervalValues>();

        var runCumulativeQuery = request.Query;

        for (var i = 0; i < comparisonItems.Count; i++)
        {
            var comparisonItem = comparisonItems[i];
            runCumulativeQuery = FilterHelper.GetSingleFilterQuery(runCumulativeQuery, filterBy.Value, comparisonItem);
            var script = $"doc['{nameof(AepReadData.EnergyPotential).ToLowerFirstChar()}'].value - doc['{nameof(AepReadData.ActualEnergy).ToLowerFirstChar()}'].value";
            var itemRunValues = await ElasticsearchSumService.SumByDateHistogramAsync(elasticClient, runCumulativeQuery, f=>f.Script(script), request.DateInterval, lossesType: LossesTypeEnum.RunTopic);

            if (result.Dates.Count == 0)
            {
                foreach (var item in itemRunValues)
                {
                    result.Dates.Add(new IntervalValues
                    {
                        D = item.Name,
                        V = new List<double?>()
                    });
                }
            }

            foreach (var d in result.Dates)
            {
                var value = itemRunValues.FirstOrDefault(x => x.Name == d.D);
                if (value != null)
                {
                    d.V.Add(value.Value);
                }
                else
                {
                    d.V.Add(null);
                }
            }
        }

        return result;
    }
}