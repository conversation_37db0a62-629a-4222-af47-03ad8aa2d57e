using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;
using Newtonsoft.Json;

namespace AEPExplorer.Service.Queries.Tpc;

public class TpcLinesQuery : MediatR.IRequest<List<TheoreticalPowerCurveLine>>
{
    public List<Guid> TurbineIds { get; init; }
}

public class TpcLinesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<TpcLinesQuery, List<TheoreticalPowerCurveLine>>
{
    public async Task<List<TheoreticalPowerCurveLine>> Handle(TpcLinesQuery request, CancellationToken cancellationToken)
    {
        var query = await dbContext.Turbines
            .Include(x => x.Tpcs)
            .Where(x => request.TurbineIds.Contains(x.Id))
            .SelectMany(turbine => turbine.Tpcs.Select(tpc => new 
            {
                TpcId = tpc.TpcId,
                TurbineId = turbine.Id,
                TurbineName = turbine.Name,
                LineCoordinates = tpc.LineCoordinates
            }))
            .ToListAsync(cancellationToken);

        return query.Select(item => new TheoreticalPowerCurveLine
        {
            Id = item.TpcId,
            TurbineId = item.TurbineId,
            Label = $"Theoretical Power Curve {item.TurbineName}",
            Coordinates = JsonConvert.DeserializeObject<List<WindSpeedPower>>(item.LineCoordinates)
        }).ToList();
    }
}