using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Tpc;

public class TpcTurbineQuery : MediatR.IRequest<List<ValueLabel>>
{
    public Query Query { get; init; }
}

public class TpcTurbineQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<TpcTurbineQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(TpcTurbineQuery request, CancellationToken cancellationToken)
    {
        var turbines = await dbContext.Turbines
            .Include(x => x.Tpcs)
            .Include(x => x.Site)
                .ThenInclude(s => s.Country)
            .Where(x => x.Tpcs.Count != 0
                && (request.Query.Region == null || request.Query.Region.Count == 0 || request.Query.Region.Contains(x.Site.Country.RegionId))
                && (request.Query.Country == null || request.Query.Country.Count == 0 || request.Query.Country.Contains(x.Site.CountryId))
                && (request.Query.Site == null || request.Query.Site.Count == 0 || request.Query.Site.Contains(x.SiteId))
                && (request.Query.TurbineId == null || request.Query.TurbineId.Count == 0 || request.Query.TurbineId.Contains(x.Id)))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);

        return turbines;
    }
}