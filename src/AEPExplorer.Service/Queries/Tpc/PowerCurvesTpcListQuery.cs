using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Tpc;

public class PowerCurvesTpcListQuery : MediatR.IRequest<List<ChildStructure<ValueLabel>>>
{
    public Query Query { get; init; }
}

public class PowerCurvesTpcListQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<PowerCurvesTpcListQuery, List<ChildStructure<ValueLabel>>>
{
    public async Task<List<ChildStructure<ValueLabel>>> Handle(PowerCurvesTpcListQuery request, CancellationToken cancellationToken)
    {
        var turbines = await dbContext.Turbines
            .Include(x => x.Tpcs)
            .Where(x => request.Query.TurbineId.Contains(x.Id) && x.Tpcs.Any())
            .ToListAsync(cancellationToken);

        return turbines.Select(x => new ChildStructure<ValueLabel>
        {
            Value = x.Id,
            Label = x.Name,
            Children = [new ValueLabel { Value = x.Id, Label = $"TPC {x.Name}" }]
        }).ToList();
    }
}