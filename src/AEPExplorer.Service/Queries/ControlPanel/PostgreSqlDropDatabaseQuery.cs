using AEPExplorer.Data.EF;

namespace AEPExplorer.Service.Queries.ControlPanel;

public class PostgreSqlCustomerDownQuery : MediatR.IRequest<bool>
{
}

public class PostgreSqlCustomerDownQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<PostgreSqlCustomerDownQuery, bool>
{
    public async Task<bool> Handle(PostgreSqlCustomerDownQuery request, CancellationToken cancellationToken)
    {
        await dbContext.Database.ExecuteSqlRawAsync($"ALTER TABLE IF EXISTS public.\"Turbine\" DROP CONSTRAINT IF EXISTS \"FK_Turbine_Customer_CustomerId\";", cancellationToken);
        Console.WriteLine($"Dropped FK_Turbine_Customer_CustomerId");
        await dbContext.Database.ExecuteSqlRawAsync($"DROP TABLE IF EXISTS public.\"Customer\";", cancellationToken);
        Console.WriteLine($"Dropped Customer table");
        await dbContext.Database.ExecuteSqlRawAsync($"DROP INDEX IF EXISTS public.\"IX_Turbine_CustomerId\";", cancellationToken);
        Console.WriteLine($"Dropped IX_Turbine_CustomerId");
        await dbContext.Database.ExecuteSqlRawAsync($"ALTER TABLE IF EXISTS public.\"Turbine\" DROP COLUMN IF EXISTS \"CustomerId\";", cancellationToken);
        Console.WriteLine($"Dropped CustomerId column");
        await dbContext.Database.ExecuteSqlRawAsync($"DELETE FROM public.\"__EFMigrationsHistory\" WHERE \"MigrationId\" = '20240927085155_AddedCustomerTable';\n", cancellationToken);
        Console.WriteLine($"Deleted migration");
        return true;
    }
}