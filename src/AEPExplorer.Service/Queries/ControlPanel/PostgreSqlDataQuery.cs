using System.Text.Json;
using AEPExplorer.Data.EF;

namespace AEPExplorer.Service.Queries.ControlPanel;

public class PostgreSqlDataQuery : MediatR.IRequest<string>
{
    public string TableName { get; set; }
}

public class PostgreSqlDataQueryHandler : IRequestHandler<PostgreSqlDataQuery, string>
{
    private readonly AepExplorerDbContext _dbContext;

    public PostgreSqlDataQueryHandler(AepExplorerDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<string> Handle(PostgreSqlDataQuery request, CancellationToken cancellationToken)
    {
        var result = await _dbContext.Subcategories.ToListAsync(cancellationToken);

        return JsonSerializer.Serialize(result);
    }
}