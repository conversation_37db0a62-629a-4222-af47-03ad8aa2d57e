using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Comparison;

public class LossesByCategoryBarChartComparisonQuery : MediatR.IRequest<List<LossesByGroups>>
{
    public Query Query { get; init; }
}

public class LossesByCategoryBarChartComparisonQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    : IRequestHandler<LossesByCategoryBarChartComparisonQuery, List<LossesByGroups>>
{
    public async Task<List<LossesByGroups>> Handle(LossesByCategoryBarChartComparisonQuery request, CancellationToken cancellationToken)
    {
        var result = new List<LossesByGroups>();

        var comparisonQuery = new Query
        {
            Region = request.Query.Region,
            Country = request.Query.Country,
            Site = request.Query.Site,
            TurbineId = request.Query.TurbineId,
            Platform = request.Query.Platform,
            Model = request.Query.Model,
            Category = request.Query.Category,
            DateFrom = request.Query.DateFrom,
            DateTo = request.Query.DateTo
        };

        var (filterBy, comparisonItems) = FilterHelper.GetFilterBy(request.Query);
        if (filterBy == null && !comparisonItems.Any())
        {
            return result;
        }

        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var bars = filterBy switch
        {
            FilterByEnum.Customer => await dbContext.Customers.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Region => await dbContext.Regions.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Country => await dbContext.Countries.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Site => await dbContext.Sites.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Turbine => await dbContext.Turbines.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            _ => throw new ArgumentOutOfRangeException()
        };

        var categoryStructure = await dbContext.Categories.Select(x => new
            {
                x.Id,
                x.Name,
                x.Subcategories
            })
            .ToListAsync(cancellationToken);

        for (var i = 0; i < comparisonItems.Count; i++)
        {
            var comparisonItem = comparisonItems[i];
            comparisonQuery = FilterHelper.GetSingleFilterQuery(comparisonQuery, filterBy.Value, comparisonItem);
            if (request.Query.Category.Contains(CategoryHelper.OPERATIVE_CATEGORY))
            {
                var operative = dbContext.Categories
                    .Where(x => x.Level4.Level3Id == CategoryHelper.LEVEL3_IN_SERVICE_ID
                                || x.Level4.Level3Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                    .Select(x => x.Id)
                    .ToList();
                request.Query.Category.Remove(CategoryHelper.OPERATIVE_CATEGORY);
                request.Query.Category.AddRange(operative);
            }

            if (request.Query.Category.Contains(CategoryHelper.NON_OPERATIVE_CATEGORY))
            {
                var nonOperative = dbContext.Categories
                    .Where(x => x.Level4.Level3Id != CategoryHelper.LEVEL3_IN_SERVICE_ID
                                && x.Level4.Level3Id != CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                    .Select(x => x.Id)
                    .ToList();
                request.Query.Category.Remove(CategoryHelper.NON_OPERATIVE_CATEGORY);
                request.Query.Category.AddRange(nonOperative);
            }
            var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, comparisonQuery, nameof(AepReadData.SubcategoryId), aggregations);
            
            comparisonQuery.Category = new List<Guid>();
            var totalEnergyPotential = await ElasticsearchSumService.SumAsync(elasticClient, comparisonQuery, p => p.Field(x => x.EnergyPotential), lossesType: LossesTypeEnum.TotalLosses);
            
            var categories = new List<NameValuePercentageWithChildren>();

            var subcategoryIds = items.Select(x => new
            {
                Id = x.Key,
                ActualProduction = x.Value[nameof(AepReadData.ActualEnergy)],
                EnergyPotential = x.Value[nameof(AepReadData.EnergyPotential)]
            }).ToList();
            
            foreach (var itemCategory in categoryStructure)
            {
                var loss = subcategoryIds.Where(x => itemCategory.Id ==x.Id || itemCategory.Subcategories.Select(s => s.Id).Contains(x.Id)).Sum(x => x.EnergyPotential - x.ActualProduction);
                categories.Add(new NameValuePercentageWithChildren
                {
                    Id = itemCategory.Id,
                    Name = itemCategory.Name,
                    Value = loss,
                    Percentage = ConvertExtensions.CalculatePercentage(loss, totalEnergyPotential)
                });
            }

            result.Add(new LossesByGroups
            {
                Id = comparisonItem,
                ItemName = bars.FirstOrDefault(x => x.Id == comparisonItem)?.Name ?? "Unknown",
                Categories = categories
            });
        }

        return result;
    }
}