using AEPExplorer.Model.Response;
using iText.IO.Image;
using iText.Layout;
using iText.Layout.Element;

namespace AEPExplorer.Service.Queries.Comparison.Report;

internal class LineChartSection(Document document, ComparisonLineChartData lineChartComparison)
{
    public void AppendToDocument()
    {
        var titleParagraph = new Paragraph("Line Chart");
        titleParagraph.SetFontSize(12);
        document.Add(titleParagraph);

        double[] dates = lineChartComparison.Data.OrderBy(x => x.Date).Select(x => x.Date.ToOADate()).ToArray();
        double[] actual = lineChartComparison.Data.OrderBy(x => x.Date).Select(x => x.Actual).ToArray();
        double[] energyPotential = lineChartComparison.Data.OrderBy(x => x.Date).Select(x => x.EnergyPotential).ToArray();
        double[] losses = lineChartComparison.Data.OrderBy(x => x.Date).Select(x => x.Losses).ToArray();
        var plot = new ScottPlot.Plot();
        //plt.Style()
        plot.Add.Scatter(dates, actual);
        plot.Add.Scatter(dates, energyPotential);
        plot.Add.Scatter(dates, losses);
        // plt.XAxis.DateTimeFormat(true);
        // plt.XAxis.TickLabelStyle(fontName: "tahoma");
        // plt.YAxis.TickLabelStyle(fontName: "tahoma");
        // plt.YAxis2.SetSizeLimit(min: 40);
        var imageBytes = plot.GetImageBytes(700, 300);
        var imageData = ImageDataFactory.Create(imageBytes);
        var image = new Image(imageData);
        document.Add(image);
    }
}