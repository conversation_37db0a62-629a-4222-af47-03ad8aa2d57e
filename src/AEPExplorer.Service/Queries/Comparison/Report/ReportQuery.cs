using AEPExplorer.Model.Request;
using iText.IO.Font;
using iText.Kernel.Events;
using iText.Kernel.Font;
using iText.Kernel.Pdf;
using iText.Layout;

namespace AEPExplorer.Service.Queries.Comparison.Report;

public class ReportQuery : MediatR.IRequest<byte[]>
{
    public Query Query { get; init; }
    public string UserFullName { get; init; }
}

public class ReportQueryHandler(IMediator mediator) : IRequestHandler<ReportQuery, byte[]>
{
    private const string FONT = "Assets/WeissenhofGrotesk.ttf";

    public async Task<byte[]> Handle(ReportQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var stream = new MemoryStream();
            var writer = new PdfWriter(stream);
            var pdfDocument = new PdfDocument(writer.SetSmartMode(true));
            var document = new Document(pdfDocument);
            var pdfFont = PdfFontFactory.CreateFont(FONT, PdfEncodings.WINANSI, PdfFontFactory.EmbeddingStrategy.FORCE_EMBEDDED, true);
            document.SetFont(pdfFont);
            document.SetMargins(130, 40, 40, 40);
            pdfDocument.AddEventHandler(PdfDocumentEvent.END_PAGE, new TextHeaderEventHandler(document, pdfFont, request.UserFullName));

            var inputSection = new InputsSection(document, request.Query, mediator);
            await inputSection.AppendToDocument();

            var productionLossesBarChartComparisonResult = await mediator.Send(new ProductionAndLossesBarChartComparisonQuery { Query = request.Query }, cancellationToken);
            var productionLossesTimelineChartComparisonResult = await mediator.Send(new ProductionAndLossesTimelineComparisonQuery { Query = request.Query }, cancellationToken);
            var lossesByCategoryBarChartComparisonResult = await mediator.Send(new LossesByCategoryBarChartComparisonQuery { Query = request.Query }, cancellationToken);

            document.AddSectionTitle("Line Chart");
            var productionComparisonSection = new ProductionComparisonSection(document, productionLossesTimelineChartComparisonResult);
            productionComparisonSection.AppendToDocument();
            var lossesComparisonSection = new LossesComparisonSection(document, productionLossesTimelineChartComparisonResult);
            lossesComparisonSection.AppendToDocument();
            var stoppedTimeComparisonSection = new StoppedTimeComparisonSection(document, productionLossesTimelineChartComparisonResult);
            stoppedTimeComparisonSection.AppendToDocument();

            foreach (var comparisonItem in productionLossesBarChartComparisonResult)
            {
                document.AddSectionTitle(comparisonItem.Name);
                var productionLossesSection = new ProductionLossesSection(document, comparisonItem);
                productionLossesSection.AppendToDocument();
                var productionLossesTimelineData = productionLossesTimelineChartComparisonResult.ChartData.FirstOrDefault(x => x.ItemName == comparisonItem.Name);
                var lineChartSection = new LineChartSection(document, productionLossesTimelineData);
                lineChartSection.AppendToDocument();
                var lossesByCategoryData = lossesByCategoryBarChartComparisonResult.FirstOrDefault(x => x.ItemName == comparisonItem.Name);
                var lossesBreakdownSection = new LossesBreakdownSection(document, lossesByCategoryData);
                lossesBreakdownSection.AppendToDocument();
            }

            document.Close();
            return stream.ToArray();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
}