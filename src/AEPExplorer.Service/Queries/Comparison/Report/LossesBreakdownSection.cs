using AEPExplorer.Model;
using AEPExplorer.Model.Response;
using iText.IO.Image;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;
using ScottPlot;
using Text = iText.Layout.Element.Text;
using Image = iText.Layout.Element.Image;

namespace AEPExplorer.Service.Queries.Comparison.Report;

internal class LossesBreakdownSection(Document document, LossesByGroups lossesByCategoryData)
{
    public void AppendToDocument()
    {
        var titleParagraph = new Paragraph("Losses Breakdown");
        titleParagraph.SetFontSize(12);
        document.Add(titleParagraph);
        var columns = lossesByCategoryData.Categories.Select(x => (float)x.Percentage).ToArray();
        var table = new iText.Layout.Element.Table(UnitValue.CreatePercentArray(columns))
            .UseAllAvailableWidth()
            .SetMarginBottom(2);
        for (int i = 0; i < columns.Length; i++)
        {
            var cell = new Cell();
            var categoryName = lossesByCategoryData.Categories.Select(x => x.Name).ToArray()[i].ToSeparateWordsByCapitalLetter();
            cell.SetBackgroundColor(LossesData.GetDeviceRgbForCategory(categoryName, 5)).SetHeight(40);
            table.AddCell(cell);
        }

        table.RemoveBorder();
        document.Add(table);

        table = new iText.Layout.Element.Table(UnitValue.CreatePercentArray(new float[] { 24, 24, 4, 24, 24 }))
            .UseAllAvailableWidth()
            .SetMarginBottom(2);
        ScottPlot.Palettes.Category10 palette = new();
        for (int i = 0; i < columns.Length; i++)
        {
            var category = lossesByCategoryData.Categories[i];
            var categoryName = lossesByCategoryData.Categories.Select(x => x.Name).ToArray()[i].ToSeparateWordsByCapitalLetter();
            var cell = new Cell();
            var paragraph = new Paragraph();
            var text = new Text(categoryName);
            paragraph.Add(text).SetTextAlignment(TextAlignment.LEFT).SetFontSize(8);

            Plot plot = new();

            Bar[] bars =
            [
                new()
                {
                    Value = category.Percentage,
                    ValueBase = 0,
                    FillColor = palette.GetColor(0)
                },
                new()
                {
                    Value = 100,
                    ValueBase = category.Percentage,
                    FillColor = palette.GetColor(1)
                }
            ];
            plot.Add.Bars(bars);
            //plot.XAxis.Hide();
            //plot.XAxis2.Hide();
            //plot.YAxis.Hide();
            //plot.YAxis2.Hide();
            //plot.Grid(false);
            // plot.SetAxisLimitsX(0, 100);
            // plot.SetAxisLimitsY(-0.1, 0.1);
            var imageBytes = plot.GetImageBytes(120, 10);
            var imageData = ImageDataFactory.Create(imageBytes);
            var image = new Image(imageData);
            paragraph.Add(image);
            cell.Add(paragraph);
            cell.SetKeepTogether(true);
            table.AddCell(cell);
            cell = new Cell();
            paragraph = new Paragraph();
            text = new Text($"{category.Value:n} MWh ({category.Percentage:n2}%)").SetFontSize(8);
            paragraph.Add(text).SetTextAlignment(TextAlignment.RIGHT);
            cell.SetVerticalAlignment(iText.Layout.Properties.VerticalAlignment.BOTTOM);
            cell.Add(paragraph);
            table.AddCell(cell);
            if (i % 2 == 0)
            {
                paragraph = new Paragraph();
                cell = new Cell();
                cell.Add(paragraph);
                table.AddCell(cell);
            }
        }

        table.RemoveBorder();
        document.Add(table);
    }
}