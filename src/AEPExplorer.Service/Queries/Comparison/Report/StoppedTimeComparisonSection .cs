using AEPExplorer.Model.Response;
using iText.IO.Image;
using iText.Layout;
using iText.Layout.Element;
using ScottPlot;
using Image = iText.Layout.Element.Image;

namespace AEPExplorer.Service.Queries.Comparison.Report;

internal class StoppedTimeComparisonSection(Document document, ProductionAndLossesTimelineComparison productionLossesTimelineChartComparisonResult)
{
    public void AppendToDocument()
    {
        var titleParagraph = new Paragraph("Stoppedge Time");
        titleParagraph.SetFontSize(12);
        document.Add(titleParagraph);
        int color = 0;
        var plot = new Plot();
        var bars = new List<Bar>();
        IPalette palette = new ScottPlot.Palettes.Tsitsulin();
        foreach (var item in productionLossesTimelineChartComparisonResult.DownPeriod)
        {
            for (int i = 0; i < item.Data.Count; i++)
            {
                bars.Add(new Bar
                {
                    Value = item.Data[i].To.AddDays(1).ToOADate(),
                    ValueBase = item.Data[i].From.ToOADate(),
                    FillColor = palette.GetColor(color),
                    // LineWidth = 0,
                    // IsVertical = false,
                    // Thickness = 1.1 / productionLossesTimelineChartComparisonResult.DownPeriod.Count,
                    Position = color
                });
            }
            color++;
        }
        plot.Add.Bars(bars);

        // plt.XAxis.DateTimeFormat(true);
        // plt.XAxis.Line(false);
        // plt.XAxis.TickMarkColor(Color.White);
        // plt.XAxis2.Hide();
        // plt.SetAxisLimitsY(-0.15 * productionLossesTimelineChartComparisonResult.DownPeriod.Count, productionLossesTimelineChartComparisonResult.DownPeriod.Count);
        // plt.YAxis.Hide();
        // plt.YAxis2.Hide();
        // plt.Grid(false);
        var imageBytes = plot.GetImageBytes(700, 20 + productionLossesTimelineChartComparisonResult.DownPeriod.Count * 30);
        var imageData = ImageDataFactory.Create(imageBytes);
        var image = new Image(imageData);
        document.Add(image);
    }
}