using AEPExplorer.Model.Response;
using iText.Kernel.Colors;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;

namespace AEPExplorer.Service.Queries.Comparison.Report;

internal class ProductionLossesSection
{
    private readonly Document document;
    private readonly BarChartComparison barChartComparison;

    public ProductionLossesSection(Document document, BarChartComparison barChartComparison)
    {
        this.document = document;
        this.barChartComparison = barChartComparison;
    }

    public void AppendToDocument()
    {
        var titleParagraph = new Paragraph("Production/Losses");
        titleParagraph.SetFontSize(12);
        document.Add(titleParagraph);
        float[] columns = new float[2];
        columns[0] = (float)barChartComparison.ActualPercentage;
        columns[1] = (float)barChartComparison.LossesPercentage;
        var table = new iText.Layout.Element.Table(UnitValue.CreatePercentArray(columns))
            .UseAllAvailableWidth()
            .SetMarginBottom(2);
        var cell = new Cell();
        cell.SetBackgroundColor(new DeviceRgb(91, 70, 115)).SetHeight(40);
        table.AddCell(cell);
        cell = new Cell();
        cell.SetBackgroundColor(new DeviceRgb(255, 76, 91)).SetHeight(40);
        table.AddCell(cell);
        table.RemoveBorder();
        document.Add(table);

        table = new iText.Layout.Element.Table(UnitValue.CreatePercentArray(3)).UseAllAvailableWidth();
        table.AddCell(new Paragraph(
                new Text("Actual production")
                    .SetFontSize(8)
                    .SetFontColor(new DeviceRgb(91, 70, 115)))
            .SetFixedLeading(10)
            .SetTextAlignment(TextAlignment.LEFT));
        table.AddCell(
            new Paragraph(
                    new Text("Producible")
                        .SetFontSize(8)
                        .SetFontColor(new DeviceRgb(135, 135, 135)))
                .SetFixedLeading(10)
                .SetTextAlignment(TextAlignment.CENTER));
        table.AddCell(
            new Paragraph(
                    new Text("Lost energy")
                        .SetFontSize(8)
                        .SetFontColor(new DeviceRgb(255, 76, 91)))
                .SetFixedLeading(10)
                .SetTextAlignment(TextAlignment.RIGHT));
        table.AddCell(
            new Paragraph(
                    new Text($"{barChartComparison.ActualValue:n} MWh ({barChartComparison.ActualPercentage:n2}%)")
                        .SetFontSize(10)
                        .SetFontColor(new DeviceRgb(91, 70, 115)))
                .SetFixedLeading(10)
                .SetTextAlignment(TextAlignment.LEFT));
        table.AddCell(
            new Paragraph(
                    new Text($"{barChartComparison.EnergyPotentialValue:n} MWh ({barChartComparison.EnergyPotentialPercentage:n2}%)")
                        .SetFontSize(10)
                        .SetFontColor(new DeviceRgb(135, 135, 135)))
                .SetFixedLeading(10)
                .SetTextAlignment(TextAlignment.CENTER));
        table.AddCell(
            new Paragraph(
                    new Text($"{barChartComparison.LossesValue:n} MWh ({barChartComparison.LossesPercentage:n2}%)")
                        .SetFontSize(10)
                        .SetFontColor(new DeviceRgb(255, 76, 91)))
                .SetFixedLeading(10)
                .SetTextAlignment(TextAlignment.RIGHT));
        table.RemoveBorder();
        document.Add(table);
    }
}