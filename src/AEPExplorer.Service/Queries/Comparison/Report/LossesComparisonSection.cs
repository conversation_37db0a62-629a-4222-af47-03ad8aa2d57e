using AEPExplorer.Model.Response;
using iText.IO.Image;
using iText.Layout;
using iText.Layout.Element;
using ScottPlot;
using Image = iText.Layout.Element.Image;

namespace AEPExplorer.Service.Queries.Comparison.Report;

internal class LossesComparisonSection(Document document, ProductionAndLossesTimelineComparison productionLossesTimelineChartComparisonResult)
{
    public void AppendToDocument()
    {
        var titleParagraph = new Paragraph("Losses");
        titleParagraph.SetFontSize(12);
        document.Add(titleParagraph);

        var plot = new Plot();
        var coordinates = new List<Coordinates>();
        
        var dates = productionLossesTimelineChartComparisonResult.ChartData.First().Data.OrderBy(x => x.Date).Select(x => x.Date.ToOADate()).ToArray();
        foreach (var item in productionLossesTimelineChartComparisonResult.ChartData)
        {
            coordinates.AddRange(dates.Select(date => new Coordinates(date, item.Data.Where(x => x.Date.ToOADate() == date).Select(x => x.Losses).FirstOrDefault())));
        }
        plot.Add.Scatter(coordinates);
        // plt.XAxis.DateTimeFormat(true);
        // plt.XAxis.TickLabelStyle(fontName: "tahoma");
        // plt.YAxis.TickLabelStyle(fontName: "tahoma");
        // plt.YAxis2.SetSizeLimit(min: 40);
        var imageBytes = plot.GetImageBytes(700, 300);
        var imageData = ImageDataFactory.Create(imageBytes);
        var image = new Image(imageData);
        document.Add(image);
    }
}