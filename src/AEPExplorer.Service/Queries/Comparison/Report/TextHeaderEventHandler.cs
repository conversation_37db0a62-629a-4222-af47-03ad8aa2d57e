using iText.IO.Image;
using iText.Kernel.Colors;
using iText.Kernel.Events;
using iText.Kernel.Pdf.Canvas;
using iText.Layout.Properties;
using iText.Layout;
using iText.Kernel.Font;
using iText.Layout.Element;
using iText.Kernel.Geom;

namespace AEPExplorer.Service.Queries.Comparison.Report;

class TextHeaderEventHandler : IEventHandler
{
    private readonly string logoBase64 = "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";
    protected Document document;
    private readonly PdfFont pdfFont;
    private string userFullName;

    internal TextHeaderEventHandler(Document document, PdfFont pdfFont, string userFullName)
    {
        this.document = document;
        this.pdfFont = pdfFont;
        this.userFullName = userFullName;
    }

    public void HandleEvent(Event currentEvent)
    {
        var pdfDocumentEvent = (PdfDocumentEvent)currentEvent;
        var pageSize = pdfDocumentEvent.GetPage().GetPageSize();
        var imageBytes = Convert.FromBase64String(logoBase64);
        var imageData = ImageDataFactory.Create(imageBytes);
        var headerX = pageSize.GetLeft() + document.GetLeftMargin();
        var headerY = pageSize.GetTop();

        var canvas = new Canvas(pdfDocumentEvent.GetPage(), pageSize);
        canvas
            .SetFont(pdfFont)
            .ShowTextAligned(
                new Paragraph(
                    new Text("AEP Explorer")
                        .SetBold()
                        .SetFontColor(new DeviceRgb(91, 70, 115))
                        .SetFontSize(18)),
                headerX, headerY - 60, TextAlignment.LEFT)
            .ShowTextAligned(
                new Paragraph(
                    new Text("Comparison data report")
                        .SetFontSize(14)),
                headerX, headerY - 80, TextAlignment.LEFT)
            .ShowTextAligned(
                new Paragraph()
                    .Add(new Text("User: ")
                        .SetFontColor(new DeviceRgb(119, 118, 125))
                        .SetFontSize(10))
                    .Add(new Text(userFullName)
                        .SetFontSize(10)
                        .SetBold()),
                headerX, headerY - 105, TextAlignment.LEFT)
            .ShowTextAligned(
                new Paragraph()
                    .Add(new Text("Date: ")
                        .SetFontColor(new DeviceRgb(119, 118, 125))
                        .SetFontSize(10))
                    .Add(new Text(DateTime.Now.ToShortDateString())
                        .SetFontSize(10)
                        .SetBold()),
                headerX, headerY - 120, TextAlignment.LEFT)
            .Close();
        var rectangle = new Rectangle(370, headerY - 70, imageData.GetWidth() / 2.5f, imageData.GetHeight() / 2.5f);
        var pdfCanvas = new PdfCanvas(pdfDocumentEvent.GetPage())
            .AddImageFittedIntoRectangle(imageData, rectangle, false);
    }
}