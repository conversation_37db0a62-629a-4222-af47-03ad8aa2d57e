using AEPExplorer.Model.Request;
using AEPExplorer.Service.Queries.Coverage;
using AEPExplorer.Service.Queries.Structure;
using iText.Kernel.Colors;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;

namespace AEPExplorer.Service.Queries.Comparison.Report;

internal class InputsSection
{
    private readonly Document _document;
    private readonly Query _query;
    private readonly IMediator _mediator;

    public InputsSection(Document document, Query query, IMediator mediator)
    {
        _document = document;
        _query = query;
        _mediator = mediator;
    }

    public async Task AppendToDocument()
    {
        _document.SetFontSize(10);

        _document.AddSectionTitle("Inputs");

        var coverage = await _mediator.Send(new CoverageQuery { Query = _query });
        var table = new iText.Layout.Element.Table(UnitValue.CreatePercentArray(3)).UseAllAvailableWidth().SetMarginBottom(10);
        table.AddCell(new Paragraph("Data Range:").SetFixedLeading(1).SetFontColor(new DeviceRgb(119, 118, 125)))
            .AddCell(new Paragraph("Calculation Method").SetFixedLeading(1).SetFontColor(new DeviceRgb(119, 118, 125)))
            .AddCell(new Paragraph("Data Coverage").SetFixedLeading(1).SetFontColor(new DeviceRgb(119, 118, 125)));
        table.AddCell(new Paragraph($"{_query.DateFrom?.ToShortDateString()} - {_query.DateTo?.ToShortDateString()}").SetBold())
            .AddCell(new Paragraph("Prioritized Combination").SetBold())
            .AddCell(new Paragraph($"{coverage.DataCoverage}%").SetBold());
        table.RemoveBorder();
        _document.Add(table);

        table = new iText.Layout.Element.Table(UnitValue.CreatePercentArray(6)).UseAllAvailableWidth();
        table.AddHeaderCell("Region");
        table.AddHeaderCell("Country");
        table.AddHeaderCell("Site");
        table.AddHeaderCell("Platform");
        table.AddHeaderCell("Turbine");
        table.AddHeaderCell("Category");
        table.FormatHeaderRow();
        var regions = await _mediator.Send(new RegionNamesQuery { IdLIst = _query.Region });
        var paragraph = new Paragraph().SetFixedLeading(10);
        regions.ForEach(x =>
        {
            paragraph.Add(new Paragraph(x.Label));
            paragraph.Add(new Text("\n"));
        });
        table.AddCell(paragraph);
        var countries = await _mediator.Send(new CountryNamesQuery { IdLIst = _query.Country });
        paragraph = new Paragraph().SetFixedLeading(10);
        countries.ForEach(x =>
        {
            paragraph.Add(new Paragraph(x.Label));
            paragraph.Add(new Text("\n"));
        });
        table.AddCell(paragraph);
        var sites = await _mediator.Send(new SiteNamesQuery { IdLIst = _query.Site });
        paragraph = new Paragraph().SetFixedLeading(10);
        sites.ForEach(x =>
        {
            paragraph.Add(new Paragraph(x.Label));
            paragraph.Add(new Text("\n"));
        });
        table.AddCell(paragraph);
        var platforms = await _mediator.Send(new TurbinePlatformNamesQuery { IdLIst = _query.TurbineId });
        paragraph = new Paragraph().SetFixedLeading(10);
        platforms.ForEach(x =>
        {
            paragraph.Add(new Paragraph(x.Label));
            paragraph.Add(new Text("\n"));
        });
        table.AddCell(paragraph);
        var turbines = await _mediator.Send(new TurbineNamesQuery { IdLIst = _query.TurbineId });
        paragraph = new Paragraph().SetFixedLeading(10);
        turbines.ForEach(x =>
        {
            paragraph.Add(new Paragraph(x.Label));
            paragraph.Add(new Text("\n"));
        });
        table.AddCell(paragraph);
        var categories = await _mediator.Send(new CategoryNamesQuery { IdLIst = _query.Category });
        paragraph = new Paragraph().SetFixedLeading(10);
        categories.ForEach(x =>
        {
            paragraph.Add(new Paragraph(x.Label));
            paragraph.Add(new Text("\n"));
        });
        table.AddCell(paragraph);
        table.RemoveBorder();
        _document.Add(table);
    }
}