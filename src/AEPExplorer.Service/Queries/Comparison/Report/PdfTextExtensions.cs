using iText.Kernel.Colors;
using iText.Layout;
using iText.Layout.Borders;
using iText.Layout.Element;
using static iText.Layout.Borders.Border;

namespace AEPExplorer.Service.Queries.Comparison.Report;

public static class PdfTextExtensions
{
    public static Document AddSectionTitle(this Document document, string title)
    {
        var table = new iText.Layout.Element.Table(1)
            .UseAllAvailableWidth()
            .SetMarginBottom(20)
            .SetMarginTop(20);
        var text = new Text(title)
            .SetBold()
            .SetFontColor(new DeviceRgb(91, 70, 115))
            .SetFontSize(18);
        var paragraph = new Paragraph();
        paragraph.Add(text);
        paragraph.SetFixedLeading(1);
        paragraph.SetMultipliedLeading(1);
        var cell = new Cell();
        cell.Add(paragraph);
        cell.SetPadding(1);
        cell.SetPaddingLeft(4);
        cell.SetBorder(NO_BORDER);
        cell.SetBorderLeft(new SolidBorder(new DeviceRgb(0, 153, 153), 4));
        table.AddCell(cell);
        document.Add(table);
        return document;
    }

    public static iText.Layout.Element.Table FormatHeaderRow(this iText.Layout.Element.Table table)
    {
        var headerTable = table.GetHeader();
        foreach (IElement element in headerTable.GetChildren())
        {
            var cell = (Cell)element;
            cell.SetBorder(NO_BORDER);
            cell.SetBackgroundColor(new DeviceRgb(66, 66, 66));
            cell.SetFontColor(ColorConstants.WHITE);
        }

        return table;
    }

    public static iText.Layout.Element.Table RemoveBorder(this iText.Layout.Element.Table table)
    {
        foreach (IElement element in table.GetChildren())
        {
            ((Cell)element).SetBorder(NO_BORDER);
        }

        return table;
    }
}