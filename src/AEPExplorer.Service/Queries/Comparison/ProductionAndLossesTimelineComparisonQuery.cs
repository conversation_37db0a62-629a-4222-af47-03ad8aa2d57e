using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Comparison;

public class ProductionAndLossesTimelineComparisonQuery : MediatR.IRequest<ProductionAndLossesTimelineComparison>
{
    public Query Query { get; init; }
}

public class ProductionAndLossesTimelineComparisonQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    : IRequestHandler<ProductionAndLossesTimelineComparisonQuery, ProductionAndLossesTimelineComparison>
{
    public async Task<ProductionAndLossesTimelineComparison> Handle(ProductionAndLossesTimelineComparisonQuery request, CancellationToken cancellationToken)
    {
        var result = new ProductionAndLossesTimelineComparison();

        var chartData = new List<ComparisonLineChartData>();
        var comparisonQuery = new Query
        {
            Region = request.Query.Region,
            Country = request.Query.Country,
            Site = request.Query.Site,
            TurbineId = request.Query.TurbineId,
            Platform = request.Query.Platform,
            Model = request.Query.Model,
            Category = request.Query.Category,
            DateFrom = request.Query.DateFrom,
            DateTo = request.Query.DateTo
        };

        var (filterBy, comparisonItems) = FilterHelper.GetFilterBy(request.Query);
        if (filterBy == null && !comparisonItems.Any())
        {
            return result;
        }

        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var lines = filterBy switch
        {
            FilterByEnum.Customer => await dbContext.Customers.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Region => await dbContext.Regions.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Country => await dbContext.Countries.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Site => await dbContext.Sites.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            FilterByEnum.Turbine => await dbContext.Turbines.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            _ => throw new ArgumentOutOfRangeException()
        };


        var downPeriod = new List<DownPeriodModel>();
        for (var i = 0; i < comparisonItems.Count; i++)
        {
            var comparisonItem = comparisonItems[i];

            comparisonQuery = FilterHelper.GetSingleFilterQuery(comparisonQuery, filterBy.Value, comparisonItem);

            var turbineCount = 1;
            if (filterBy != FilterByEnum.Turbine)
            {
                var turbines = await ElasticsearchGeneralService.GetDistinctValuesAsync(elasticClient, x => x.TurbineId, comparisonQuery);
                turbineCount = turbines.Count;
            }

            var items = await ElasticsearchSumService.SumByDateHistogramAsync(elasticClient, comparisonQuery, aggregations, DateInterval.Day,
                lossesType: request.Query.FilteredCategories ? LossesTypeEnum.Detailed : LossesTypeEnum.TotalLosses);

            sumPropertyNames = new List<string>
            {
                nameof(AepReadData.ActualEnergy),
                nameof(AepReadData.EnergyPotential),
                nameof(AepReadData.DataCoverage),
            };

            aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
                propertyName => propertyName,
                propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });
            comparisonQuery.Category = new List<Guid>();
            var totalItems = await ElasticsearchSumService.SumByDateHistogramAsync(elasticClient, comparisonQuery, aggregations, DateInterval.Day,
                lossesType: LossesTypeEnum.TotalLosses);
            var actual = items.Select(x => new ValueDate
            {
                Value = x.Value[nameof(AepReadData.ActualEnergy)],
                Date = x.Key
            }).ToList();
            downPeriod.Add(new DownPeriodModel
            {
                Name = lines.FirstOrDefault(x => x.Id == comparisonItem)?.Name ?? "Unknown",
                Data = ComparisonHelper.GetDownPeriod(actual)
            });
            var data = new List<ComparisonLineChartItem>();
            for (var dt = request.Query.DateFrom.Value; dt <= request.Query.DateTo.Value; dt = dt.AddDays(1))
            {
                items.TryGetValue(dt, out var item);
                totalItems.TryGetValue(dt, out var totalItem);
                double actualEnergy = 0.0, energyPotential = 0.0, dataCoverage = 0.0, totalActualEnergy = 0.0, totalEnergyPotential = 0.0;
                item?.TryGetValue(nameof(AepReadData.ActualEnergy), out actualEnergy);
                item?.TryGetValue(nameof(AepReadData.EnergyPotential), out energyPotential);
                totalItem?.TryGetValue(nameof(AepReadData.DataCoverage), out dataCoverage);
                totalItem?.TryGetValue(nameof(AepReadData.EnergyPotential), out totalEnergyPotential);
                totalItem?.TryGetValue(nameof(AepReadData.ActualEnergy), out totalActualEnergy);
                data.Add(new ComparisonLineChartItem
                {
                    Date = dt,
                    Actual = totalActualEnergy,
                    ActualPercentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential),
                    Losses = energyPotential - actualEnergy,
                    LossesPercentage = ConvertExtensions.CalculatePercentage(energyPotential - actualEnergy, totalEnergyPotential),
                    EnergyPotential = totalEnergyPotential,
                    EnergyPotentialPercentage = totalEnergyPotential <= 0 ? 0 : 100.0,
                    DataCoverage = (dataCoverage / turbineCount).ToPercentage()
                });
            }

            chartData.Add(new ComparisonLineChartData
            {
                ItemName = lines.FirstOrDefault(x => x.Id == comparisonItem)?.Name ?? "Unknown",
                Data = data
            });
        }

        result.ChartData = chartData;
        result.DownPeriod = downPeriod;
        return result;
    }
}