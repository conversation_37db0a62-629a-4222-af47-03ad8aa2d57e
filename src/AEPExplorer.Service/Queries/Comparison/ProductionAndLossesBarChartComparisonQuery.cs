using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Comparison;

public class ProductionAndLossesBarChartComparisonQuery : MediatR.IRequest<List<BarChartComparison>>
{
    public Query Query { get; init; }
}

public class ProductionAndLossesBarChartComparisonQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    : IRequestHandler<ProductionAndLossesBarChartComparisonQuery, List<BarChartComparison>>
{
    public async Task<List<BarChartComparison>> Handle(ProductionAndLossesBarChartComparisonQuery request, CancellationToken cancellationToken)
    {
        var result = new List<BarChartComparison>();

        var (filterBy, groupProperty) = FilterHelper.GetFilterGroupProperty(request.Query);
        var (_, comparisonItems) = FilterHelper.GetFilterBy(request.Query);

        if (filterBy == null)
        {
            return result;
        }

        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, groupProperty, aggregations,
            ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION,
            lossesType: request.Query.FilteredCategories ? LossesTypeEnum.Detailed : LossesTypeEnum.TotalLosses);

        request.Query.Category = new List<Guid>();
        var totalItems = await ElasticsearchSumService.SumByGroupAsync(elasticClient, request.Query, groupProperty, aggregations, ElasticsearchConstants.DECIMAL_PLACES_FOR_AGGREGATION, lossesType: LossesTypeEnum.TotalLosses);

        var bars = groupProperty switch
        {
            nameof(AepReadData.CustomerId) => await dbContext.Customers.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.RegionId) => await dbContext.Regions.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.CountryId) => await dbContext.Countries.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.SiteId) => await dbContext.Sites.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.TurbineId) => await dbContext.Turbines.Where(x => comparisonItems.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
            }).ToListAsync(cancellationToken: cancellationToken),
            _ => throw new ArgumentOutOfRangeException()
        };
        
        foreach (var comparisonItem in comparisonItems)
        {
            var (key, itemValue) = items.FirstOrDefault(x => x.Key == comparisonItem);
            if (itemValue == null || itemValue.Count == 0)
            {
                result.Add(new BarChartComparison
                {
                    Id = comparisonItem,
                    Name = bars.FirstOrDefault(x => x.Id == comparisonItem)?.Name ?? "Unknown",
                    ActualValue = 0,
                    ActualPercentage = 0,
                    LossesValue = 0,
                    LossesPercentage = 0,
                    EnergyPotentialPercentage = 0,
                    EnergyPotentialValue = 0
                });
                continue;
            }

            var actualEnergy = itemValue.GetValueOrDefault(nameof(AepReadData.ActualEnergy), 0);
            var energyPotential = itemValue.GetValueOrDefault(nameof(AepReadData.EnergyPotential), 0);
            var losses = energyPotential - actualEnergy;
            
            var totalItem = totalItems.GetValueOrDefault(key);
            var totalEnergyPotential = totalItem?.GetValueOrDefault(nameof(AepReadData.EnergyPotential), 0) ?? 0;
            var totalActualEnergy = totalItem?.GetValueOrDefault(nameof(AepReadData.ActualEnergy), 0) ?? 0;
            
            var bar = bars.FirstOrDefault(x => x.Id == key);
            
            result.Add(new BarChartComparison
            {
                Id = key,
                Name = bar?.Name ?? "Unknown",
                ActualValue = totalActualEnergy,
                ActualPercentage = ConvertExtensions.CalculatePercentage(totalActualEnergy, totalEnergyPotential),
                LossesValue = losses,
                LossesPercentage = ConvertExtensions.CalculatePercentage(losses, totalEnergyPotential),
                EnergyPotentialPercentage = totalEnergyPotential > 0 ? 100.00 : 0,
                EnergyPotentialValue = totalEnergyPotential
            });
        }

        return result;
    }
}