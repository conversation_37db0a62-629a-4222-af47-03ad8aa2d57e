using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Ice.TurbineLosses;

public class IceDataAnalyticsLossesHoursQuery : MediatR.IRequest<List<NameValue>>
{
    public Query Query { get; set; }
}

public class IceDataAnalyticsLossesHoursHandler : IRequestHandler<IceDataAnalyticsLossesHoursQuery, List<NameValue>>
{
    private readonly IElasticClient _elasticClient;

    private readonly AepExplorerDbContext _dbContext;

    public IceDataAnalyticsLossesHoursHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<NameValue>> Handle(IceDataAnalyticsLossesHoursQuery request, CancellationToken cancellationToken)
    {
        var queryIce = request.Query;
        queryIce.Category = CategoryHelper.ICE_TURBINE_CATEGORIES.ToList();
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.DurationInHours),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });
        var items = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, queryIce, nameof(AepReadData.SubcategoryId), aggregations, 4);
        var categories = new List<NameValue>();
        var categoryStructure = await _dbContext.Subcategories
            .Where(x => CategoryHelper.ICE_TURBINE_CATEGORIES.Contains(x.Id))
            .Select(x => new
            {
                x.Id,
                x.Name,
            })
            .ToListAsync(cancellationToken);
        
        foreach (var item in items)
        {
            categories.Add(new NameValue
            {
                Id = categoryStructure.First(x => x.Id == item.Key).Id,
                Name = categoryStructure.FirstOrDefault(x=>x.Id == item.Key)?.Name ?? "Unknown",
                Value = item.Value[nameof(AepReadData.DurationInHours)].Round(2)       
            });
        }

        return categories;
    }
}