using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Ice.TurbineLosses;

public class IceDataAnalyticsLossesTimelineQuery : MediatR.IRequest<List<IceLossesWithDate>>
{
    public Query Query { get; init; }
}

public class IceDataAnalyticsLossesTimelineHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<IceDataAnalyticsLossesTimelineQuery, List<IceLossesWithDate>>
{
    public async Task<List<IceLossesWithDate>> Handle(IceDataAnalyticsLossesTimelineQuery request, CancellationToken cancellationToken)
    {
        var queryIce = request.Query;

        var aggregations = new Dictionary<string, IAggregationContainer>();
        var script = $"doc['{nameof(AepReadData.EnergyPotential).ToLowerFirstChar()}'].value - doc['{nameof(AepReadData.ActualEnergy).ToLowerFirstChar()}'].value";
        aggregations.Add("turbineIceLosses", new AggregationContainer
        {
            ScriptedMetric = new ScriptedMetricAggregation("turbineIceLosses")
            {
                InitScript = new InlineScript("state.commits = []"),
                MapScript = new InlineScript($"state.commits.add({script})"),
                CombineScript = new InlineScript("def sum = 0.0; for (c in state.commits) { sum += c } return sum"),
                ReduceScript = new InlineScript("def sum = 0.0; for (a in states) { sum += a } return sum")
            }
        });
        var categoryStructure = await dbContext.Subcategories
            .Where(x => CategoryHelper.ICE_TURBINE_CATEGORIES.Contains(x.Id))
            .Select(x => new
            {
                x.Id,
                x.Name,
            })
            .ToListAsync(cancellationToken);

        queryIce.Category = CategoryHelper.ICE_TURBINE_CATEGORIES.ToList();
        var itemsGrouped = await ElasticsearchSumService.SumByGroupDateHistogramAsync(elasticClient, queryIce, nameof(AepReadData.SubcategoryId), aggregations, DateInterval.Day);
        var result = new List<IceLossesWithDate>();
        for (var date = request.Query.DateFrom.Value; date <= request.Query.DateTo.Value; date = date.AddDays(1))
        {
            var categories = categoryStructure.Select(x=> new NameValue()
            {
                Id = x.Id,
                Name = x.Name,
                Value = 0.0
            }).ToList();
            
            if (itemsGrouped.ContainsKey(date))
            {
                var item = itemsGrouped.FirstOrDefault(x => x.Key == date);
                foreach (var category in categories)
                {
                    item.Value.TryGetValue(category.Id, out var itemCategory);
                    if (itemCategory != null)
                    {
                        category.Value = itemCategory.FirstOrDefault(v=>v.Key == "turbineIceLosses").Value;
                    }
                }
            }
            
            result.Add(new IceLossesWithDate
            {
                Categories = categories,
                Date = date
            });
        }

        return result;
    }
}