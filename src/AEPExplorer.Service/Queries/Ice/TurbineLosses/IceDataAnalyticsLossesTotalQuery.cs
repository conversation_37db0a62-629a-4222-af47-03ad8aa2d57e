using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Ice.TurbineLosses;

public class IceDataAnalyticsLossesTotalQuery : MediatR.IRequest<List<NameValuePercentageWithChildren>>
{
    public Query Query { get; init; }
}

public class IceDataAnalyticsLossesTotalHandler : IRequestHandler<IceDataAnalyticsLossesTotalQuery, List<NameValuePercentageWithChildren>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public IceDataAnalyticsLossesTotalHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<NameValuePercentageWithChildren>> Handle(IceDataAnalyticsLossesTotalQuery request, CancellationToken cancellationToken)
    {
        var queryIce = request.Query;
        queryIce.Category = CategoryHelper.ICE_TURBINE_CATEGORIES.ToList();
        var aggregations = new Dictionary<string, IAggregationContainer>();
        var script = $"doc['{nameof(AepReadData.EnergyPotential).ToLowerFirstChar()}'].value - doc['{nameof(AepReadData.ActualEnergy).ToLowerFirstChar()}'].value";
        aggregations.Add("turbineIceLosses", new AggregationContainer
        {
            ScriptedMetric = new ScriptedMetricAggregation("turbineIceLosses")
            {
                InitScript = new InlineScript("state.commits = []"),
                MapScript = new InlineScript($"state.commits.add({script})"),
                CombineScript = new InlineScript("def sum = 0.0; for (c in state.commits) { sum += c } return sum"),
                ReduceScript = new InlineScript("def sum = 0.0; for (a in states) { sum += a } return sum")
            }
        });
        var items = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, queryIce, nameof(AepReadData.SubcategoryId), aggregations, 4);
        var categories = new List<NameValuePercentageWithChildren>();
        var categoryStructure = await _dbContext.Subcategories
            .Where(x => CategoryHelper.ICE_TURBINE_CATEGORIES.Contains(x.Id))
            .Select(x => new
            {
                x.Id,
                x.Name,
            })
            .ToListAsync(cancellationToken);
        var total = items.Select(x => x.Value["turbineIceLosses"]).Sum();
        foreach (var item in items)
        {
            categories.Add(new NameValuePercentageWithChildren
            {
                Id = categoryStructure.FirstOrDefault(x => x.Id == item.Key).Id,
                Name = categoryStructure.FirstOrDefault(x=>x.Id == item.Key)?.Name ?? "Unknown",
                Value = item.Value["turbineIceLosses"],
                Percentage = ConvertExtensions.CalculatePercentage(item.Value["turbineIceLosses"], total)
            });
        }

        return categories;
    }
}