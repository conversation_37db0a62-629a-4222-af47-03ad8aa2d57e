using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Ice.EstimationLosses;

public class IceDataAnalyticsEstimationLossesTimelineQuery : MediatR.IRequest<List<IceLossesWithDate>>
{
    public Query Query { get; set; }
}

public class IceDataAnalyticsEstimationLossesTimelineHandler : IRequestHandler<IceDataAnalyticsEstimationLossesTimelineQuery, List<IceLossesWithDate>>
{
    private readonly IElasticClient _elasticClient;
    private readonly AepExplorerDbContext _dbContext;

    public IceDataAnalyticsEstimationLossesTimelineHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext)
    {
        _elasticClient = elasticClient;
        _dbContext = dbContext;
    }

    public async Task<List<IceLossesWithDate>> Handle(IceDataAnalyticsEstimationLossesTimelineQuery request, CancellationToken cancellationToken)
    {
        var aggregations = new Dictionary<string, IAggregationContainer>();
        var script = $"doc['{nameof(AepReadData.EnergyPotential).ToLowerFirstChar()}'].value - doc['{nameof(AepReadData.ActualEnergy).ToLowerFirstChar()}'].value";
        aggregations.Add("turbineIceLosses", new AggregationContainer
        {
            ScriptedMetric = new ScriptedMetricAggregation("turbineIceLosses")
            {
                InitScript = new InlineScript("state.commits = []"),
                MapScript = new InlineScript($"state.commits.add({script})"),
                CombineScript = new InlineScript("def sum = 0.0; for (c in state.commits) { sum += c } return sum"),
                ReduceScript = new InlineScript("def sum = 0.0; for (a in states) { sum += a } return sum")
            }
        });
        var categoryStructure = CategoryHelper.ICE_ESTIMATE_CATEGORIES
            .Select(x => new
            {
                Id = x.Key,
                Name = x.Value,
            }).ToList();

        var queryIce = request.Query;
        queryIce.Category = CategoryHelper.ICE_ESTIMATE_CATEGORIES.Select(x => x.Key).ToList();
        var itemsGrouped = await ElasticsearchSumService.SumByGroupDateHistogramAsync(_elasticClient, queryIce, nameof(AepReadData.SubcategoryId), aggregations, DateInterval.Day, lossesType: LossesTypeEnum.IceEstimateTopic);
        var result = new List<IceLossesWithDate>();
        for (var date = request.Query.DateFrom.Value; date <= request.Query.DateTo.Value; date = date.AddDays(1))
        {
            var categories = categoryStructure.Select(x=> new NameValue()
            {
                Id = x.Id,
                Name = x.Name,
                Value = 0.0
            }).ToList();

            if (itemsGrouped.ContainsKey(date))
            {
                var item = itemsGrouped.FirstOrDefault(x => x.Key == date);
                foreach (var category in categories)
                {
                    item.Value.TryGetValue(category.Id, out var itemCategory);
                    if (itemCategory != null)
                    {
                        category.Value = itemCategory.FirstOrDefault(v=>v.Key == "turbineIceLosses").Value;
                    }
                }
            }

            result.Add(new IceLossesWithDate
            {
                Categories = categories,
                Date = date
            });
        }

        return result;
    }
}