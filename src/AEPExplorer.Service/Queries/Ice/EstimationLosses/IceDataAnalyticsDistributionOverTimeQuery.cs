using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Ice.EstimationLosses;

public class IceDataAnalyticsDistributionOverTimeQuery : MediatR.IRequest<IceDataAnalyticsDistributionOverTime>
{
    public Query Query { get; set; }
}

public class IceDataAnalyticsPeriodQueryHandler(IElasticClient elasticClient) : IRequestHandler<IceDataAnalyticsDistributionOverTimeQuery, IceDataAnalyticsDistributionOverTime>
{
    public async Task<IceDataAnalyticsDistributionOverTime> Handle(IceDataAnalyticsDistributionOverTimeQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.EnergyPotential),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });
        var categoryStructure = CategoryHelper.ICE_ESTIMATE_CATEGORIES
            .Select(x => new
            {
                Id = x.Key,
                Name = x.Value,
            }).ToList();

        var queryIce = request.Query;
        var categoryDistribution = new List<NameFromTo>();
        foreach (var category in categoryStructure)
        {
            queryIce.Category = [category.Id];
            var categoryHistogram = await ElasticsearchSumService.SumByDateHistogramAsync(elasticClient, queryIce, aggregations, DateInterval.Day, lossesType: LossesTypeEnum.IceEstimateTopic);

            categoryDistribution.Add(new NameFromTo
            {
                Name = category.Name,
                Id = category.Id,
                Periods = ComparisonHelper.GetUpPeriod(categoryHistogram.Select(x => new NameValueDate
                {
                    Date = x.Key,
                    Value = x.Value[nameof(AepReadData.EnergyPotential)]
                }).ToList())
            }
            );
        }

        var result = new IceDataAnalyticsDistributionOverTime
        {
            Data = [.. categoryDistribution.OrderBy(x => x.Name)],
            From = request.Query.DateFrom,
            To = request.Query.DateTo
        };

        return result;
    }
}