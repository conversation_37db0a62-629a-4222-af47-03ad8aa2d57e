using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Ice.EstimationLosses;

public class IceDataAnalyticsDistributionQuery : MediatR.IRequest<List<NameValue>>
{
    public Query Query { get; set; }
}

public class IceDataLossesDistributionHandler : IRequestHandler<IceDataAnalyticsDistributionQuery, List<NameValue>>
{
    private readonly IElasticClient _elasticClient;

    public IceDataLossesDistributionHandler(IElasticClient elasticClient)
    {
        _elasticClient = elasticClient;
    }

    public async Task<List<NameValue>> Handle(IceDataAnalyticsDistributionQuery request, CancellationToken cancellationToken)
    {
        var queryIce = request.Query;
        queryIce.Category = CategoryHelper.ICE_ESTIMATE_CATEGORIES.Select(x => x.Key).ToList();
        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.DurationInHours),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });
        var items = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, queryIce, nameof(AepReadData.SubcategoryId), aggregations, 4, lossesType: LossesTypeEnum.IceEstimateTopic);
        var categories = new List<NameValue>();
        var categoryStructure = CategoryHelper.ICE_ESTIMATE_CATEGORIES
            .Select(x => new
            {
                Id = x.Key,
                Name = x.Value,
            }).ToList();
        foreach (var item in items)
        {
            categories.Add(new NameValue
            {
                Id = item.Key,
                Name = categoryStructure.FirstOrDefault(x => x.Id == item.Key)?.Name ?? "Unknown",
                Value = item.Value[nameof(AepReadData.DurationInHours)].Round(2),
            });
        }

        return [.. categories.OrderBy(x => x.Name)];
    }
}