using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Ice.EstimationLosses;

public class IceDataAnalyticsEstimationLossesTotalQuery : MediatR.IRequest<List<NameValuePercentageWithChildren>>
{
    public Query Query { get; init; }
}

public class IceDataAnalyticsEstimationLossesTotalHandler : IRequestHandler<IceDataAnalyticsEstimationLossesTotalQuery, List<NameValuePercentageWithChildren>>
{
    private readonly IElasticClient _elasticClient;

    public IceDataAnalyticsEstimationLossesTotalHandler(IElasticClient elasticClient)
    {
        _elasticClient = elasticClient;
    }

    public async Task<List<NameValuePercentageWithChildren>> Handle(IceDataAnalyticsEstimationLossesTotalQuery request, CancellationToken cancellationToken)
    {
        var queryIce = request.Query;
        queryIce.Category = CategoryHelper.ICE_ESTIMATE_CATEGORIES_ONLY_LOSSES.Select(x => x.Key).ToList();
        var aggregations = new Dictionary<string, IAggregationContainer>();
        var script = $"doc['{nameof(AepReadData.EnergyPotential).ToLowerFirstChar()}'].value - doc['{nameof(AepReadData.ActualEnergy).ToLowerFirstChar()}'].value";
        aggregations.Add("turbineIceLosses", new AggregationContainer
        {
            ScriptedMetric = new ScriptedMetricAggregation("turbineIceLosses")
            {
                InitScript = new InlineScript("state.commits = []"),
                MapScript = new InlineScript($"state.commits.add({script})"),
                CombineScript = new InlineScript("def sum = 0.0; for (c in state.commits) { sum += c } return sum"),
                ReduceScript = new InlineScript("def sum = 0.0; for (a in states) { sum += a } return sum")
            }
        });
        var items = await ElasticsearchSumService.SumByGroupAsync(_elasticClient, queryIce, nameof(AepReadData.SubcategoryId), aggregations, 4, lossesType: LossesTypeEnum.IceEstimateTopic);
        var categories = new List<NameValuePercentageWithChildren>();
        var categoryStructure = CategoryHelper.ICE_ESTIMATE_CATEGORIES_ONLY_LOSSES
            .Select(x => new
            {
                Id = x.Key,
                Name = x.Value,
            }).ToList();
        var total = items.Select(x => x.Value["turbineIceLosses"]).Sum();
        foreach (var item in items)
        {
            categories.Add(new NameValuePercentageWithChildren
            {
                Id = item.Key,
                Name = categoryStructure.FirstOrDefault(x => x.Id == item.Key)?.Name ?? "Unknown",
                Value = item.Value["turbineIceLosses"],
                Percentage = ConvertExtensions.CalculatePercentage(item.Value["turbineIceLosses"], total)
            });
        }

        return categories;
    }
}