using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Filter;

public class CountryFilterQuery : MediatR.IRequest<List<ValueLabel>>
{
    public FilterQuery Query { get; init; }
    public bool PowerBoost { get; init; }
}

public class CountryFilterQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<CountryFilterQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(CountryFilterQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.Countries.AsQueryable();

        if (request.PowerBoost)
        {
            query = query.Where(x => x.PowerBoost);
        }

        if (request.Query.Customer?.Count > 0)
        {
            query = query.Where(x => x.Sites.Any(s => s.Turbines.Any(t => 
                t.CustomerId.HasValue && request.Query.Customer.Contains((Guid)t.CustomerId))));
        }

        if (request.Query.Region?.Count > 0)
        {
            query = query.Where(x => request.Query.Region.Contains(x.RegionId));
        }

        if (request.Query.Site?.Count > 0)
        {
            query = query.Where(x => x.Sites.Any(s => request.Query.Site.Contains(s.Id)));
        }

        if (request.Query.TurbineId?.Count > 0)
        {
            query = query.Where(x => x.Sites.Any(s => s.Turbines.Any(t => 
                request.Query.TurbineId.Contains(t.Id))));
        }
        
        if (request.Query.LocationTypeName?.Count > 0)
        {
            query = query.Where(x => x.Sites.Any(s => s.Turbines.Any(t => request.Query.LocationTypeName.Contains(t.LocationTypeName))));
        }

        if (request.Query.Model?.Count > 0)
        {
            query = query.Where(x => x.Sites.Any(s => s.Turbines.Any(t => 
                request.Query.Model.Contains(t.ModelId))));
        }

        if (request.Query.Platform?.Count > 0)
        {
            query = query.Where(x => x.Sites.Any(s => s.Turbines.Any(t => 
                request.Query.Platform.Contains(t.TurbineModel.TurbinePlatformId))));
        }

        var result = await query
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);

        return result;
    }
}