using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Filter;

public class TurbineFilterQuery : MediatR.IRequest<List<ValueLabel>>
{
    public FilterQuery Query { get; init; }
    public bool PowerBoost { get; init; }
}

public class TurbineFilterQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<TurbineFilterQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(TurbineFilterQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.Turbines.AsQueryable();

        query = request.PowerBoost ? query.Where(x => x.PowerBoost) : query.Where(x => x.IsPresent);

        if (request.Query.Customer?.Count > 0)
        {
            query = query.Where(x => x.CustomerId.HasValue &&
                request.Query.Customer.Contains((Guid)x.CustomerId));
        }

        if (request.Query.Region?.Count > 0)
        {
            query = query.Where(x => request.Query.Region.Contains(x.Site.Country.RegionId));
        }

        if (request.Query.Country?.Count > 0)
        {
            query = query.Where(x => request.Query.Country.Contains(x.Site.CountryId));
        }

        if (request.Query.Site?.Count > 0)
        {
            query = query.Where(x => request.Query.Site.Contains(x.SiteId));
        }

        if (request.Query.Platform?.Count > 0)
        {
            query = query.Where(x => request.Query.Platform.Contains(x.TurbineModel.TurbinePlatformId));
        }

        if (request.Query.Model?.Count > 0)
        {
            query = query.Where(x => request.Query.Model.Contains(x.ModelId));
        }

        if (request.Query.LocationTypeName?.Count > 0)
        {
            query = query.Where(x=> request.Query.LocationTypeName.Contains(x.LocationTypeName));
        }

        var result = await query
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);

        return result;
    }
}