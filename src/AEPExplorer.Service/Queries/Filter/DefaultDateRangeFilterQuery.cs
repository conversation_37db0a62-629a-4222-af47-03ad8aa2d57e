using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Filter;

public class DefaultDateRangeFilterQuery : MediatR.IRequest<DateRangeModel>
{
    public bool PowerBoost { get; set; }
}

public class DefaultDateRangeFilterQueryHandler(IElasticClient elasticClient) : IRequestHandler<DefaultDateRangeFilterQuery, DateRangeModel>
{
    public async Task<DateRangeModel> Handle(DefaultDateRangeFilterQuery request, CancellationToken cancellationToken)
    {
        return request.PowerBoost ? await ElasticsearchGeneralService.GetPowerBoostDateRange(elasticClient) : await ElasticsearchGeneralService.GetDateRange(elasticClient);
    }
}