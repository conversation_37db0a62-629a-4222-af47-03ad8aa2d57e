using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Filter;

public class SelectedCategoryQuery : MediatR.IRequest<List<ValueLabel>>
{
    public List<Guid> Categories { get; init; }
}

public class SelectedCategoryQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<SelectedCategoryQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(SelectedCategoryQuery request, CancellationToken cancellationToken)
    {
        var queryCategories = FilterHelper.MapToCategories(request.Categories, dbContext);

        var dbCategories = await dbContext.Categories
            .Where(c => c.Id != CategoryHelper.RUN_CATEGORY_ID).Select(x => new { x.Id, x.Name, x.Subcategories }).ToListAsync(cancellationToken);

        return dbCategories
             .Where(x => queryCategories.Count == 0 || queryCategories.Contains(x.Id) || (x.Subcategories != null && x.Subcategories.Any(y => queryCategories.Contains(y.Id))))
             .Select(z => new ValueLabel { Value = z.Id, Label = z.Name })
             .ToList();

    }
}