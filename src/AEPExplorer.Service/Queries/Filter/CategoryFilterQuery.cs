using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Filter;

public class CategoryFilterQuery : MediatR.IRequest<List<ValueLabelWithChildren>>
{
}

public class CategoryFilterQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<CategoryFilterQuery, List<ValueLabelWithChildren>>
{
    public async Task<List<ValueLabelWithChildren>> Handle(CategoryFilterQuery request, CancellationToken cancellationToken)
    {
        var operative = await dbContext.Categories
            .Where(x => (x.Level4.Level3Id == CategoryHelper.LEVEL3_IN_SERVICE_ID 
                        || x.Level4.Level3Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
            && x.Id != CategoryHelper.RUN_CATEGORY_ID)
            .Select(x => new ValueLabelWithChildren
            {
                Value = x.Id,
                Label = x.Name,
                Children = x.Subcategories.Select(s=> new ValueLabelWithChildren
                {
                    Value = s.Id,
                    Label = s.Name
                }).ToList(),
            })
            .ToListAsync(cancellationToken);
        
        var nonoperative = await dbContext.Categories.Where(x => x.Level4.Level3Id != CategoryHelper.LEVEL3_IN_SERVICE_ID && x.Level4.Level3Id != CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
            .Select(x => new ValueLabelWithChildren
            {
                Value = x.Id,
                Label = x.Name,
                Children = x.Subcategories.Select(s=> new ValueLabelWithChildren
                {
                    Value = s.Id,
                    Label = s.Name
                }).ToList(),
            })
            .ToListAsync(cancellationToken);
        var result = new List<ValueLabelWithChildren>
        {
            new()
            {
                Label = "Operative",
                Value = CategoryHelper.OPERATIVE_CATEGORY,
                Children = operative
            },
            new()
            {
                Label = "Non operative",
                Value = CategoryHelper.NON_OPERATIVE_CATEGORY,
                Children = nonoperative
            }
        };
        
        return result;
    }
}