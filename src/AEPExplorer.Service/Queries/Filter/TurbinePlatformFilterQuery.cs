using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Filter;

public class TurbinePlatformFilterQuery : MediatR.IRequest<List<ValueLabelWithChildren>>
{
    public FilterQuery Query { get; init; }
    public bool PowerBoost { get; init; }
}

public class TurbinePlatformFilterQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<TurbinePlatformFilterQuery, List<ValueLabelWithChildren>>
{
    public async Task<List<ValueLabelWithChildren>> Handle(TurbinePlatformFilterQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.TurbinePlatforms.Include(p => p.Oem).AsQueryable();
        if (request.PowerBoost)
        {
            query = query.Where(x => x.PowerBoost);
        }
        
        if (request.Query.Customer?.Count > 0)
        {
            query = query.Where(x => x.TurbineModels.Any(tm => tm.Turbines.Any(t => 
                t.CustomerId.HasValue && request.Query.Customer.Contains((Guid)t.CustomerId))));
        }
        
        if (request.Query.Region?.Count > 0)
        {
            query = query.Where(x => x.TurbineModels.Any(tm => tm.Turbines.Any(t => 
                request.Query.Region.Contains(t.Site.Country.RegionId))));
        }

        if (request.Query.Country?.Count > 0)
        {
            query = query.Where(x => x.TurbineModels.Any(tm => tm.Turbines.Any(t => 
                request.Query.Country.Contains(t.Site.CountryId))));
        }

        if (request.Query.Site?.Count > 0)
        {
            query = query.Where(x => x.TurbineModels.Any(tm => tm.Turbines.Any(t => 
                request.Query.Site.Contains(t.SiteId))));
        }

        if (request.Query.TurbineId?.Count > 0)
        {
            query = query.Where(x => x.TurbineModels.Any(tm => tm.Turbines.Any(t => 
                request.Query.TurbineId.Contains(t.Id))));
        }
        
        if (request.Query.LocationTypeName?.Count > 0)
        {
            query = query.Where(x => x.TurbineModels.Any(tm => tm.Turbines.Any(t => 
                request.Query.LocationTypeName.Contains(t.LocationTypeName))));
        }

        if (request.Query.Model?.Count > 0)
        {
            query = query.Where(x => x.TurbineModels.Any(tm=> tm.Turbines.Any(t => 
                request.Query.Model.Contains(t.ModelId))));
        }
        
        var result = await query
            .Select(x => x.Oem).Distinct().OrderBy(x => x.Name)
            .Select(oem => new ValueLabelWithChildren
            {
                Value = oem.Id,
                Label = oem.Name,
                Children = oem.TurbinePlatforms
                    .Where(p => query.Contains(p))
                    .Select(p => new ValueLabelWithChildren
                    {
                        Value = p.Id,
                        Label = p.Name
                    })
                    .OrderBy(x => x.Label)
                    .ToList()
            }).ToListAsync(cancellationToken);

        return result; 
    }
}