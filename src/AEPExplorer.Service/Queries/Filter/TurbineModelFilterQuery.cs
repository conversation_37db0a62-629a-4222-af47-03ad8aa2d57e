using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Filter;

public class TurbineModelFilterQuery : MediatR.IRequest<List<ValueLabelWithChildren>>
{
    public FilterQuery Query { get; init; }
    public bool PowerBoost { get; init; }
}

public class TurbineModelFilterQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<TurbineModelFilterQuery, List<ValueLabelWithChildren>>
{
    public async Task<List<ValueLabelWithChildren>> Handle(TurbineModelFilterQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.Models.AsQueryable();

        if (request.PowerBoost)
        {
            query = query.Where(x => x.PowerBoost);
        }

        if (request.Query.Customer?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t =>
                t.CustomerId.HasValue && request.Query.Customer.Contains((Guid)t.CustomerId)));
        }

        if (request.Query.Region?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t =>
                request.Query.Region.Contains(t.Site.Country.RegionId)));
        }

        if (request.Query.Country?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t =>
                request.Query.Country.Contains(t.Site.CountryId)));
        }

        if (request.Query.Site?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t =>
                request.Query.Site.Contains(t.SiteId)));
        }

        if (request.Query.TurbineId?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t =>
                request.Query.TurbineId.Contains(t.Id)));
        }

        if (request.Query.LocationTypeName?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => request.Query.LocationTypeName.Contains(t.LocationTypeName)));
        }

        if (request.Query.Platform?.Count > 0)
        {
            query = query.Where(x => request.Query.Platform.Contains(x.TurbinePlatformId));
        }

        var result = await query
            .Select(x => new ValueLabelWithChildren
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);

        return result;
    }
}