using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Filter;

public class CustomerFilterQuery : MediatR.IRequest<List<ValueLabel>>
{
    public FilterQuery Query { get; init; }
    public bool PowerBoost { get; init; }
}

public class CustomerFilterQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<CustomerFilterQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(CustomerFilterQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.Customers.AsQueryable();

        if (request.PowerBoost)
        {
            query = query.Where(x => x.PowerBoost);
        }

        if (request.Query?.Region?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => 
                request.Query.Region.Contains(t.Site.Country.RegionId)));
        }

        if (request.Query?.Country?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => 
                request.Query.Country.Contains(t.Site.CountryId)));
        }

        if (request.Query?.Site?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => 
                request.Query.Site.Contains(t.SiteId)));
        }

        if (request.Query?.TurbineId?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => 
                request.Query.TurbineId.Contains(t.Id)));
        }
        
        if (request.Query?.LocationTypeName?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => request.Query.LocationTypeName.Contains(t.LocationTypeName)));
        }

        if (request.Query?.Model?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => 
                request.Query.Model.Contains(t.ModelId)));
        }

        if (request.Query?.Platform?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => 
                request.Query.Platform.Contains(t.TurbineModel.TurbinePlatformId)));
        }

        var result = await query
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);

        return result;
    }
}