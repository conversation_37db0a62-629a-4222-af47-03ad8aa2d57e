using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Filter;

public class LocationTypeNameFilterQuery : MediatR.IRequest<List<ValueIntLabel>>
{
    public FilterQuery Query { get; init; }
    public bool PowerBoost { get; init; }
}

public class LocationTypeNameFilterQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<LocationTypeNameFilterQuery, List<ValueIntLabel>>
{
    public async Task<List<ValueIntLabel>> Handle(LocationTypeNameFilterQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.Turbines.AsQueryable();

        query = request.PowerBoost ? query.Where(x => x.PowerBoost) : query.Where(x => x.IsPresent);

        if (request.Query.Customer?.Count > 0)
        {
            query = query.Where(x => x.CustomerId.HasValue &&
                                     request.Query.Customer.Contains((Guid)x.CustomerId));
        }

        if (request.Query.Region?.Count > 0)
        {
            query = query.Where(x => request.Query.Region.Contains(x.Site.Country.RegionId));
        }

        if (request.Query.Country?.Count > 0)
        {
            query = query.Where(x => request.Query.Country.Contains(x.Site.CountryId));
        }

        if (request.Query.Site?.Count > 0)
        {
            query = query.Where(x => request.Query.Site.Contains(x.SiteId));
        }

        if (request.Query.Platform?.Count > 0)
        {
            query = query.Where(x => request.Query.Platform.Contains(x.TurbineModel.TurbinePlatformId));
        }

        if (request.Query.Model?.Count > 0)
        {
            query = query.Where(x => request.Query.Model.Contains(x.ModelId));
        }

        if (request.Query.LocationTypeName?.Count > 0)
        {
            query = query.Where(x => request.Query.LocationTypeName.Contains(x.LocationTypeName));
        }

        var result = await query
            .Select(x => x.LocationTypeName)
            .Distinct()
            .ToListAsync(cancellationToken);

        return result.Select(x => new ValueIntLabel
            {
                Value = (int)x,
                Label = x.ToString()
            })
            .OrderBy(x => x.Label)
            .ToList();
    }
}