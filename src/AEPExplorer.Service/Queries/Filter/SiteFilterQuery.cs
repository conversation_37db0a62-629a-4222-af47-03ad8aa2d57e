using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Filter;

public class SiteFilterQuery : MediatR.IRequest<List<ValueLabel>>
{
    public FilterQuery Query { get; init; }
    public bool PowerBoost { get; init; }
}

public class SiteFilterQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<SiteFilterQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(SiteFilterQuery request, CancellationToken cancellationToken)
    {
        var query = dbContext.Sites.AsQueryable();

        query = request.PowerBoost ? query.Where(x => x.PowerBoost) : query.Where(x => x.IsPresent);

        if (request.Query.Customer?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => t.CustomerId.HasValue &&
                                                         request.Query.Customer.Contains((Guid)t.CustomerId)));
        }

        if (request.Query.Region?.Count > 0)
        {
            query = query.Where(x => request.Query.Region.Contains(x.Country.RegionId));
        }

        if (request.Query.Country?.Count > 0)
        {
            query = query.Where(x => request.Query.Country.Contains(x.CountryId));
        }

        if (request.Query.TurbineId?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => request.Query.TurbineId.Contains(t.Id)));
        }

        if (request.Query.LocationTypeName?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => request.Query.LocationTypeName.Contains(t.LocationTypeName)));
        }

        if (request.Query.Model?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t => request.Query.Model.Contains(t.ModelId)));
        }

        if (request.Query.Platform?.Count > 0)
        {
            query = query.Where(x => x.Turbines.Any(t =>
                request.Query.Platform.Contains(t.TurbineModel.TurbinePlatformId)));
        }

        var result = await query
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);

        return result;
    }
}