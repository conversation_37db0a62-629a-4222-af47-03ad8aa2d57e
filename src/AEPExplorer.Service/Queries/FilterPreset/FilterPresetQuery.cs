using AEPExplorer.Data.EF;

namespace AEPExplorer.Service.Queries.FilterPreset;

public class FilterPresetQuery : MediatR.IRequest<List<Model.Domain.FilterPreset>>
{
    public Guid UserId { get; set; }
}

public class FilterPresetQueryHandler : IRequestHandler<FilterPresetQuery, List<Model.Domain.FilterPreset>>
{
    private readonly AepExplorerDbContext _dbContext;

    public FilterPresetQueryHandler(AepExplorerDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<List<Model.Domain.FilterPreset>> Handle(FilterPresetQuery request, CancellationToken cancellationToken)
    {
        return await _dbContext.FilterPreset.Where(x=>x.UserId == request.UserId)
            .OrderByDescending(x => x.Favorite)
            .ThenByDescending(x => x.CreatedAt)
            .ToListAsync(cancellationToken: cancellationToken);
    }
}