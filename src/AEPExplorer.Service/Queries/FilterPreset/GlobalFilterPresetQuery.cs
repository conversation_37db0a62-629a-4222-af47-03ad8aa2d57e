namespace AEPExplorer.Service.Queries.FilterPreset;

public class GlobalFilterPresetQuery : MediatR.IRequest<List<Model.Domain.FilterPreset>>
{
}

public class GlobalFilterPresetQueryHandler : IRequestHandler<GlobalFilterPresetQuery, List<Model.Domain.FilterPreset>>
{
    public async Task<List<Model.Domain.FilterPreset>> <PERSON>le(GlobalFilterPresetQuery request, CancellationToken cancellationToken)
    {
        var result = new List<Model.Domain.FilterPreset>
        {
            new()
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                Name = "ICE",
                Query =
                    $"{{\"Category\":[" +
                    $"\"Ice. No alarm - Operating with OWI-AO|&|{new Guid("0A7477A67E8EB450C9DC834CB38F8FE4")}\"," +
                    $"\"Ice. Operating with anti-icing|&|{new Guid("8C25EA5C2AC9B9444D6B4EFB157A7D89")}\"," +
                    $"\"Ice. Operating with OWI-AO|&|{new Guid("EA0F3AB28FD718F2F1AC310EE5DDBF05")}\"," +
                    $"\"Ice. Other|&|{new Guid("1D28B695121D6044A9DB5A75B4D510D5")}\"," +
                    $"\"Ice. Stopped|&|{new Guid("CA8E4E1056FF3A0B05F9C5FA67B1CB5A")}\"," +
                    $"\"Ice. Stopped and de-icing|&|{new Guid("5E2D66A6ADE3184A712A4D9325DC41C9")}\"," +
                    $"\"Ice. Stopped by OWI strategy|&|{new Guid("55E8A78E70B0965E4224F44256FCECC0")}\"," +
                    $"\"Ice. Stopped. Blades shake|&|{new Guid("FEDFE79623CB7548745EDD9D9EDCDA8A")}\"," +
                    $"\"Ice. Stopped. Fault in nacelle wind sensors|&|{new Guid("07039E88331D639D387282DA57196227")}\"," +
                    $"\"Ice. Stopped. Waiting for release|&|{new Guid("561F94576FA1CF36EB9E4505890E1B6A")}\"," +
                    $"\"Ice. Work Order|&|{new Guid("08A08E9DAC00E7EB96C01DD00C112D05")}\"," +
                    $"\"Remote stop Ice|&|{new Guid("9C101B724E19D0F15ACE50D884FEF681")}\"" +
                    $"]}}"
            },
            new()
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                Name = "NOISE",
                Query =
                    $"{{\"Category\":[\"Low Noise - normal operation|&|00372e23-031f-7896-680b-7832f843a74e\",\"Remote stop Noise|&|d76a6533-63f4-a9c3-c3a0-f17bc2352270\",\"Low Noise - failsafe operation|&|5a575d2a-64c7-6c1f-6046-b56e667ec8c9\"]}}"
            },
            new()
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                Name = "TEMPERATURE",
                Query =
                    $"{{\"Category\":[\"High temperature|&|f94ae1a5-0273-a51c-0420-28926a43a8fa\",\"High Component temperature - degraded operation|&|6f35a78f-8aa9-7de1-abf7-4f1e26e5fda5\",\"Component temperature out of range|&|a179ce2c-87eb-e8c9-a0be-ae2c69db936e\",\"Low temperature|&|4d6984de-af5d-e099-dfa7-62f940fa79c8\",\"No alarm - temperature|&|3ff246aa-d0b4-1300-841d-2f341187725d\",\"Low temperature - preheating|&|2d867ce7-d111-6d4c-9112-76be090ad4ce\",\"High temperature - derated operation|&|715d8991-13e9-e1d8-836d-14c54c6c2b2c\"]}}"
            },
            new()
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                Name = "EXPORT CAPACITY",
                Query =
                    $"{{\"Category\":[\"External active power limitation|&|a36bdac8-ecfa-6e0f-3543-49f088689a05\",\"Power limited by frequency control|&|0898f5f9-7cf7-f1e3-7bd4-ec2fa9849859\",\"Remote stop - Active power regulation|&|2c73b484-d4a2-826c-1205-3587fe2b5624\",\"Remote stop - Grid frequency regulation|&|32ee559c-b498-5695-a6e1-ac2970b1f7b5\",\"Remote stop Grid operator|&|0d01d908-a30c-8277-b070-9261e92daa8f\"]}}"
            },
            new()
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                Name = "HIGH WIND SPEED",
                Query =
                    $"{{\"Category\":[\"High wind speed|&|752533cd-ee5b-c6d1-baef-96c8849dc69d\",\"High Wind Turbulence Intensity|&|27ff0bcf-a4f5-eb15-bcc8-e4683ea5a288\",\"HighWind operation - derated|&|7844fcd0-9860-a02c-507b-382e510cb575\",\"Wind Speed Out of range|&|ecd2bf2d-af5a-4818-6132-644b974090f8\"]}}"
            },
            new()
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                Name = "LOW WIND SPEED",
                Query =
                    $"{{\"Category\":[\"Low wind speed|&|b2bf2b68-132d-3a05-d30c-310d1eaf4cec\",\"No alarm - wind speed|&|09f2def6-b428-6d53-3d41-425c72aa324e\",\"Wind Speed Out of range|&|ecd2bf2d-af5a-4818-6132-644b974090f8\"]}}"
            }
        };

        return result;
    }
}