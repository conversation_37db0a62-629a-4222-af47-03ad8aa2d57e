using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Structure;

public class TurbinePlatformNamesQuery : MediatR.IRequest<List<ValueLabel>>
{
    public List<Guid> IdLIst { get; init; }
}

public class TurbinePlatformNamesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<TurbinePlatformNamesQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(TurbinePlatformNamesQuery request, CancellationToken cancellationToken)
    {
        var result = await dbContext.TurbinePlatforms.Where(x => request.IdLIst.Contains(x.Id))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken: cancellationToken);

        return result;
    }
}