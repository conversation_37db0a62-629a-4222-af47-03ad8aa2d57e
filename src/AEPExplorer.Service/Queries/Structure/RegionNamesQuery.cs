using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Structure;

public class RegionNamesQuery : MediatR.IRequest<List<ValueLabel>>
{
    public List<Guid> IdLIst { get; init; }
}

public class RegionNamesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<RegionNamesQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(RegionNamesQuery request, CancellationToken cancellationToken)
    {
        var result = await dbContext.Regions.Where(x => request.IdLIst.Contains(x.Id))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken: cancellationToken);

        return result;
    }
}