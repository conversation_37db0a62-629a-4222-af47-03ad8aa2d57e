using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Structure;

public class CustomerNamesQuery : MediatR.IRequest<List<ValueLabel>>
{
    public List<Guid> IdLIst { get; init; }
}

public class CustomerNamesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<CustomerNamesQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(CustomerNamesQuery request, CancellationToken cancellationToken)
    {
        var result = await dbContext.Customers.Where(x => request.IdLIst.Contains(x.Id))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken: cancellationToken);

        return result;
    }
}