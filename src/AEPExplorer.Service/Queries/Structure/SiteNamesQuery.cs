using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Structure;

public class SiteNamesQuery : MediatR.IRequest<List<ValueLabel>>
{
    public List<Guid> IdLIst { get; init; }
}

public class SiteNamesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<SiteNamesQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(SiteNamesQuery request, CancellationToken cancellationToken)
    {
        var result = await dbContext.Sites.Where(x => request.IdLIst.Contains(x.Id))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken: cancellationToken);

        return result;
    }
}