using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Structure;

public class TurbineNamesQuery : MediatR.IRequest<List<ValueLabel>>
{
    public List<Guid> IdLIst { get; init; }
}

public class TurbineNamesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<TurbineNamesQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(TurbineNamesQuery request, CancellationToken cancellationToken)
    {
        var result = await dbContext.Turbines.Where(x => request.IdLIst.Contains(x.Id))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken: cancellationToken);

        return result;
    }
}