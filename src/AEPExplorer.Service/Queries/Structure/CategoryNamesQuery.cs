using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Structure;

public class CategoryNamesQuery : MediatR.IRequest<List<ValueLabel>>
{
    public List<Guid> IdLIst { get; init; }
}

public class CategoryNamesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<CategoryNamesQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(CategoryNamesQuery request, CancellationToken cancellationToken)
    {
        var result = await dbContext.Categories.Where(x => request.IdLIst.Contains(x.Id))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken: cancellationToken);

        return result;
    }
}