using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Structure;

public class CountryNamesQuery : MediatR.IRequest<List<ValueLabel>>
{
    public List<Guid> IdLIst { get; init; }
}

public class CountryNamesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<CountryNamesQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(CountryNamesQuery request, CancellationToken cancellationToken)
    {
        var result = await dbContext.Countries.Where(x => request.IdLIst.Contains(x.Id))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken: cancellationToken);

        return result;
    }
}