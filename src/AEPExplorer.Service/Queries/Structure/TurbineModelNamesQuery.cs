using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Structure;

public class TurbineModelNamesQuery : MediatR.IRequest<List<ValueLabel>>
{
    public List<Guid> IdLIst { get; init; }
}

public class TurbineModelNamesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<TurbineModelNamesQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(TurbineModelNamesQuery request, CancellationToken cancellationToken)
    {
        var result = await dbContext.Models.Where(x => request.IdLIst.Contains(x.Id))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken: cancellationToken);

        return result;
    }
}