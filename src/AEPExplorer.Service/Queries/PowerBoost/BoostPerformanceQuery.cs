using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.PowerBoost;

public class BoostPerformanceQuery : MediatR.IRequest<List<BoostPerformance>>
{
    public Query Query { get; init; }
}

public class BoostPerformanceQueryHandler(IElasticClient elasticClient) : IRequestHandler<BoostPerformanceQuery, List<BoostPerformance>>
{
    public async Task<List<BoostPerformance>> Handle(BoostPerformanceQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(PowerBoostReadData.BoostExpectedCriteriaOK),
            nameof(PowerBoostReadData.BoostExpected),
            nameof(PowerBoostReadData.BoostCoverage),
            nameof(PowerBoostReadData.BoostCriteriaOK),
            nameof(PowerBoostReadData.BoostAva),
            nameof(PowerBoostReadData.BoostAct)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var items = await ElasticsearchSumService.PowerBoostSumByDateHistogramAsync(elasticClient, request.Query, aggregations, DateInterval.Day);

        var boostExpectedTimeData = new List<ValueDate>();
        var boostActualTimeData = new List<ValueDate>();
        var boostEfficiencyData = new List<ValueDate>();
        var boostUtilisationData = new List<ValueDate>();

        for (var dt = request.Query.DateFrom.Value; dt <= request.Query.DateTo.Value; dt = dt.AddDays(1))
        {
            items.TryGetValue(dt, out var item);
            if (item == null)
            {
                continue;
            }

            item.TryGetValue(nameof(PowerBoostReadData.BoostExpectedCriteriaOK), out var boostExpectedCriteriaOk);
            item.TryGetValue(nameof(PowerBoostReadData.BoostExpected), out var boostExpected);
            item.TryGetValue(nameof(PowerBoostReadData.BoostCoverage), out var boostCoverage);
            item.TryGetValue(nameof(PowerBoostReadData.BoostCriteriaOK), out var boostCriteriaOk);
            item.TryGetValue(nameof(PowerBoostReadData.BoostAva), out var boostAva);
            item.TryGetValue(nameof(PowerBoostReadData.BoostAct), out var boostAct);

            boostExpectedTimeData.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(boostExpectedCriteriaOk, boostCoverage)
            });
            boostActualTimeData.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(boostCriteriaOk, boostCoverage)
            });
            boostEfficiencyData.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(boostAva, boostExpected)
            });
            boostUtilisationData.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(boostAct, boostAva, limited: true)
            });
        }

        var boostExpectedCriteriaOkSum = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostExpectedCriteriaOK)]);
        var boostExpectedSum = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostExpected)]);
        var boostCoverageSum = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostCoverage)]);
        var boostCriteriaOkSum = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostCriteriaOK)]);
        var boostAvaSum = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostAva)]);
        var boostActSum = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostAct)]);

        var result = new List<BoostPerformance>
        {
            new() { Id = BoostPerformanceEnum.BoostEfficiency, Average = ConvertExtensions.CalculatePercentage(boostAvaSum, boostExpectedSum), TimelineData = boostEfficiencyData },
            new() { Id = BoostPerformanceEnum.BoostUtilisation, Average = ConvertExtensions.CalculatePercentage(boostActSum, boostAvaSum, limited: true), TimelineData = boostUtilisationData },
            new() { Id = BoostPerformanceEnum.BoostExpectedTime, Average = ConvertExtensions.CalculatePercentage(boostExpectedCriteriaOkSum, boostCoverageSum), TimelineData = boostExpectedTimeData },
            new() { Id = BoostPerformanceEnum.BoostActualTime, Average = ConvertExtensions.CalculatePercentage(boostCriteriaOkSum, boostCoverageSum), TimelineData = boostActualTimeData }
        };

        return result;
    }
}