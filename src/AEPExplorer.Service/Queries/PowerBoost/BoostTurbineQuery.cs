using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.PowerBoost;

public class BoostTurbineQuery : MediatR.IRequest<List<ValueLabel>>
{
    public Query Query { get; init; }
}

public class TurbineQueryHandler(AepExplorerDbContext dbContext, IElasticClient elasticClient) : IRequestHandler<BoostTurbineQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(BoostTurbineQuery request, CancellationToken cancellationToken)
    {
        var importedTurbines = await ElasticsearchGeneralService.GetPowerBoostDistinctTurbinesAsync(elasticClient, request.Query);
        var result = await dbContext.Turbines
            .Where(x => importedTurbines.Contains(x.Id))
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);

        return result;
    }
}