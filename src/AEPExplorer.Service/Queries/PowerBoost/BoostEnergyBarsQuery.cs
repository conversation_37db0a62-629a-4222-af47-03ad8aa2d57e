using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.PowerBoost;

public class BoostEnergyBarsQuery : MediatR.IRequest<List<NameValuePercentage>>
{
    public Query Query { get; init; }
}

public class BoostEnergyBarsQueryHandler(IElasticClient elasticClient) : IRequestHandler<BoostEnergyBarsQuery, List<NameValuePercentage>>
{
    public async Task<List<NameValuePercentage>> Handle(BoostEnergyBarsQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(PowerBoostReadData.BoostCriteriaOK),
            nameof(PowerBoostReadData.BoostExpectedCriteriaOK),
            nameof(PowerBoostReadData.BoostExpected),
            nameof(PowerBoostReadData.BoostEnergyCnt),
            nameof(PowerBoostReadData.BoostEnergyEst),
            nameof(PowerBoostReadData.BoostAct),
            nameof(PowerBoostReadData.BoostAva),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var items = await ElasticsearchSumService.PowerBoostSumAsync(elasticClient, request.Query, aggregations);

        var boostExpectedTime = new NameValuePercentage
        {
            Name = "Boost Expected Time",
            Value = (items[nameof(PowerBoostReadData.BoostExpectedCriteriaOK)] / 6).Round(2)
        };

        var boostActualTime = new NameValuePercentage
        {
            Name = "Boost Actual Time",
            Value = (items[nameof(PowerBoostReadData.BoostCriteriaOK)] / 6).Round(2)
        };

        var boostCounter = new NameValuePercentage
        {
            Name = "Boost Counter",
            Value = items[nameof(PowerBoostReadData.BoostEnergyCnt)]
        };

        var boostEstimated = new NameValuePercentage
        {
            Name = "Boost Estimated",
            Value = items[nameof(PowerBoostReadData.BoostEnergyEst)]
        };

        var actual = items[nameof(PowerBoostReadData.BoostAct)];
        var available = items[nameof(PowerBoostReadData.BoostAva)];
        var expected = items[nameof(PowerBoostReadData.BoostExpected)];
        var potential = available > 0 ? (expected * actual / available).Round(2) : 0;
        var losses = potential - actual;
        
        var boostActual = new NameValuePercentage
        {
            Name = "Boost Actual",
            Value = actual,
            Percentage = ConvertExtensions.CalculatePercentage(actual, potential)
        };
        var boostLosses = new NameValuePercentage
        {
            Name = "Boost Losses",
            Value = losses,
            Percentage = ConvertExtensions.CalculatePercentage(losses, potential)
        };
        var boostAvailable = new NameValuePercentage
        {
            Name = "Boost Available",
            Value = potential,
            Percentage = 100
        };

        var result = new List<NameValuePercentage> { boostExpectedTime, boostActualTime, boostCounter, boostEstimated, boostActual, boostLosses, boostAvailable };
        return result;
    }
}