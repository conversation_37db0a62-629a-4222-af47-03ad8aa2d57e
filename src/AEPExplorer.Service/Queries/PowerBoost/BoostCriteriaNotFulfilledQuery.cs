using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.PowerBoost;

public class BoostCriteriaNotFulfilledQuery : MediatR.IRequest<List<BoostCriteria>>
{
    public Query Query { get; init; }
}

public class BoostCriteriaNotFulfilledQueryHandler(IElasticClient elasticClient) : IRequestHandler<BoostCriteriaNotFulfilledQuery, List<BoostCriteria>>
{
    public async Task<List<BoostCriteria>> Handle(BoostCriteriaNotFulfilledQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(PowerBoostReadData.NotCurtailed_CNF),
            nameof(PowerBoostReadData.BoostCoverage),
            nameof(PowerBoostReadData.RunningRestricted_CNF),
            nameof(PowerBoostReadData.Pitch_CNF),
            nameof(PowerBoostReadData.Disabled_CNF),
            nameof(PowerBoostReadData.SafeMode_CNF),
            nameof(PowerBoostReadData.AmbientTemp_CNF),
            nameof(PowerBoostReadData.GridVoltage_CNF),
            nameof(PowerBoostReadData.PowerFactor_CNF),
            nameof(PowerBoostReadData.CoolDown_CNF),
            nameof(PowerBoostReadData.Turbulence_CNF),
            nameof(PowerBoostReadData.ACS_CNF),
            nameof(PowerBoostReadData.SensorError_CNF),
            nameof(PowerBoostReadData.RSA_CNF),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var query = request.Query;
        query.TurbineId = [query.TurbineId.First()];
        var items = await ElasticsearchSumService.PowerBoostSumByDateHistogramAsync(elasticClient, request.Query, aggregations, DateInterval.Day);

        var boostCoverageTimeline = new List<ValueDate>();
        var notCurtailedTimeline = new List<ValueDate>();
        var runningRestrictedTimeline = new List<ValueDate>();
        var pitchTimeline = new List<ValueDate>();
        var disabledTimeline = new List<ValueDate>();
        var safeModeTimeline = new List<ValueDate>();
        var ambientTemperatureTimeline = new List<ValueDate>();
        var gridVoltageTimeline = new List<ValueDate>();
        var powerFactorTimeline = new List<ValueDate>();
        var coolDownTimeline = new List<ValueDate>();
        var turbulenceTimeline = new List<ValueDate>();
        var acsTimeline = new List<ValueDate>();
        var sensorErrorTimeline = new List<ValueDate>();
        var rsaTimeline = new List<ValueDate>();

        for (var dt = request.Query.DateFrom.Value; dt <= request.Query.DateTo.Value; dt = dt.AddDays(1))
        {
            items.TryGetValue(dt, out var item);
            if (item == null)
            {
                continue;
            }
            item.TryGetValue(nameof(PowerBoostReadData.NotCurtailed_CNF), out var notCurtailedCnf);
            item.TryGetValue(nameof(PowerBoostReadData.BoostCoverage), out var boostCoverage);
            item.TryGetValue(nameof(PowerBoostReadData.RunningRestricted_CNF), out var runningRestrictedCnf);
            item.TryGetValue(nameof(PowerBoostReadData.Pitch_CNF), out var pitchCnf);
            item.TryGetValue(nameof(PowerBoostReadData.Disabled_CNF), out var disabledCnf);
            item.TryGetValue(nameof(PowerBoostReadData.SafeMode_CNF), out var safeModeCnf);
            item.TryGetValue(nameof(PowerBoostReadData.AmbientTemp_CNF), out var ambientTempCnf);
            item.TryGetValue(nameof(PowerBoostReadData.GridVoltage_CNF), out var gridVoltageCnf);
            item.TryGetValue(nameof(PowerBoostReadData.PowerFactor_CNF), out var powerFactorCnf);
            item.TryGetValue(nameof(PowerBoostReadData.CoolDown_CNF), out var coolDownCnf);
            item.TryGetValue(nameof(PowerBoostReadData.Turbulence_CNF), out var turbulenceCnf);
            item.TryGetValue(nameof(PowerBoostReadData.ACS_CNF), out var acsCnf);
            item.TryGetValue(nameof(PowerBoostReadData.SensorError_CNF), out var sensorErrorCnf);
            item.TryGetValue(nameof(PowerBoostReadData.RSA_CNF), out var rsaCnf);

            boostCoverageTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(boostCoverage, 144, 4) 
            });
            notCurtailedTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(notCurtailedCnf, boostCoverage, 4)
            });
            runningRestrictedTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(runningRestrictedCnf, boostCoverage, 4)
            });
            pitchTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(pitchCnf, boostCoverage, 4)
            });
            disabledTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(disabledCnf, boostCoverage, 4)
            });
            ambientTemperatureTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(ambientTempCnf, boostCoverage, 4)
            });
            gridVoltageTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(gridVoltageCnf, boostCoverage, 4)
            });
            safeModeTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(safeModeCnf, boostCoverage, 4)
            });
            powerFactorTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(powerFactorCnf, boostCoverage, 4)
            });
            coolDownTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(coolDownCnf, boostCoverage, 4)
            });
            turbulenceTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(turbulenceCnf, boostCoverage, 4)
            });
            acsTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(acsCnf, boostCoverage, 4)
            });
            sensorErrorTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(sensorErrorCnf, boostCoverage, 4)
            });
            rsaTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(rsaCnf, boostCoverage, 4)
            });
        }

        var num10Min = request.Query.TotalDays * 144;
        var boostCoverageSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.BoostCoverage)]);
        var notCurtailedSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.NotCurtailed_CNF)]);
        var runningRestrictedSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.RunningRestricted_CNF)]);
        var pitchSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.Pitch_CNF)]);
        var disabledSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.Disabled_CNF)]);
        var safeModeSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.SafeMode_CNF)]);
        var ambientTemperatureSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.AmbientTemp_CNF)]);
        var gridVoltageSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.GridVoltage_CNF)]);
        var powerFactorSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.PowerFactor_CNF)]);
        var coolDownSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.CoolDown_CNF)]);
        var turbulenceSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.Turbulence_CNF)]);
        var acsSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.ACS_CNF)]);
        var sensorErrorSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.SensorError_CNF)]);
        var rsaSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.RSA_CNF)]);
        
        var result = new List<BoostCriteria>
        {
            new() { Id = BoostCriteriaEnum.BoostCoverage, Average = ConvertExtensions.CalculatePercentage(boostCoverageSum, num10Min),TimelineData = boostCoverageTimeline },
            new() { Id = BoostCriteriaEnum.NotCurtailed,Average = ConvertExtensions.CalculatePercentage(notCurtailedSum, boostCoverageSum) , TimelineData = notCurtailedTimeline },
            new() { Id = BoostCriteriaEnum.RunningRestricted,Average = ConvertExtensions.CalculatePercentage(runningRestrictedSum, boostCoverageSum) , TimelineData = runningRestrictedTimeline },
            new() { Id = BoostCriteriaEnum.Pitch, Average = ConvertExtensions.CalculatePercentage(pitchSum, boostCoverageSum), TimelineData = pitchTimeline },
            new() { Id = BoostCriteriaEnum.Disabled, Average = ConvertExtensions.CalculatePercentage(disabledSum, boostCoverageSum), TimelineData = disabledTimeline },
            new() { Id = BoostCriteriaEnum.SafeMode, Average = ConvertExtensions.CalculatePercentage(safeModeSum, boostCoverageSum), TimelineData = safeModeTimeline },
            new() { Id = BoostCriteriaEnum.AmbientTemperature,Average = ConvertExtensions.CalculatePercentage(ambientTemperatureSum, boostCoverageSum), TimelineData = ambientTemperatureTimeline },
            new() { Id = BoostCriteriaEnum.GridVoltage, Average = ConvertExtensions.CalculatePercentage(gridVoltageSum, boostCoverageSum), TimelineData = gridVoltageTimeline },
            new() { Id = BoostCriteriaEnum.PowerFactor, Average = ConvertExtensions.CalculatePercentage(powerFactorSum, boostCoverageSum), TimelineData = powerFactorTimeline },
            new() { Id = BoostCriteriaEnum.CoolDown, Average = ConvertExtensions.CalculatePercentage(coolDownSum, boostCoverageSum), TimelineData = coolDownTimeline },
            new() { Id = BoostCriteriaEnum.Turbulence, Average = ConvertExtensions.CalculatePercentage(turbulenceSum, boostCoverageSum), TimelineData = turbulenceTimeline },
            new() { Id = BoostCriteriaEnum.Acs, Average = ConvertExtensions.CalculatePercentage(acsSum, boostCoverageSum), TimelineData = acsTimeline },
            new() { Id = BoostCriteriaEnum.SensorError, Average = ConvertExtensions.CalculatePercentage(sensorErrorSum, boostCoverageSum), TimelineData = sensorErrorTimeline },
            new() { Id = BoostCriteriaEnum.Rsa,Average = ConvertExtensions.CalculatePercentage(rsaSum, boostCoverageSum) , TimelineData = rsaTimeline },
        };

        return result;
    }
}