using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.PowerBoost;

public class BoostEnergyCumulativeTimelineQuery : MediatR.IRequest<List<BoostEnergy>>
{
    public Query Query { get; init; }
}

public class BoostEnergyCumulativeTimelineQueryHandler(IElasticClient elasticClient) : IRequestHandler<BoostEnergyCumulativeTimelineQuery, List<BoostEnergy>>
{
    public async Task<List<BoostEnergy>> Handle(BoostEnergyCumulativeTimelineQuery request, CancellationToken cancellationToken)
    {
        var boostEnergyCntItems = await ElasticsearchSumService.PowerBoostCumulateSumByDateHistogramAsync(elasticClient, request.Query, x => x.Field(f => f.BoostEnergyCnt), DateInterval.Day);
        var boostActItems = await ElasticsearchSumService.PowerBoostCumulateSumByDateHistogramAsync(elasticClient, request.Query, x => x.Field(f => f.BoostAct), DateInterval.Day);

        var boostCounterCumulative = new BoostEnergy
        {
            Name = "Boost Counter",
            TimelineData = boostEnergyCntItems.Select(x => new ValueDate() { Date = x.Date, Value = x.Value }).ToList()
        };

        var boostActualCumulative = new BoostEnergy
        {
            Name = "Boost Actual",
            TimelineData = boostActItems.Select(x => new ValueDate() { Date = x.Date, Value = x.Value }).ToList()
        };

        var result = new List<BoostEnergy> { boostCounterCumulative, boostActualCumulative };
        return result;
    }
}