using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.PowerBoost;

public class BoostCriteriaFulfilledQuery : MediatR.IRequest<List<BoostCriteria>>
{
    public Query Query { get; init; }
}

public class BoostCriteriaFulfilledQueryHandler(IElasticClient elasticClient) : IRequestHandler<BoostCriteriaFulfilledQuery, List<BoostCriteria>>
{
    public async Task<List<BoostCriteria>> Handle(BoostCriteriaFulfilledQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(PowerBoostReadData.NotCurtailed_CF),
            nameof(PowerBoostReadData.BoostCoverage),
            nameof(PowerBoostReadData.RunningRestricted_CF),
            nameof(PowerBoostReadData.Pitch_CF),
            nameof(PowerBoostReadData.Disabled_CF),
            nameof(PowerBoostReadData.SafeMode_CF),
            nameof(PowerBoostReadData.AmbientTemp_CF),
            nameof(PowerBoostReadData.GridVoltage_CF),
            nameof(PowerBoostReadData.PowerFactor_CF),
            nameof(PowerBoostReadData.CoolDown_CF),
            nameof(PowerBoostReadData.Turbulence_CF),
            nameof(PowerBoostReadData.ACS_CF),
            nameof(PowerBoostReadData.SensorError_CF),
            nameof(PowerBoostReadData.RSA_CF),
            nameof(PowerBoostReadData.BoostExpectedCriteriaOK),
            nameof(PowerBoostReadData.BoostExpected),
            nameof(PowerBoostReadData.BoostAva),
            nameof(PowerBoostReadData.BoostAct),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var query = request.Query;
        query.TurbineId = [query.TurbineId.First()];
        var items = await ElasticsearchSumService.PowerBoostSumByDateHistogramAsync(elasticClient, request.Query, aggregations, DateInterval.Day);

        var boostCoverageTimeline = new List<ValueDate>();
        var notCurtailedTimeline = new List<ValueDate>();
        var runningRestrictedTimeline = new List<ValueDate>();
        var pitchTimeline = new List<ValueDate>();
        var disabledTimeline = new List<ValueDate>();
        var safeModeTimeline = new List<ValueDate>();
        var ambientTemperatureTimeline = new List<ValueDate>();
        var gridVoltageTimeline = new List<ValueDate>();
        var powerFactorTimeline = new List<ValueDate>();
        var coolDownTimeline = new List<ValueDate>();
        var turbulenceTimeline = new List<ValueDate>();
        var acsTimeline = new List<ValueDate>();
        var sensorErrorTimeline = new List<ValueDate>();
        var rsaTimeline = new List<ValueDate>();

        for (var dt = request.Query.DateFrom.Value; dt <= request.Query.DateTo.Value; dt = dt.AddDays(1))
        {
            items.TryGetValue(dt, out var item);
            if (item == null)
            {
                continue;
            }
            item.TryGetValue(nameof(PowerBoostReadData.NotCurtailed_CF), out var notCurtailed);
            item.TryGetValue(nameof(PowerBoostReadData.BoostCoverage), out var boostCoverage);
            item.TryGetValue(nameof(PowerBoostReadData.RunningRestricted_CF), out var runningRestrictedCf);
            item.TryGetValue(nameof(PowerBoostReadData.Pitch_CF), out var pitchCf);
            item.TryGetValue(nameof(PowerBoostReadData.Disabled_CF), out var disabledCf);
            item.TryGetValue(nameof(PowerBoostReadData.SafeMode_CF), out var safeModeCf);
            item.TryGetValue(nameof(PowerBoostReadData.AmbientTemp_CF), out var ambientTempCf);
            item.TryGetValue(nameof(PowerBoostReadData.GridVoltage_CF), out var gridVoltageCf);
            item.TryGetValue(nameof(PowerBoostReadData.PowerFactor_CF), out var powerFactorCf);
            item.TryGetValue(nameof(PowerBoostReadData.CoolDown_CF), out var coolDownCf);
            item.TryGetValue(nameof(PowerBoostReadData.Turbulence_CF), out var turbulenceCf);
            item.TryGetValue(nameof(PowerBoostReadData.ACS_CF), out var acsCf);
            item.TryGetValue(nameof(PowerBoostReadData.SensorError_CF), out var sensorErrorCf);
            item.TryGetValue(nameof(PowerBoostReadData.RSA_CF), out var rsaCf);

            boostCoverageTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(boostCoverage, 144, 4) 
            });
            notCurtailedTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(notCurtailed, boostCoverage, 4)
            });
            runningRestrictedTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(runningRestrictedCf, notCurtailed, 4)
            });
            pitchTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(pitchCf, runningRestrictedCf, 4)
            });
            disabledTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(disabledCf, pitchCf, 4)
            });
            ambientTemperatureTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(ambientTempCf, disabledCf, 4)
            });
            gridVoltageTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(gridVoltageCf, ambientTempCf, 4)
            });
            safeModeTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(safeModeCf, gridVoltageCf, 4)
            });
            powerFactorTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(powerFactorCf, safeModeCf, 4)
            });
            coolDownTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(coolDownCf, powerFactorCf, 4)
            });
            turbulenceTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(turbulenceCf, coolDownCf, 4)
            });
            acsTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(acsCf, turbulenceCf, 4)
            });
            sensorErrorTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(sensorErrorCf, acsCf, 4)
            });
            rsaTimeline.Add(new ValueDate
            {
                Date = dt,
                Value = ConvertExtensions.CalculatePercentageNullable(rsaCf, sensorErrorCf, 4)
            });
        }
        
        var num10Min = request.Query.TotalDays * 144;
        var boostCoverageSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.BoostCoverage)]);
        var notCurtailedSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.NotCurtailed_CF)]);
        var runningRestrictedSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.RunningRestricted_CF)]);
        var pitchSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.Pitch_CF)]);
        var disabledSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.Disabled_CF)]);
        var safeModeSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.SafeMode_CF)]);
        var ambientTemperatureSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.AmbientTemp_CF)]);
        var gridVoltageSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.GridVoltage_CF)]);
        var powerFactorSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.PowerFactor_CF)]);
        var coolDownSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.CoolDown_CF)]);
        var turbulenceSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.Turbulence_CF)]);
        var acsSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.ACS_CF)]);
        var sensorErrorSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.SensorError_CF)]);
        var rsaSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.RSA_CF)]);
        var boostExpectedCriteriaOkSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.BoostExpectedCriteriaOK)]);
        var boostExpectedSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.BoostExpected)]);
        var boostAvaSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.BoostAva)]);
        var boostActSum = items.Values.Sum(x=>x[nameof(PowerBoostReadData.BoostAct)]);
        var result = new List<BoostCriteria>
        {
            new() { Id = BoostCriteriaEnum.BoostCoverage, Average = ConvertExtensions.CalculatePercentage(boostCoverageSum, num10Min),TimelineData = boostCoverageTimeline },
            new() { Id = BoostCriteriaEnum.NotCurtailed,Average = ConvertExtensions.CalculatePercentage(notCurtailedSum, boostCoverageSum) , TimelineData = notCurtailedTimeline },
            new() { Id = BoostCriteriaEnum.RunningRestricted,Average = ConvertExtensions.CalculatePercentage(runningRestrictedSum, notCurtailedSum) , TimelineData = runningRestrictedTimeline },
            new() { Id = BoostCriteriaEnum.Pitch, Average = ConvertExtensions.CalculatePercentage(pitchSum, runningRestrictedSum), TimelineData = pitchTimeline },
            new() { Id = BoostCriteriaEnum.Disabled, Average = ConvertExtensions.CalculatePercentage(disabledSum, pitchSum), TimelineData = disabledTimeline },
            new() { Id = BoostCriteriaEnum.AmbientTemperature,Average = ConvertExtensions.CalculatePercentage(ambientTemperatureSum, disabledSum), TimelineData = ambientTemperatureTimeline },
            new() { Id = BoostCriteriaEnum.GridVoltage, Average = ConvertExtensions.CalculatePercentage(gridVoltageSum, ambientTemperatureSum), TimelineData = gridVoltageTimeline },
            new() { Id = BoostCriteriaEnum.SafeMode, Average = ConvertExtensions.CalculatePercentage(safeModeSum, gridVoltageSum), TimelineData = safeModeTimeline },
            new() { Id = BoostCriteriaEnum.PowerFactor, Average = ConvertExtensions.CalculatePercentage(powerFactorSum, safeModeSum), TimelineData = powerFactorTimeline },
            new() { Id = BoostCriteriaEnum.CoolDown, Average = ConvertExtensions.CalculatePercentage(coolDownSum, powerFactorSum), TimelineData = coolDownTimeline },
            new() { Id = BoostCriteriaEnum.Turbulence, Average = ConvertExtensions.CalculatePercentage(turbulenceSum, coolDownSum), TimelineData = turbulenceTimeline },
            new() { Id = BoostCriteriaEnum.Acs, Average = ConvertExtensions.CalculatePercentage(acsSum, turbulenceSum), TimelineData = acsTimeline },
            new() { Id = BoostCriteriaEnum.SensorError, Average = ConvertExtensions.CalculatePercentage(sensorErrorSum, acsSum), TimelineData = sensorErrorTimeline },
            new() { Id = BoostCriteriaEnum.Rsa,Average = ConvertExtensions.CalculatePercentage(rsaSum, sensorErrorSum) , TimelineData = rsaTimeline },
            new() { Id = BoostCriteriaEnum.BoostPossibleInPeriod,Average = ConvertExtensions.CalculatePercentage(boostExpectedCriteriaOkSum, boostCoverageSum) , TimelineData = [] },
            new() { Id = BoostCriteriaEnum.UtilisationByParkPilot,Average = ConvertExtensions.CalculatePercentage(boostActSum, boostAvaSum) , TimelineData = [] },
            new() { Id = BoostCriteriaEnum.EfficiencyWhenBoostPossible,Average = ConvertExtensions.CalculatePercentage(boostAvaSum, boostExpectedSum) , TimelineData = [] },
        };

        return result;
    }
}