using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.PowerBoost.Table;

public static class BoostTableHelper
{
    public static BoostTableModel GetTotal(Dictionary<Guid, Dictionary<string, double>> items, Query query)
    {
        if (items.Values.Count == 0) return new BoostTableModel();
        
        var numberOfTurbines = items.Count;
        if (items.Values.First().ContainsKey("NumberOfTurbines"))
        {
            numberOfTurbines = (int)items.Values.Sum(x => x["NumberOfTurbines"]);
        }

        var num10Min = query.TotalDays * 144 * numberOfTurbines;
        var boostCoverage = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostCoverage)]);
        var boostEnergyCnt = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostEnergyCnt)]);
        var boostEnergyEst = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostEnergyEst)]);
        var boostAva = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostAva)]);
        var boostAct = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostAct)]);
        var boostExpected = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostExpected)]);
        var notCurtailedCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.NotCurtailed_CF)]);
        var runningRestrictedCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.RunningRestricted_CF)]);
        var pitchCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Pitch_CF)]);
        var disabledCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Disabled_CF)]);
        var safeModeCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.SafeMode_CF)]);
        var ambientTempCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.AmbientTemp_CF)]);
        var gridVoltageCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.GridVoltage_CF)]);
        var powerFactorCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.PowerFactor_CF)]);
        var turbulenceCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Turbulence_CF)]);
        var acsCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.ACS_CF)]);
        var sensorErrorCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.SensorError_CF)]);
        var rsaCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.RSA_CF)]);
        var boostCriteriaOk = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostCriteriaOK)]);
        var boostExpectedCriteriaOk = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostExpectedCriteriaOK)]);
        var boostExpMin = items.Values.Min(x => x[nameof(PowerBoostReadData.BoostExpMin)]);
        var boostExpMax = items.Values.Max(x => x[nameof(PowerBoostReadData.BoostExpMax)]);
        var boostExpAvg = items.Values.Average(x => x[nameof(PowerBoostReadData.BoostExpAvg)]);
        var boostUtilisation = ConvertExtensions.CalculatePercentage(boostAct, boostAva, 4, limited: true);
        var boostEfficiency = ConvertExtensions.CalculatePercentage(boostAva, boostExpected, 4);
        var coolDownCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.CoolDown_CF)]);
        var notCurtailedCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.NotCurtailed_CNF)]);
        var runningRestrictedCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.RunningRestricted_CNF)]);
        var pitchCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Pitch_CNF)]);
        var disabledCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Disabled_CNF)]);
        var safeModeCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.SafeMode_CNF)]);
        var ambientTempCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.AmbientTemp_CNF)]);
        var gridVoltageCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.GridVoltage_CNF)]);
        var powerFactorCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.PowerFactor_CNF)]);
        var coolDownCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.CoolDown_CNF)]);
        var turbulenceCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Turbulence_CNF)]);
        var acsCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.ACS_CNF)]);
        var sensorErrorCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.SensorError_CNF)]);
        var rsaCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.RSA_CNF)]);
        var total = new BoostTableModel
        {
            Id = "Total",
            StartTimeUtc = query.DateFrom ?? DateTime.MinValue,
            EndTimeUtc = query.DateTo ?? DateTime.MinValue,
            Num10Min = num10Min,
            BoostCoverage = ConvertExtensions.CalculatePercentage(boostCoverage, num10Min, 4),
            BoostEnergyCnt = boostEnergyCnt.Round(4),
            BoostEnergyEst = boostEnergyEst.Round(4),
            BoostAva = boostAva.Round(4),
            BoostAct = boostAct.Round(4),
            BoostExpectedTime = ConvertExtensions.CalculatePercentage(boostExpectedCriteriaOk, boostCoverage, 4),
            BoostExpected = boostExpected,
            BoostEfficiencyTime = ConvertExtensions.CalculatePercentage(boostCriteriaOk, boostExpectedCriteriaOk, 4),
            BoostEfficiency = boostEfficiency,
            BoostActualTime = ConvertExtensions.CalculatePercentage(boostCriteriaOk, boostCoverage, 4),
            BoostUtilisation = boostUtilisation,
            BoostLosses = ((boostExpected - boostAva) * boostUtilisation / 100).Round(4),
            BoostLossesPercentage = boostExpected > 0 ? ((1 - boostAva / boostExpected) * boostUtilisation).Round(4) : 0,
            BoostExpMin = boostExpMin,
            BoostExpMax = boostExpMax,
            BoostExpAvg = boostExpAvg,
            NotCurtailedCf = ConvertExtensions.CalculatePercentage(notCurtailedCf, boostCoverage, 4),
            RunningRestrictedCf = ConvertExtensions.CalculatePercentage(runningRestrictedCf, notCurtailedCf, 4),
            PitchCf = ConvertExtensions.CalculatePercentage(pitchCf, runningRestrictedCf, 4),
            DisabledCf = ConvertExtensions.CalculatePercentage(disabledCf, pitchCf, 4),
            AmbientTempCf = ConvertExtensions.CalculatePercentage(ambientTempCf, disabledCf, 4),
            GridVoltageCf = ConvertExtensions.CalculatePercentage(gridVoltageCf, ambientTempCf, 4),
            SafeModeCf = ConvertExtensions.CalculatePercentage(safeModeCf, gridVoltageCf, 4),
            PowerFactorCf = ConvertExtensions.CalculatePercentage(powerFactorCf, safeModeCf, 4),
            CoolDownCf = ConvertExtensions.CalculatePercentage(coolDownCf, powerFactorCf, 4),
            TurbulenceCf = ConvertExtensions.CalculatePercentage(turbulenceCf, coolDownCf, 4),
            AcsCf = ConvertExtensions.CalculatePercentage(acsCf, turbulenceCf, 4),
            SensorErrorCf = ConvertExtensions.CalculatePercentage(sensorErrorCf, acsCf, 4),
            RsaCf = ConvertExtensions.CalculatePercentage(rsaCf, sensorErrorCf, 4),
            NotCurtailedCnf = ConvertExtensions.CalculatePercentage(notCurtailedCnf, boostCoverage, 4),
            RunningRestrictedCnf = ConvertExtensions.CalculatePercentage(runningRestrictedCnf, boostCoverage, 4),
            PitchCnf = ConvertExtensions.CalculatePercentage(pitchCnf, boostCoverage, 4),
            DisabledCnf = ConvertExtensions.CalculatePercentage(disabledCnf, boostCoverage, 4),
            SafeModeCnf = ConvertExtensions.CalculatePercentage(safeModeCnf, boostCoverage, 4),
            AmbientTempCnf = ConvertExtensions.CalculatePercentage(ambientTempCnf, boostCoverage, 4),
            GridVoltageCnf = ConvertExtensions.CalculatePercentage(gridVoltageCnf, boostCoverage, 4),
            PowerFactorCnf = ConvertExtensions.CalculatePercentage(powerFactorCnf, boostCoverage, 4),
            CoolDownCnf = ConvertExtensions.CalculatePercentage(coolDownCnf, boostCoverage, 4),
            TurbulenceCnf = ConvertExtensions.CalculatePercentage(turbulenceCnf, boostCoverage, 4),
            AcsCnf = ConvertExtensions.CalculatePercentage(acsCnf, boostCoverage, 4),
            SensorErrorCnf = ConvertExtensions.CalculatePercentage(sensorErrorCnf, boostCoverage, 4),
            RsaCnf = ConvertExtensions.CalculatePercentage(rsaCnf, boostCoverage, 4)
        };

        return total;
    }
}