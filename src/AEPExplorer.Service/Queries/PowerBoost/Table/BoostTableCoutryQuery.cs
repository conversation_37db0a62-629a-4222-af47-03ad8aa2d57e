using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.PowerBoost.Table;

public class BoostTableCountryQuery : MediatR.IRequest<PowerBoostTableTotalModelResponse<BoostTableCountryModel>>
{
    public Query Query { get; init; }
}

public class BoostTableCountryQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<BoostTableCountryQuery, PowerBoostTableTotalModelResponse<BoostTableCountryModel>>
{
    public async Task<PowerBoostTableTotalModelResponse<BoostTableCountryModel>> Handle(BoostTableCountryQuery request, CancellationToken cancellationToken)
    {
        var data = new List<BoostTableCountryModel>();

        var fieldPropertyNames = new List<string>
        {
            nameof(PowerBoostReadData.BoostCoverage),
            nameof(PowerBoostReadData.BoostEnergyCnt),
            nameof(PowerBoostReadData.BoostEnergyEst),
            nameof(PowerBoostReadData.BoostAva),
            nameof(PowerBoostReadData.BoostAct),
            nameof(PowerBoostReadData.BoostExpected),
            nameof(PowerBoostReadData.NotCurtailed_CF),
            nameof(PowerBoostReadData.RunningRestricted_CF),
            nameof(PowerBoostReadData.Pitch_CF),
            nameof(PowerBoostReadData.Disabled_CF),
            nameof(PowerBoostReadData.SafeMode_CF),
            nameof(PowerBoostReadData.AmbientTemp_CF),
            nameof(PowerBoostReadData.GridVoltage_CF),
            nameof(PowerBoostReadData.PowerFactor_CF),
            nameof(PowerBoostReadData.Turbulence_CF),
            nameof(PowerBoostReadData.ACS_CF),
            nameof(PowerBoostReadData.SensorError_CF),
            nameof(PowerBoostReadData.RSA_CF),
            nameof(PowerBoostReadData.BoostCriteriaOK),
            nameof(PowerBoostReadData.BoostExpectedCriteriaOK),
            nameof(PowerBoostReadData.CoolDown_CF),
            nameof(PowerBoostReadData.PowerFactor_CNF),
            nameof(PowerBoostReadData.CoolDown_CNF),
            nameof(PowerBoostReadData.Turbulence_CNF),
            nameof(PowerBoostReadData.ACS_CNF),
            nameof(PowerBoostReadData.SensorError_CNF),
            nameof(PowerBoostReadData.RSA_CNF),
            nameof(PowerBoostReadData.RunningRestricted_CNF),
            nameof(PowerBoostReadData.Pitch_CNF),
            nameof(PowerBoostReadData.Disabled_CNF),
            nameof(PowerBoostReadData.SafeMode_CNF),
            nameof(PowerBoostReadData.AmbientTemp_CNF),
            nameof(PowerBoostReadData.GridVoltage_CNF),
            nameof(PowerBoostReadData.NotCurtailed_CNF)
        };

        var aggregations = fieldPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });
        aggregations.Add(nameof(PowerBoostReadData.BoostExpMin), new AggregationContainer
        {
            Min = new MinAggregation(nameof(PowerBoostReadData.BoostExpMin), nameof(PowerBoostReadData.BoostExpMin).ToLowerFirstChar())
        });
        aggregations.Add(nameof(PowerBoostReadData.BoostExpMax), new AggregationContainer
        {
            Max = new MaxAggregation(nameof(PowerBoostReadData.BoostExpMax), nameof(PowerBoostReadData.BoostExpMax).ToLowerFirstChar())
        });
        aggregations.Add(nameof(PowerBoostReadData.BoostExpAvg), new AggregationContainer
        {
            Average = new AverageAggregation(nameof(PowerBoostReadData.BoostExpAvg), nameof(PowerBoostReadData.BoostExpAvg).ToLowerFirstChar())
        });
        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE, Field = new Field(nameof(PowerBoostReadData.TurbineId).ToLowerFirstChar())
            }
        });

        var items = await ElasticsearchSumService.PowerBoostSumByGroupAsync(elasticClient, request.Query, nameof(PowerBoostReadData.CountryId), aggregations);

        var countries = await dbContext.Countries.Where(x => items.Keys.Contains(x.Id)).Select(x => new
        {
            x.Id,
            CountryName = x.Name,
            RegionName = x.Region.Name,
        }).ToListAsync(cancellationToken);
        
        foreach (var item in items)
        {
            var numberOfTurbines = (int)item.Value["NumberOfTurbines"];
            var num10Min = request.Query.TotalDays * 144 * numberOfTurbines;
            var boostCoverage = item.Value[nameof(PowerBoostReadData.BoostCoverage)];
            var boostEnergyCnt = item.Value[nameof(PowerBoostReadData.BoostEnergyCnt)];
            var boostEnergyEst = item.Value[nameof(PowerBoostReadData.BoostEnergyEst)];
            var boostAva = item.Value[nameof(PowerBoostReadData.BoostAva)];
            var boostAct = item.Value[nameof(PowerBoostReadData.BoostAct)];
            var boostExpected = item.Value[nameof(PowerBoostReadData.BoostExpected)];
            var notCurtailedCf = item.Value[nameof(PowerBoostReadData.NotCurtailed_CF)];
            var runningRestrictedCf = item.Value[nameof(PowerBoostReadData.RunningRestricted_CF)];
            var pitchCf = item.Value[nameof(PowerBoostReadData.Pitch_CF)];
            var disabledCf = item.Value[nameof(PowerBoostReadData.Disabled_CF)];
            var safeModeCf = item.Value[nameof(PowerBoostReadData.SafeMode_CF)];
            var ambientTempCf = item.Value[nameof(PowerBoostReadData.AmbientTemp_CF)];
            var gridVoltageCf = item.Value[nameof(PowerBoostReadData.GridVoltage_CF)];
            var powerFactorCf = item.Value[nameof(PowerBoostReadData.PowerFactor_CF)];
            var turbulenceCf = item.Value[nameof(PowerBoostReadData.Turbulence_CF)];
            var acsCf = item.Value[nameof(PowerBoostReadData.ACS_CF)];
            var sensorErrorCf = item.Value[nameof(PowerBoostReadData.SensorError_CF)];
            var rsaCf = item.Value[nameof(PowerBoostReadData.RSA_CF)];
            var boostCriteriaOk = item.Value[nameof(PowerBoostReadData.BoostCriteriaOK)];
            var boostExpectedCriteriaOk = item.Value[nameof(PowerBoostReadData.BoostExpectedCriteriaOK)];
            var boostExpMin = item.Value[nameof(PowerBoostReadData.BoostExpMin)];
            var boostExpMax = item.Value[nameof(PowerBoostReadData.BoostExpMax)];
            var boostExpAvg = item.Value[nameof(PowerBoostReadData.BoostExpAvg)];
            var boostUtilisation = ConvertExtensions.CalculatePercentage(boostAct, boostAva, 4, limited: true);
            var boostEfficiency = ConvertExtensions.CalculatePercentage(boostAva, boostExpected, 4);
            var coolDownCf = item.Value[nameof(PowerBoostReadData.CoolDown_CF)];
            var powerFactorCnf = item.Value[nameof(PowerBoostReadData.PowerFactor_CNF)];
            var coolDownCnf = item.Value[nameof(PowerBoostReadData.CoolDown_CNF)];
            var turbulenceCnf = item.Value[nameof(PowerBoostReadData.Turbulence_CNF)];
            var acsCnf = item.Value[nameof(PowerBoostReadData.ACS_CNF)];
            var sensorErrorCnf = item.Value[nameof(PowerBoostReadData.SensorError_CNF)];
            var rsaCnf = item.Value[nameof(PowerBoostReadData.RSA_CNF)];
            var runningRestrictedCnf = item.Value[nameof(PowerBoostReadData.RunningRestricted_CNF)];
            var pitchCnf = item.Value[nameof(PowerBoostReadData.Pitch_CNF)];
            var disabledCnf = item.Value[nameof(PowerBoostReadData.Disabled_CNF)];
            var safeModeCnf = item.Value[nameof(PowerBoostReadData.SafeMode_CNF)];
            var ambientTempCnf = item.Value[nameof(PowerBoostReadData.AmbientTemp_CNF)];
            var gridVoltageCnf = item.Value[nameof(PowerBoostReadData.GridVoltage_CNF)];
            var notCurtailedCnf = item.Value[nameof(PowerBoostReadData.NotCurtailed_CNF)];
            var country = countries.FirstOrDefault(x => x.Id == item.Key);

            data.Add(new BoostTableCountryModel
            {
                Id = item.Key.ToString(),
                Region = country != null ? country.RegionName : "Unknown",
                Country = country != null ? country.CountryName : "Unknown",
                StartTimeUtc = request.Query.DateFrom ?? DateTime.MinValue,
                EndTimeUtc = request.Query.DateTo ?? DateTime.MinValue,
                Num10Min = num10Min,
                BoostCoverage = ConvertExtensions.CalculatePercentage(boostCoverage, num10Min, 4),
                BoostEnergyCnt = boostEnergyCnt,
                BoostEnergyEst = boostEnergyEst,
                BoostAva = boostAva,
                BoostAct = boostAct,
                BoostExpectedTime = ConvertExtensions.CalculatePercentage(boostExpectedCriteriaOk, boostCoverage, 4),
                BoostExpected = boostExpected,
                BoostEfficiencyTime = ConvertExtensions.CalculatePercentage(boostCriteriaOk, boostExpectedCriteriaOk, 4),
                BoostEfficiency = boostEfficiency,
                BoostActualTime = ConvertExtensions.CalculatePercentage(boostCriteriaOk, boostCoverage, 4),
                BoostUtilisation = boostUtilisation,
                BoostLosses = ((boostExpected - boostAva) * boostUtilisation / 100).Round(4),
                BoostLossesPercentage = boostExpected > 0 ? ((1 - boostAva / boostExpected) * boostUtilisation).Round(4) : 0,
                BoostExpMin = boostExpMin,
                BoostExpMax = boostExpMax,
                BoostExpAvg = boostExpAvg,
                NotCurtailedCf = ConvertExtensions.CalculatePercentage(notCurtailedCf, boostCoverage, 4),
                RunningRestrictedCf = ConvertExtensions.CalculatePercentage(runningRestrictedCf, notCurtailedCf, 4),
                PitchCf = ConvertExtensions.CalculatePercentage(pitchCf, runningRestrictedCf, 4),
                DisabledCf = ConvertExtensions.CalculatePercentage(disabledCf, pitchCf, 4),
                AmbientTempCf = ConvertExtensions.CalculatePercentage(ambientTempCf, disabledCf, 4),
                GridVoltageCf = ConvertExtensions.CalculatePercentage(gridVoltageCf, ambientTempCf, 4),
                SafeModeCf = ConvertExtensions.CalculatePercentage(safeModeCf, gridVoltageCf, 4),
                PowerFactorCf = ConvertExtensions.CalculatePercentage(powerFactorCf, safeModeCf, 4),
                CoolDownCf = ConvertExtensions.CalculatePercentage(coolDownCf, powerFactorCf, 4),
                TurbulenceCf = ConvertExtensions.CalculatePercentage(turbulenceCf, coolDownCf, 4),
                AcsCf = ConvertExtensions.CalculatePercentage(acsCf, turbulenceCf, 4),
                SensorErrorCf = ConvertExtensions.CalculatePercentage(sensorErrorCf, acsCf, 4),
                RsaCf = ConvertExtensions.CalculatePercentage(rsaCf, sensorErrorCf, 4),
                NotCurtailedCnf = ConvertExtensions.CalculatePercentage(notCurtailedCnf, boostCoverage, 4),
                RunningRestrictedCnf = ConvertExtensions.CalculatePercentage(runningRestrictedCnf, boostCoverage, 4),
                PitchCnf = ConvertExtensions.CalculatePercentage(pitchCnf, boostCoverage, 4),
                DisabledCnf = ConvertExtensions.CalculatePercentage(disabledCnf, boostCoverage, 4),
                AmbientTempCnf = ConvertExtensions.CalculatePercentage(ambientTempCnf, boostCoverage, 4),
                GridVoltageCnf = ConvertExtensions.CalculatePercentage(gridVoltageCnf, boostCoverage, 4),
                SafeModeCnf = ConvertExtensions.CalculatePercentage(safeModeCnf, boostCoverage, 4),
                PowerFactorCnf = ConvertExtensions.CalculatePercentage(powerFactorCnf, boostCoverage, 4),
                CoolDownCnf = ConvertExtensions.CalculatePercentage(coolDownCnf, boostCoverage, 4),
                TurbulenceCnf = ConvertExtensions.CalculatePercentage(turbulenceCnf, boostCoverage, 4),
                AcsCnf = ConvertExtensions.CalculatePercentage(acsCnf, boostCoverage, 4),
                SensorErrorCnf = ConvertExtensions.CalculatePercentage(sensorErrorCnf, boostCoverage, 4),
                RsaCnf = ConvertExtensions.CalculatePercentage(rsaCnf, boostCoverage, 4)
            });
        }

        return new PowerBoostTableTotalModelResponse<BoostTableCountryModel>
        {
            Data = data.SortBy(request.Query.SortColumn, request.Query.SortDirection).Skip(ElasticsearchConstants.PAGE_SIZE * (request.Query.Page - 1)).Take(ElasticsearchConstants.PAGE_SIZE).ToList(),
            Total = BoostTableHelper.GetTotal(items, request.Query),
            TotalRows = items.Count,
            TotalPages = items.Count / ElasticsearchConstants.PAGE_SIZE + 1
        };
    }
}