using System.Dynamic;
using System.IO.Compression;
using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Service.Elasticsearch;
using AEPExplorer.Service.Queries.Table.Export;
using CsvHelper;
using CsvHelper.Configuration;

namespace AEPExplorer.Service.Queries.PowerBoost.Export;

public class PowerBoostCountryExportQuery : MediatR.IRequest<(string fileName, byte[] file)>
{
    public ExportQuery Query { get; init; }
}

public class PowerBoostCountryExportQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext, IExportFilter exportFilter)
    : IRequestHandler<PowerBoostCountryExportQuery, (string fileName, byte[] file)>
{
    public async Task<(string fileName, byte[] file)> Handle(PowerBoostCountryExportQuery request, CancellationToken cancellationToken)
    {
        var originalQuery = request.Query.DeepCopy();
        var fieldPropertyNames = new List<string>
        {
            nameof(PowerBoostReadData.BoostCoverage),
            nameof(PowerBoostReadData.BoostEnergyCnt),
            nameof(PowerBoostReadData.BoostEnergyEst),
            nameof(PowerBoostReadData.BoostAva),
            nameof(PowerBoostReadData.BoostAct),
            nameof(PowerBoostReadData.BoostExpected),
            nameof(PowerBoostReadData.NotCurtailed_CF),
            nameof(PowerBoostReadData.RunningRestricted_CF),
            nameof(PowerBoostReadData.Pitch_CF),
            nameof(PowerBoostReadData.Disabled_CF),
            nameof(PowerBoostReadData.SafeMode_CF),
            nameof(PowerBoostReadData.AmbientTemp_CF),
            nameof(PowerBoostReadData.GridVoltage_CF),
            nameof(PowerBoostReadData.PowerFactor_CF),
            nameof(PowerBoostReadData.Turbulence_CF),
            nameof(PowerBoostReadData.ACS_CF),
            nameof(PowerBoostReadData.SensorError_CF),
            nameof(PowerBoostReadData.RSA_CF),
            nameof(PowerBoostReadData.BoostCriteriaOK),
            nameof(PowerBoostReadData.BoostExpectedCriteriaOK),
            nameof(PowerBoostReadData.CoolDown_CF),
            nameof(PowerBoostReadData.PowerFactor_CNF),
            nameof(PowerBoostReadData.CoolDown_CNF),
            nameof(PowerBoostReadData.Turbulence_CNF),
            nameof(PowerBoostReadData.ACS_CNF),
            nameof(PowerBoostReadData.SensorError_CNF),
            nameof(PowerBoostReadData.RSA_CNF),
            nameof(PowerBoostReadData.RunningRestricted_CNF),
            nameof(PowerBoostReadData.Pitch_CNF),
            nameof(PowerBoostReadData.Disabled_CNF),
            nameof(PowerBoostReadData.SafeMode_CNF),
            nameof(PowerBoostReadData.AmbientTemp_CNF),
            nameof(PowerBoostReadData.GridVoltage_CNF),
            nameof(PowerBoostReadData.NotCurtailed_CNF)
        };

        var aggregations = fieldPropertyNames.ToDictionary<string, string, IAggregationContainer>(propertyName => propertyName, propertyName => new AggregationContainer
        {
            Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar())
        });
        aggregations.Add(nameof(PowerBoostReadData.BoostExpMin), new AggregationContainer
        {
            Min = new MinAggregation(nameof(PowerBoostReadData.BoostExpMin), nameof(PowerBoostReadData.BoostExpMin).ToLowerFirstChar())
        });
        aggregations.Add(nameof(PowerBoostReadData.BoostExpMax), new AggregationContainer
        {
            Max = new MaxAggregation(nameof(PowerBoostReadData.BoostExpMax), nameof(PowerBoostReadData.BoostExpMax).ToLowerFirstChar())
        });
        aggregations.Add(nameof(PowerBoostReadData.BoostExpAvg), new AggregationContainer
        {
            Average = new AverageAggregation(nameof(PowerBoostReadData.BoostExpAvg), nameof(PowerBoostReadData.BoostExpAvg).ToLowerFirstChar())
        });
        aggregations.Add("NumberOfTurbines", new AggregationContainer
        {
            Terms = new TermsAggregation("NumberOfTurbines")
            {
                Size = ElasticsearchConstants.BUCKET_SIZE, Field = new Field(nameof(PowerBoostReadData.TurbineId).ToLowerFirstChar())
            }
        });

        var items = await ElasticsearchSumService.PowerBoostSumByGroupAsync(elasticClient, request.Query, nameof(PowerBoostReadData.CountryId), aggregations);

        var countries = await dbContext.Countries.Where(x => items.Keys.Contains(x.Id)).Select(x => new
        {
            x.Id,
            CountryName = x.Name,
            RegionName = x.Region.Name,
        }).ToListAsync(cancellationToken);

        var records = new List<dynamic>();
        foreach (var item in items)
        {
            var numberOfTurbines = (int)item.Value["NumberOfTurbines"];
            var num10Min = request.Query.TotalDays * 144 * numberOfTurbines;
            var boostCoverage = item.Value[nameof(PowerBoostReadData.BoostCoverage)];
            var boostEnergyCnt = item.Value[nameof(PowerBoostReadData.BoostEnergyCnt)];
            var boostEnergyEst = item.Value[nameof(PowerBoostReadData.BoostEnergyEst)];
            var boostAva = item.Value[nameof(PowerBoostReadData.BoostAva)];
            var boostAct = item.Value[nameof(PowerBoostReadData.BoostAct)];
            var boostExpected = item.Value[nameof(PowerBoostReadData.BoostExpected)];
            var notCurtailedCf = item.Value[nameof(PowerBoostReadData.NotCurtailed_CF)];
            var runningRestrictedCf = item.Value[nameof(PowerBoostReadData.RunningRestricted_CF)];
            var pitchCf = item.Value[nameof(PowerBoostReadData.Pitch_CF)];
            var disabledCf = item.Value[nameof(PowerBoostReadData.Disabled_CF)];
            var safeModeCf = item.Value[nameof(PowerBoostReadData.SafeMode_CF)];
            var ambientTempCf = item.Value[nameof(PowerBoostReadData.AmbientTemp_CF)];
            var gridVoltageCf = item.Value[nameof(PowerBoostReadData.GridVoltage_CF)];
            var powerFactorCf = item.Value[nameof(PowerBoostReadData.PowerFactor_CF)];
            var turbulenceCf = item.Value[nameof(PowerBoostReadData.Turbulence_CF)];
            var acsCf = item.Value[nameof(PowerBoostReadData.ACS_CF)];
            var sensorErrorCf = item.Value[nameof(PowerBoostReadData.SensorError_CF)];
            var rsaCf = item.Value[nameof(PowerBoostReadData.RSA_CF)];
            var boostCriteriaOk = item.Value[nameof(PowerBoostReadData.BoostCriteriaOK)];
            var boostExpectedCriteriaOk = item.Value[nameof(PowerBoostReadData.BoostExpectedCriteriaOK)];
            var boostExpMin = item.Value[nameof(PowerBoostReadData.BoostExpMin)];
            var boostExpMax = item.Value[nameof(PowerBoostReadData.BoostExpMax)];
            var boostExpAvg = item.Value[nameof(PowerBoostReadData.BoostExpAvg)];
            var boostUtilisation = ConvertExtensions.CalculatePercentage(boostAct, boostAva, 4, limited: true);
            var boostEfficiency = ConvertExtensions.CalculatePercentage(boostAva, boostExpected, 4);
            var coolDownCf = item.Value[nameof(PowerBoostReadData.CoolDown_CF)];
            var powerFactorCnf = item.Value[nameof(PowerBoostReadData.PowerFactor_CNF)];
            var coolDownCnf = item.Value[nameof(PowerBoostReadData.CoolDown_CNF)];
            var turbulenceCnf = item.Value[nameof(PowerBoostReadData.Turbulence_CNF)];
            var acsCnf = item.Value[nameof(PowerBoostReadData.ACS_CNF)];
            var sensorErrorCnf = item.Value[nameof(PowerBoostReadData.SensorError_CNF)];
            var rsaCnf = item.Value[nameof(PowerBoostReadData.RSA_CNF)];
            var runningRestrictedCnf = item.Value[nameof(PowerBoostReadData.RunningRestricted_CNF)];
            var pitchCnf = item.Value[nameof(PowerBoostReadData.Pitch_CNF)];
            var disabledCnf = item.Value[nameof(PowerBoostReadData.Disabled_CNF)];
            var safeModeCnf = item.Value[nameof(PowerBoostReadData.SafeMode_CNF)];
            var ambientTempCnf = item.Value[nameof(PowerBoostReadData.AmbientTemp_CNF)];
            var gridVoltageCnf = item.Value[nameof(PowerBoostReadData.GridVoltage_CNF)];
            var notCurtailedCnf = item.Value[nameof(PowerBoostReadData.NotCurtailed_CNF)];
            var site = countries.FirstOrDefault(x => x.Id == item.Key);
            var obj = new ExpandoObject();
            var store = (IDictionary<string, object>)obj;
            store.Add("RegionName", site != null ? site.RegionName : "Unknown");
            store.Add("CountryName", site != null ? site.CountryName : "Unknown");
            store.Add("StartTimeUTC", request.Query.DateFrom);
            store.Add("EndTimeUTC", request.Query.DateTo);
            store.Add("Num10min", num10Min);
            store.Add("Boost Coverage [%]", ConvertExtensions.CalculatePercentage(boostCoverage, num10Min, 4));
            store.Add("Boost Energy Count [kWh]", boostEnergyCnt);
            store.Add("Boost Energy cumulated [kWh]", boostEnergyEst);
            store.Add("Avail Boost cumulated [kWh]", boostAva);
            store.Add("Actual Boost cumulated [kWh]", boostAct);
            store.Add("TimeExpectedBoost [%]", ConvertExtensions.CalculatePercentage(boostExpectedCriteriaOk, boostCoverage, 4));
            store.Add("Expected Boost cumulated [kWh]", boostExpected);
            store.Add("BoostEfficiencyTime [%]", ConvertExtensions.CalculatePercentage(boostCriteriaOk, boostExpectedCriteriaOk, 4));
            store.Add("BoostEfficiency [%]", boostEfficiency);
            store.Add("TimeBoosting [%]", ConvertExtensions.CalculatePercentage(boostCriteriaOk, boostCoverage).Round(4));
            store.Add("HPPP Utilisation [%]", boostUtilisation);
            store.Add("BoostLosses [kWh]", ((boostExpected - boostAva) * boostUtilisation / 100).Round(4));
            store.Add("BoostLosses [%]", (boostExpected > 0 ? (1 - boostAva / boostExpected) * boostUtilisation : 0).Round(4));
            store.Add("Expected Boost Min [kW]", boostExpMin);
            store.Add("Expected Boost Max [kW]", boostExpMax);
            store.Add("Expected Boost Avg [kW]", boostExpAvg);
            store.Add("NotCurtailed_CF [%]", ConvertExtensions.CalculatePercentage(notCurtailedCf, boostCoverage, 4));
            store.Add("RunningRestricted_CF [%]", ConvertExtensions.CalculatePercentage(runningRestrictedCf, notCurtailedCf, 4));
            store.Add("Pitch_CF [%]", ConvertExtensions.CalculatePercentage(pitchCf, runningRestrictedCf, 4));
            store.Add("Disabled_CF [%]", ConvertExtensions.CalculatePercentage(disabledCf, pitchCf, 4));
            store.Add("AmbientTemp_CF [%]", ConvertExtensions.CalculatePercentage(ambientTempCf, disabledCf, 4));
            store.Add("GridVoltage_CF [%]", ConvertExtensions.CalculatePercentage(gridVoltageCf, ambientTempCf, 4));
            store.Add("SafeMode_CF [%]", ConvertExtensions.CalculatePercentage(safeModeCf, gridVoltageCf, 4));
            store.Add("PowerFactor_CF [%]", ConvertExtensions.CalculatePercentage(powerFactorCf, safeModeCf, 4));
            store.Add("CoolDown_CF [%]", ConvertExtensions.CalculatePercentage(coolDownCf, powerFactorCf, 4));
            store.Add("Turbulence_CF [%]", ConvertExtensions.CalculatePercentage(turbulenceCf, coolDownCf, 4));
            store.Add("ACS_CF [%]", ConvertExtensions.CalculatePercentage(acsCf, turbulenceCf, 4));
            store.Add("SensorError_CF [%]", ConvertExtensions.CalculatePercentage(sensorErrorCf, acsCf, 4));
            store.Add("RSA_CF [%]", ConvertExtensions.CalculatePercentage(rsaCf, sensorErrorCf, 4));
            store.Add("NotCurtailed_CNF [%]", ConvertExtensions.CalculatePercentage(notCurtailedCnf, boostCoverage, 4));
            store.Add("RunningRestricted_CNF [%]", ConvertExtensions.CalculatePercentage(runningRestrictedCnf, boostCoverage, 4));
            store.Add("Pitch_CNF [%]", ConvertExtensions.CalculatePercentage(pitchCnf, boostCoverage, 4));
            store.Add("Disabled_CNF [%]", ConvertExtensions.CalculatePercentage(disabledCnf, boostCoverage, 4));
            store.Add("AmbientTemp_CNF [%]", ConvertExtensions.CalculatePercentage(ambientTempCnf, boostCoverage, 4));
            store.Add("GridVoltage_CNF [%]", ConvertExtensions.CalculatePercentage(gridVoltageCnf, boostCoverage, 4));
            store.Add("SafeMode_CNF [%]", ConvertExtensions.CalculatePercentage(safeModeCnf, boostCoverage, 4));
            store.Add("PowerFactor_CNF [%]", ConvertExtensions.CalculatePercentage(powerFactorCnf, boostCoverage, 4));
            store.Add("CoolDown_CNF [%]", ConvertExtensions.CalculatePercentage(coolDownCnf, boostCoverage, 4));
            store.Add("Turbulence_CNF [%]", ConvertExtensions.CalculatePercentage(turbulenceCnf, boostCoverage, 4));
            store.Add("ACS_CNF [%]", ConvertExtensions.CalculatePercentage(acsCnf, boostCoverage, 4));
            store.Add("SensorError_CNF [%]", ConvertExtensions.CalculatePercentage(sensorErrorCnf, boostCoverage, 4));
            store.Add("RSA_CNF [%]", ConvertExtensions.CalculatePercentage(rsaCnf, boostCoverage, 4));

            dynamic record = obj;
            records.Add(record);
        }

        var totalRecord = BoostTableExportHelper.GetTotal(items, request.Query, ["RegionName", "CountryName"]);
        records.Add(totalRecord);

        var invariantCulture = new CultureInfo(CultureInfo.InvariantCulture.LCID);
        invariantCulture.NumberFormat.NumberDecimalSeparator = request.Query.DecimalSeparator switch
        {
            DecimalSeparatorEnum.Comma => ",",
            DecimalSeparatorEnum.Dot => ".",
            _ => invariantCulture.NumberFormat.NumberDecimalSeparator
        };

        using var resultsMemoryStream = new MemoryStream();
        await using (var writer = new StreamWriter(resultsMemoryStream))
        await using (var csvWriter = new CsvWriter(writer, new CsvConfiguration(invariantCulture)
                     {
                         Delimiter = ConvertExtensions.GetDelimiter(request.Query.Delimiter)
                     }))
            await csvWriter.WriteRecordsAsync(records, cancellationToken);

        var filtersMemoryStream = await exportFilter.GetCsv(originalQuery, invariantCulture, cancellationToken);

        var timestamp = $"{DateTime.Now:yyyy-MM-dd_hh-mm-ss}";
        using var zipMemoryStream = new MemoryStream();
        using (var archive = new ZipArchive(zipMemoryStream, ZipArchiveMode.Create, false))
        {
            try
            {
                var entry = archive.CreateEntry($"results_{timestamp}.csv", CompressionLevel.Fastest);
                await using (var entryStream = entry.Open())
                {
                    await entryStream.WriteAsync(resultsMemoryStream.ToArray(), cancellationToken);
                }

                var filtersEntry = archive.CreateEntry($"filters_{timestamp}.csv", CompressionLevel.Fastest);
                await using (var filtersEntryStream = filtersEntry.Open())
                {
                    await filtersEntryStream.WriteAsync(filtersMemoryStream, cancellationToken);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        var fileName = $"aep_power_boost_{timestamp}.zip";
        return (fileName, zipMemoryStream.ToArray());
    }
}