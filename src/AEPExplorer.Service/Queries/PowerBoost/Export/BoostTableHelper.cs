using System.Dynamic;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.PowerBoost.Export;

public static class BoostTableExportHelper
{
    public static dynamic GetTotal(Dictionary<Guid, Dictionary<string, double>> items, Query query, List<string> fields)
    {
        var numberOfTurbines = items.Count;
        if (items.Values.First().ContainsKey("NumberOfTurbines"))
        {
            numberOfTurbines = (int)items.Values.Sum(x => x["NumberOfTurbines"]);
        }

        var num10Min = query.TotalDays * 144 * numberOfTurbines;
        var boostCoverage = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostCoverage)]);
        var boostEnergyCnt = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostEnergyCnt)]);
        var boostEnergyEst = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostEnergyEst)]);
        var boostAva = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostAva)]);
        var boostAct = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostAct)]);
        var boostExpected = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostExpected)]);
        var notCurtailedCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.NotCurtailed_CF)]);
        var runningRestrictedCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.RunningRestricted_CF)]);
        var pitchCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Pitch_CF)]);
        var disabledCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Disabled_CF)]);
        var safeModeCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.SafeMode_CF)]);
        var ambientTempCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.AmbientTemp_CF)]);
        var gridVoltageCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.GridVoltage_CF)]);
        var powerFactorCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.PowerFactor_CF)]);
        var turbulenceCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Turbulence_CF)]);
        var acsCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.ACS_CF)]);
        var sensorErrorCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.SensorError_CF)]);
        var rsaCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.RSA_CF)]);
        var boostCriteriaOk = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostCriteriaOK)]);
        var boostExpectedCriteriaOk = items.Values.Sum(x => x[nameof(PowerBoostReadData.BoostExpectedCriteriaOK)]);
        var boostExpMin = items.Values.Min(x => x[nameof(PowerBoostReadData.BoostExpMin)]);
        var boostExpMax = items.Values.Max(x => x[nameof(PowerBoostReadData.BoostExpMax)]);
        var boostExpAvg = items.Values.Average(x => x[nameof(PowerBoostReadData.BoostExpAvg)]);
        var boostUtilisation = ConvertExtensions.CalculatePercentage(boostAct, boostAva, decimals: 4, limited: true);
        var boostEfficiency = ConvertExtensions.CalculatePercentage(boostAva, boostExpected, 4);
        var coolDownCf = items.Values.Sum(x => x[nameof(PowerBoostReadData.CoolDown_CF)]);
        var powerFactorCnf =  items.Values.Sum(x => x[nameof(PowerBoostReadData.PowerFactor_CNF)]);
        var coolDownCnf =  items.Values.Sum(x => x[nameof(PowerBoostReadData.CoolDown_CNF)]);
        var turbulenceCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Turbulence_CNF)]);
        var acsCnf = items.Values.Sum(x=>x[nameof(PowerBoostReadData.ACS_CNF)]);
        var sensorErrorCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.SensorError_CNF)]);
        var rsaCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.RSA_CNF)]);
        var runningRestrictedCnf =  items.Values.Sum(x => x[nameof(PowerBoostReadData.RunningRestricted_CNF)]);
        var pitchCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Pitch_CNF)]);
        var disabledCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.Disabled_CNF)]);
        var safeModeCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.SafeMode_CNF)]);
        var ambientTempCnf =  items.Values.Sum(x => x[nameof(PowerBoostReadData.AmbientTemp_CNF)]);
        var gridVoltageCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.GridVoltage_CNF)]);
        var notCurtailedCnf = items.Values.Sum(x => x[nameof(PowerBoostReadData.NotCurtailed_CNF)]);

        var obj = new ExpandoObject();
        IDictionary<string, object> store = obj;

        foreach (var field in fields)
        {
            store.Add(field, fields.IndexOf(field) == fields.Count - 1 ? "Total" : "");
        }

        store.Add("StartTimeUTC", query.DateFrom);
        store.Add("EndTimeUTC", query.DateTo);
        store.Add("Num10min", num10Min);
        store.Add("Boost Coverage [%]", ConvertExtensions.CalculatePercentage(boostCoverage, num10Min, 4));
        store.Add("Boost Energy Count [kWh]", boostEnergyCnt);
        store.Add("Boost Energy cumulated [kWh]", boostEnergyEst);
        store.Add("Avail Boost cumulated [kWh]", boostAva);
        store.Add("Actual Boost cumulated [kWh]", boostAct);
        store.Add("TimeExpectedBoost [%]", ConvertExtensions.CalculatePercentage(boostExpectedCriteriaOk, boostCoverage, 4));
        store.Add("Expected Boost cumulated [kWh]", boostExpected);
        store.Add("BoostEfficiencyTime [%]", ConvertExtensions.CalculatePercentage(boostCriteriaOk, boostExpectedCriteriaOk, 4));
        store.Add("BoostEfficiency [%]", boostEfficiency);
        store.Add("TimeBoosting [%]", ConvertExtensions.CalculatePercentage(boostCriteriaOk, boostCoverage).Round(4));
        store.Add("HPPP Utilisation [%]", boostUtilisation);
        store.Add("BoostLosses [kWh]", ((boostExpected - boostAva) * boostUtilisation / 100).Round(4));
        store.Add("BoostLosses [%]", ((1 - boostAva / boostExpected) * boostUtilisation).Round(4));
        store.Add("Expected Boost Min [kW]", boostExpMin);
        store.Add("Expected Boost Max [kW]", boostExpMax);
        store.Add("Expected Boost Avg [kW]", boostExpAvg);
        store.Add("NotCurtailed_CF [%]", ConvertExtensions.CalculatePercentage(notCurtailedCf, boostCoverage, 4));
        store.Add("RunningRestricted_CF [%]", ConvertExtensions.CalculatePercentage(runningRestrictedCf, notCurtailedCf, 4));
        store.Add("Pitch_CF [%]", ConvertExtensions.CalculatePercentage(pitchCf, runningRestrictedCf, 4));
        store.Add("Disabled_CF [%]", ConvertExtensions.CalculatePercentage(disabledCf, pitchCf, 4));
        store.Add("AmbientTemp_CF [%]", ConvertExtensions.CalculatePercentage(ambientTempCf, disabledCf, 4));
        store.Add("GridVoltage_CF [%]", ConvertExtensions.CalculatePercentage(gridVoltageCf, ambientTempCf, 4));
        store.Add("SafeMode_CF [%]", ConvertExtensions.CalculatePercentage(safeModeCf, gridVoltageCf, 4));
        store.Add("PowerFactor_CF [%]", ConvertExtensions.CalculatePercentage(powerFactorCf, safeModeCf, 4));
        store.Add("CoolDown_CF [%]", ConvertExtensions.CalculatePercentage(coolDownCf, powerFactorCf, 4));
        store.Add("Turbulence_CF [%]", ConvertExtensions.CalculatePercentage(turbulenceCf, coolDownCf, 4));
        store.Add("ACS_CF [%]", ConvertExtensions.CalculatePercentage(acsCf, turbulenceCf, 4));
        store.Add("SensorError_CF [%]", ConvertExtensions.CalculatePercentage(sensorErrorCf, acsCf, 4));
        store.Add("RSA_CF [%]", ConvertExtensions.CalculatePercentage(rsaCf, sensorErrorCf, 4));
        store.Add("NotCurtailed_CNF [%]", ConvertExtensions.CalculatePercentage(notCurtailedCnf, boostCoverage, 4));
        store.Add("RunningRestricted_CNF [%]", ConvertExtensions.CalculatePercentage(runningRestrictedCnf, boostCoverage, 4));
        store.Add("Pitch_CNF [%]", ConvertExtensions.CalculatePercentage(pitchCnf, boostCoverage, 4));
        store.Add("Disabled_CNF [%]", ConvertExtensions.CalculatePercentage(disabledCnf, boostCoverage, 4));
        store.Add("AmbientTemp_CNF [%]", ConvertExtensions.CalculatePercentage(ambientTempCnf, boostCoverage, 4));
        store.Add("GridVoltage_CNF [%]", ConvertExtensions.CalculatePercentage(gridVoltageCnf, boostCoverage, 4));
        store.Add("SafeMode_CNF [%]", ConvertExtensions.CalculatePercentage(safeModeCnf, boostCoverage, 4));
        store.Add("PowerFactor_CNF [%]", ConvertExtensions.CalculatePercentage(powerFactorCnf, boostCoverage, 4));
        store.Add("CoolDown_CNF [%]", ConvertExtensions.CalculatePercentage(coolDownCnf, boostCoverage, 4));
        store.Add("Turbulence_CNF [%]", ConvertExtensions.CalculatePercentage(turbulenceCnf, boostCoverage, 4));
        store.Add("ACS_CNF [%]", ConvertExtensions.CalculatePercentage(acsCnf, boostCoverage, 4));
        store.Add("SensorError_CNF [%]", ConvertExtensions.CalculatePercentage(sensorErrorCnf, boostCoverage, 4));
        store.Add("RSA_CNF [%]", ConvertExtensions.CalculatePercentage(rsaCnf, boostCoverage, 4));

        dynamic record = obj;
        return record;
    }
}