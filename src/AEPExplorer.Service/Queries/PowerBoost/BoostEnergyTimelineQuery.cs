using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.PowerBoost;

public class BoostEnergyTimelineQuery : MediatR.IRequest<List<BoostEnergy>>
{
    public Query Query { get; init; }
}

public class BoostEnergyTimelineQueryHandler(IElasticClient elasticClient) : IRequestHandler<BoostEnergyTimelineQuery, List<BoostEnergy>>
{
    public async Task<List<BoostEnergy>> Handle(BoostEnergyTimelineQuery request, CancellationToken cancellationToken)
    {
        var sumPropertyNames = new List<string>
        {
            nameof(PowerBoostReadData.BoostAva),
            nameof(PowerBoostReadData.BoostAct),
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });

        var items = await ElasticsearchSumService.PowerBoostSumByDateHistogramAsync(elasticClient, request.Query, aggregations, DateInterval.Day);
        var boostAvailableTimeline = new List<ValueDate>();
        var boostActualTimeline = new List<ValueDate>();
        for (var dt = request.Query.DateFrom.Value; dt <= request.Query.DateTo.Value; dt = dt.AddDays(1))
        {
            items.TryGetValue(dt, out var item);
            if (item == null)
            {
                continue;
            }

            item.TryGetValue(nameof(PowerBoostReadData.BoostAva), out var boostAva);
            item.TryGetValue(nameof(PowerBoostReadData.BoostAct), out var boostAct);
            boostAvailableTimeline.Add(new ValueDate()
            {
                Date = dt,
                Value = boostAva
            });

            boostActualTimeline.Add(new ValueDate()
            {
                Date = dt,
                Value = boostAct
            });
        }

        var boostAvailable = new BoostEnergy
        {
            Name = "Boost Available",
            TimelineData = boostAvailableTimeline
        };

        var boostActual = new BoostEnergy
        {
            Name = "Boost Actual",
            TimelineData = boostActualTimeline
        };

        var result = new List<BoostEnergy> { boostActual, boostAvailable };
        return result;
    }
}