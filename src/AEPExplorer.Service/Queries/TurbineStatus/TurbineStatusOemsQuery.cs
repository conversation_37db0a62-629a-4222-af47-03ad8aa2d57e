using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusOemsQuery : MediatR.IRequest<List<string>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusOemsQueryHandler(AepExplorerDbContext dbContext) : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusOemsQuery, List<string>>
    {
        public async Task<List<string>> Handle(TurbineStatusOemsQuery request, CancellationToken cancellationToken)
        {
            return await GetDistinctValuesAsync(
                dbContext,
                request.Filter,
                t => t.TurbineOem,
                cancellationToken);
        }
    }
}
