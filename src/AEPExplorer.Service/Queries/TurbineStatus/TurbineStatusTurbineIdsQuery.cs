using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusTurbineIdsQuery : MediatR.IRequest<List<int>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusTurbineIdsQueryHandler(AepExplorerDbContext dbContext) : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusTurbineIdsQuery, List<int>>
    {
        public async Task<List<int>> Handle(TurbineStatusTurbineIdsQuery request, CancellationToken cancellationToken)
        {
            return await GetDistinctValuesAsync(
                dbContext,
                request.Filter,
                t => t.TurbineId,
                cancellationToken);
        }
    }
}
