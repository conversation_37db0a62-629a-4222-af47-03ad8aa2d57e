using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public abstract class BaseTurbineStatusFilterQuery
    {
        private IQueryable<Model.Domain.TurbineStatus> ApplyFilters(
            IQueryable<Model.Domain.TurbineStatus> query, 
            TurbineStatusFilterRequest filter)
        {
            if (filter == null)
                return query;

            if (filter.RegionName.Any())
            {
                query = query.Where(t => filter.RegionName.Contains(t.RegionName));
            }

            if (filter.RegionShortName.Any())
            {
                query = query.Where(t => filter.RegionShortName.Contains(t.RegionShortName));
            }

            if (filter.CountryName.Any())
            {
                query = query.Where(t => filter.CountryName.Contains(t.CountryName));
            }

            if (filter.LocationTypeName.Any())
            {
                query = query.Where(t => filter.LocationTypeName.Contains(t.LocationTypeName));
            }

            if (filter.ProjectParkName.Any())
            {
                query = query.Where(t => filter.ProjectParkName.Contains(t.ProjectParkName));
            }

            if (filter.ScadaParkName.Any())
            {
                query = query.Where(t => filter.ScadaParkName.Contains(t.ScadaParkName));
            }

            if (filter.ProjectParkId.Any())
            {
                query = query.Where(t => filter.ProjectParkId.Contains(t.ProjectParkId));
            }

            if (filter.TurbineName.Any())
            {
                query = query.Where(t => filter.TurbineName.Contains(t.TurbineName));
            }

            if (filter.TurbineId.Any())
            {
                query = query.Where(t => filter.TurbineId.Contains(t.TurbineId));
            }

            if (filter.TurbineOem.Any())
            {
                query = query.Where(t => filter.TurbineOem.Contains(t.TurbineOem));
            }

            if (filter.TurbinePlatform.Any())
            {
                query = query.Where(t => filter.TurbinePlatform.Contains(t.TurbinePlatform));
            }

            if (filter.TurbineModel.Any())
            {
                query = query.Where(t => filter.TurbineModel.Contains(t.TurbineModel));
            }

            if (filter.IsTurbinePresent.HasValue)
            {
                query = query.Where(t => t.IsTurbinePresent == filter.IsTurbinePresent.Value);
            }

            if (filter.TurbineMissingInfo.Any())
            {
                query = query.Where(t => filter.TurbineMissingInfo.Contains(t.TurbineMissingInfo));
            }

            return query;
        }

        protected async Task<List<string>> GetDistinctValuesAsync(
            AepExplorerDbContext dbContext, 
            TurbineStatusFilterRequest filter,
            Expression<Func<Model.Domain.TurbineStatus, string>> selector,
            CancellationToken cancellationToken)
        {
            var query = dbContext.TurbineStatus.AsQueryable();
            query = ApplyFilters(query, filter);

            return await query
                .Select(selector)
                .Where(value => value != null)
                .Distinct()
                .OrderBy(x => x)
                .ToListAsync(cancellationToken);
        }
        
        protected async Task<List<bool>> GetDistinctValuesAsync(
            AepExplorerDbContext dbContext, 
            TurbineStatusFilterRequest filter,
            Expression<Func<Model.Domain.TurbineStatus, bool>> selector,
            CancellationToken cancellationToken)
        {
            var query = dbContext.TurbineStatus.AsQueryable();
            query = ApplyFilters(query, filter);

            return await query
                .Select(selector)
                .Distinct()
                .OrderBy(x => x)
                .ToListAsync(cancellationToken);
        }
        
        protected async Task<List<int>> GetDistinctValuesAsync(
            AepExplorerDbContext dbContext, 
            TurbineStatusFilterRequest filter,
            Expression<Func<Model.Domain.TurbineStatus, int>> selector,
            CancellationToken cancellationToken)
        {
            var query = dbContext.TurbineStatus.AsQueryable();
            query = ApplyFilters(query, filter);

            return await query
                .Select(selector)
                .Distinct()
                .OrderBy(x => x)
                .ToListAsync(cancellationToken);
        }
    }
}
