using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusProjectParkNamesQuery : MediatR.IRequest<List<string>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusProjectParkNamesQueryHandler(AepExplorerDbContext dbContext)
        : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusProjectParkNamesQuery, List<string>>
    {
        public async Task<List<string>> Handle(TurbineStatusProjectParkNamesQuery request, CancellationToken cancellationToken)
        {
            return await GetDistinctValuesAsync(
                dbContext,
                request.Filter,
                t => t.ProjectParkName,
                cancellationToken);
        }
    }
}
