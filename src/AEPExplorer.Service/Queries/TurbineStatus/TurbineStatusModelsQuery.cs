using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusModelsQuery : MediatR.IRequest<List<string>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusModelsQueryHandler(AepExplorerDbContext dbContext) : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusModelsQuery, List<string>>
    {
        public async Task<List<string>> Handle(TurbineStatusModelsQuery request, CancellationToken cancellationToken)
        {
            return await GetDistinctValuesAsync(
                dbContext,
                request.Filter,
                t => t.TurbineModel,
                cancellationToken);
        }
    }
}
