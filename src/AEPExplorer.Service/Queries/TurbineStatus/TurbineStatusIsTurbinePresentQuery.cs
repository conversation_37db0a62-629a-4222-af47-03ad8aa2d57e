using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusIsTurbinePresentsQuery : MediatR.IRequest<List<bool>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusIsTurbinePresentsQueryHandler(AepExplorerDbContext dbContext) : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusIsTurbinePresentsQuery, List<bool>>
    {
        public async Task<List<bool>> Handle(TurbineStatusIsTurbinePresentsQuery request, CancellationToken cancellationToken)
        {
            return await GetDistinctValuesAsync(
                dbContext,
                request.Filter,
                t => t.IsTurbinePresent,
                cancellationToken);
        }
    }
}
