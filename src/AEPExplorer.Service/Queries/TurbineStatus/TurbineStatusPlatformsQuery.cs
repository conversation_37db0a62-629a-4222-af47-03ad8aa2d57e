using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusPlatformsQuery : MediatR.IRequest<List<string>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusPlatformsQueryHandler(AepExplorerDbContext dbContext) : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusPlatformsQuery, List<string>>
    {
        public async Task<List<string>> Handle(TurbineStatusPlatformsQuery request, CancellationToken cancellationToken)
        {
            return await GetDistinctValuesAsync(
                dbContext,
                request.Filter,
                t => t.TurbinePlatform,
                cancellationToken);
        }
    }
}
