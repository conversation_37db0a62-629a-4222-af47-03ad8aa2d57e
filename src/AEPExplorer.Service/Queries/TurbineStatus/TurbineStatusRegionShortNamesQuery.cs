using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusRegionShortNamesQuery : MediatR.IRequest<List<string>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusRegionShortNamesQueryHandler(AepExplorerDbContext dbContext)
        : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusRegionShortNamesQuery, List<string>>
    {
        public async Task<List<string>> Handle(TurbineStatusRegionShortNamesQuery request, CancellationToken cancellationToken)
        {
            return await GetDistinctValuesAsync(
                dbContext,
                request.Filter,
                t => t.RegionShortName,
                cancellationToken);
        }
    }
}
