using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusRegionsQuery : MediatR.IRequest<List<string>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusRegionsQueryHandler(AepExplorerDbContext dbContext) : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusRegionsQuery, List<string>>
    {
        public async Task<List<string>> Handle(TurbineStatusRegionsQuery request, CancellationToken cancellationToken)
        {
            return await GetDistinctValuesAsync(
                dbContext,
                request.Filter,
                t => t.RegionName,
                cancellationToken);
        }
    }
}
