using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusLocationTypeNamesQuery : MediatR.IRequest<List<string>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusLocationTypeNamesQueryHandler(AepExplorerDbContext dbContext)
        : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusLocationTypeNamesQuery, List<string>>
    {
        public async Task<List<string>> Handle(TurbineStatusLocationTypeNamesQuery request, CancellationToken cancellationToken)
        {
            return await GetDistinctValuesAsync(
                dbContext,
                request.Filter,
                t => t.LocationTypeName,
                cancellationToken);
        }
    }
}
