using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusQuery : MediatR.IRequest<TableTotalModelResponse<Model.Domain.TurbineStatus>>
    {
        public TurbineStatusFilterRequest Query { get; init; }
    }

    public class TurbineStatusQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<TurbineStatusQuery, TableTotalModelResponse<Model.Domain.TurbineStatus>>
    {
        public async Task<TableTotalModelResponse<Model.Domain.TurbineStatus>> Handle(TurbineStatusQuery request, CancellationToken cancellationToken)
        {
            var query = dbContext.TurbineStatus.AsQueryable();

            if (request.Query != null)
            {
                if (request.Query.RegionName.Any())
                {
                    query = query.Where(t => request.Query.RegionName.Contains(t.RegionName));
                }

                if (request.Query.RegionShortName.Any())
                {
                    query = query.Where(t => request.Query.RegionShortName.Contains(t.RegionShortName));
                }

                if (request.Query.CountryName.Any())
                {
                    query = query.Where(t => request.Query.CountryName.Contains(t.CountryName));
                }

                if (request.Query.LocationTypeName.Any())
                {
                    query = query.Where(t => request.Query.LocationTypeName.Contains(t.LocationTypeName));
                }

                if (request.Query.ProjectParkName.Any())
                {
                    query = query.Where(t => request.Query.ProjectParkName.Contains(t.ProjectParkName));
                }

                if (request.Query.ScadaParkName.Any())
                {
                    query = query.Where(t => request.Query.ScadaParkName.Contains(t.ScadaParkName));
                }

                if (request.Query.ProjectParkId.Any())
                {
                    query = query.Where(t => request.Query.ProjectParkId.Contains(t.ProjectParkId));
                }

                if (request.Query.TurbineName.Any())
                {
                    query = query.Where(t => request.Query.TurbineName.Contains(t.TurbineName));
                }

                if (request.Query.TurbineId.Any())
                {
                    query = query.Where(t => request.Query.TurbineId.Contains(t.TurbineId));
                }

                if (request.Query.TurbineOem.Any())
                {
                    query = query.Where(t => request.Query.TurbineOem.Contains(t.TurbineOem));
                }

                if (request.Query.TurbinePlatform.Any())
                {
                    query = query.Where(t => request.Query.TurbinePlatform.Contains(t.TurbinePlatform));
                }

                if (request.Query.TurbineModel.Any())
                {
                    query = query.Where(t => request.Query.TurbineModel.Contains(t.TurbineModel));
                }

                if (request.Query.IsTurbinePresent.HasValue)
                {
                    query = query.Where(t => t.IsTurbinePresent == request.Query.IsTurbinePresent.Value);
                }

                if (request.Query.TurbineMissingInfo.Any())
                {
                    query = query.Where(t => request.Query.TurbineMissingInfo.Contains(t.TurbineMissingInfo));
                }
            }

            var data = await query.ToListAsync(cancellationToken);
            
            return new TableTotalModelResponse<Model.Domain.TurbineStatus>         {
                Data = data.SortBy(request.Query?.SortColumn, request.Query?.SortDirection).Skip(ElasticsearchConstants.PAGE_SIZE * ((request.Query?.Page ?? 1) - 1)).Take(ElasticsearchConstants.PAGE_SIZE).ToList(),
                TotalRows = data.Count,
                TotalPages = data.Count / ElasticsearchConstants.PAGE_SIZE + 1
            };
        }
    }
}
