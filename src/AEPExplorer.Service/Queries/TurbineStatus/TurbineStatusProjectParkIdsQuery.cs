using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service.Queries.TurbineStatus
{
    public class TurbineStatusProjectParkIdsQuery : MediatR.IRequest<List<string>>
    {
        public TurbineStatusFilterRequest Filter { get; init; }
    }

    public class TurbineStatusProjectParkIdsQueryHandler(AepExplorerDbContext dbContext) : BaseTurbineStatusFilterQuery, IRequestHandler<TurbineStatusProjectParkIdsQuery, List<string>>
    {
        public async Task<List<string>> Handle(TurbineStatusProjectParkIdsQuery request, CancellationToken cancellationToken)
        {
                return await GetDistinctValuesAsync(
                    dbContext,
                    request.Filter,
                    t => t.ProjectParkId,
                    cancellationToken);
        }
    }
}
