using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Coverage;

public class PowerBoostCoverageQuery : MediatR.IRequest<PowerBoostCoverageModel>
{
    public Query Query { get; init; }
}

public class PowerBoostCoverageQueryHandler(IElasticClient elasticClient) : IRequestHandler<PowerBoostCoverageQuery, PowerBoostCoverageModel>
{
    public async Task<PowerBoostCoverageModel> Handle(PowerBoostCoverageQuery request, CancellationToken cancellationToken)
    {
        var coverageQuery = request.Query.DeepCopy();
        coverageQuery.Category = [];
        var totalDataCoverage = await ElasticsearchSumService.PowerBoostSumAsync(elasticClient, coverageQuery, p => p.Field(x => x.BoostCoverage));
        var turbines = await ElasticsearchGeneralService.GetPowerBoostDistinctTurbinesAsync(elasticClient, coverageQuery);
        var dataCoverage = ConvertExtensions.CalculatePercentage(totalDataCoverage / 144, coverageQuery.TotalDays * turbines.Count);

        var coverageResult = new PowerBoostCoverageModel
        {
            DataCoverage = dataCoverage,
            TurbinesCount = turbines.Count
        };

        return coverageResult;
    }
}