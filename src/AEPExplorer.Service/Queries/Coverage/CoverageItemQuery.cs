using AEPExplorer.Data.EF;
using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Coverage;

public class CoverageItemQuery : MediatR.IRequest<List<CoverageItemModel>>
{
    public Query Query { get; init; }
}

public class CoverageItemQueryHandler(IElasticClient elasticClient, AepExplorerDbContext dbContext) : IRequestHandler<CoverageItemQuery, List<CoverageItemModel>>
{
    public async Task<List<CoverageItemModel>> Handle(CoverageItemQuery request, CancellationToken cancellationToken)
    {
        var result = new List<CoverageItemModel>();

        var (filterBy, groupProperty) = FilterHelper.GetFilterGroupProperty(request.Query);

        if (filterBy == null)
        {
            return result;
        }

        var sumPropertyNames = new List<string>
        {
            nameof(AepReadData.ActualEnergy),
            nameof(AepReadData.EnergyPotential),
            nameof(AepReadData.DataCoverage)
        };

        var aggregations = sumPropertyNames.ToDictionary<string, string, IAggregationContainer>(
            propertyName => propertyName,
            propertyName => new AggregationContainer { Sum = new SumAggregation(propertyName, propertyName.ToLowerFirstChar()) });
        var coverageQuery = request.Query.DeepCopy();
        coverageQuery.Category = [];
        
        var items = await ElasticsearchSumService.SumByGroupAsync(elasticClient, coverageQuery, groupProperty, aggregations);
        var itemPropertyIds = items.Select(x => x.Key).ToList();
        var bars = groupProperty switch
        {
            nameof(AepReadData.CustomerId) => await dbContext.Customers.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                NumberOfTurbines = x.Turbines.Count,
                NominalPower = x.Turbines.Sum(t=>t.NominalPower)
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.RegionId) => await dbContext.Regions.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                NumberOfTurbines = x.Countries.SelectMany(s=>s.Sites.SelectMany(t=>t.Turbines)).Count(),
                NominalPower = x.Countries.SelectMany(s=>s.Sites.SelectMany(t=>t.Turbines)).Sum(n=>n.NominalPower),
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.CountryId) => await dbContext.Countries.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                NumberOfTurbines = x.Sites.SelectMany(t=>t.Turbines).Count(),
                NominalPower = x.Sites.SelectMany(t=>t.Turbines).Sum(n=>n.NominalPower),
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.SiteId) => await dbContext.Sites.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                NumberOfTurbines = x.Turbines.Count,
                NominalPower = x.Turbines.Sum(t=>t.NominalPower)
            }).ToListAsync(cancellationToken: cancellationToken),
            nameof(AepReadData.TurbineId) => await dbContext.Turbines.Where(x => itemPropertyIds.Contains(x.Id)).Select(x => new
            {
                x.Id,
                x.Name,
                NumberOfTurbines = 1,
                x.NominalPower
            }).ToListAsync(cancellationToken: cancellationToken),
            _ => throw new ArgumentOutOfRangeException()
        };

        foreach (var item in items)
        {
            var bar = bars.Single(x => x.Id == item.Key);
            var capacityFactorActual = bar.NominalPower > 0 ? item.Value[nameof(AepReadData.ActualEnergy)] / coverageQuery.TotalDays/ 24 / bar.NominalPower : 0.0;
            var capacityFactorPotential = bar.NominalPower > 0 ? item.Value[nameof(AepReadData.EnergyPotential)] / coverageQuery.TotalDays / 24 / bar.NominalPower : 0.0;
            
            result.Add(new CoverageItemModel
            {
                Id = item.Key,
                DataCoverage = (item.Value[nameof(AepReadData.DataCoverage)] / (coverageQuery.TotalDays * bar.NumberOfTurbines)).ToPercentage(),
                CapacityFactorActual = capacityFactorActual.Round(2),
                CapacityFactorPotential = capacityFactorPotential.Round(2)
            });
        }

        return result;
    }
}