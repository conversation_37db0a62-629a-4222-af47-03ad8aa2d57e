using AEPExplorer.Model;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Elasticsearch;

namespace AEPExplorer.Service.Queries.Coverage;

public class CoverageQuery : MediatR.IRequest<CoverageModel>
{
    public Query Query { get; init; }
}

public class CoverageQueryHandler(IElasticClient elasticClient) : IRequestHandler<CoverageQuery, CoverageModel>
{
    public async Task<CoverageModel> Handle(CoverageQuery request, CancellationToken cancellationToken)
    {
        var aggregations = new Dictionary<string, IAggregationContainer>
        {
            {
                nameof(AepReadData.DataCoverage), new AggregationContainer
                {
                    Sum = new SumAggregation(nameof(AepReadData.DataCoverage), nameof(AepReadData.DataCoverage).ToLowerFirstChar())
                }
            },
            {
                nameof(AepReadData.DurationInHours), new AggregationContainer
                {
                    Sum = new SumAggregation(nameof(AepReadData.DurationInHours), nameof(AepReadData.DurationInHours).ToLowerFirstChar())
                }
            }
        };

        request.Query.Category = [];

        var aggregationResult = await ElasticsearchSumService.SumAsync(elasticClient, request.Query, aggregations, lossesType: LossesTypeEnum.TotalLosses);
        var totalDataCoverage = aggregationResult[nameof(AepReadData.DataCoverage)];
        var duration = aggregationResult[nameof(AepReadData.DurationInHours)];

        var turbines = await ElasticsearchGeneralService.GetDistinctValuesAsync(elasticClient, x => x.TurbineId, request.Query);
        var dataCoverage = ConvertExtensions.CalculatePercentage(totalDataCoverage, request.Query.TotalDays * turbines.Count);
        var actualEnergy = ConvertExtensions.CalculatePercentage(duration, request.Query.TotalDays * 24 * turbines.Count);
        var coverageResult = new CoverageModel
        {
            DataCoverage = dataCoverage,
            ActualEnergy = actualEnergy,
            PotentialEnergy = ConvertExtensions.CalculatePercentage(dataCoverage, actualEnergy),
            TurbinesCount = turbines.Count
        };

        return coverageResult;
    }
}