using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using Apache.Arrow;
using Apache.Arrow.Ipc;
using Microsoft.Azure.Databricks.Client;
using Microsoft.Azure.Databricks.Client.Models;
using Microsoft.Extensions.Configuration;

namespace AEPExplorer.Service.Queries.Epc;

public class FullPerformancePowerQuery : MediatR.IRequest<List<FullPerformancePowerData>>
{
    public Query Query { get; init; }
}

public class FullPerformancePowerQueryHandler(AepExplorerDbContext dbContext, IConfiguration configuration, DatabricksClient databricksClient): IRequestHandler<FullPerformancePowerQuery, List<FullPerformancePowerData>>
{
    public async Task<List<FullPerformancePowerData>> Handle(FullPerformancePowerQuery request, CancellationToken cancellationToken)
    {
        var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                ?? throw new InvalidOperationException("Databricks Catalog is missing");
        var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                          ?? throw new InvalidOperationException("Databricks WarehouseId is missing");

        var result = new List<EpcDot>();

        var turbineStructure = await dbContext.Turbines.Where(x => request.Query.TurbineId.Contains(x.Id)).ToListAsync(cancellationToken);
        var turbinesJoin = string.Join(',', turbineStructure.Select(x => x.MasterDataId));

        var sqlStatement = new SqlStatement
        {
            WarehouseId = warehouseId,
            Statement = $"""
                         SELECT
                             afp.MeasuringTime_UTC as UTC,
                             afp.TurbineId as TurbineId,
                             afp.WindSpeedNormDens as WindSpeedNormDens,
                             afp.PowerMean as PowerMean
                         FROM aep_result.aep_full_performance afp
                         WHERE TurbineId IN ({turbinesJoin})
                             AND MeasuringTime_UTC BETWEEN '{request.Query.DateFrom:yyyy-MM-dd}' AND '{request.Query.DateTo:yyyy-MM-dd}'
                         LIMIT {turbineStructure.Count * 50000}
                         """,
            Catalog = databricksCatalog,
            WaitTimeout = "50s",
            Format = StatementFormat.ARROW_STREAM,
            Disposition = SqlStatementDisposition.EXTERNAL_LINKS
        };

        var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement, cancellationToken);

        while (statement.Status.State == StatementExecutionState.PENDING || statement.Status.State == StatementExecutionState.RUNNING)
        {
            await Task.Delay(1000, cancellationToken);
            statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId, cancellationToken);
        }

        if (statement.Status.State != StatementExecutionState.SUCCEEDED)
        {
            throw new Exception($"Query failed: {statement.Status.Error}");
        }

        if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
        {
            return new List<FullPerformancePowerData>();
        }

        using var httpClient = new HttpClient();
        foreach (var link in statement.Result.ExternalLinks)
        {
            var response = await httpClient.GetAsync(link.ExternalLink, cancellationToken);
            if (!response.IsSuccessStatusCode)
            {
                continue;
            }

            await using var stream = await response.Content.ReadAsStreamAsync(cancellationToken);
            using var reader = new ArrowStreamReader(stream);

            while (await reader.ReadNextRecordBatchAsync(cancellationToken) is { } recordBatch)
            {
                var schema = recordBatch.Schema;
                for (var i = 0; i < recordBatch.Length; i++)
                {
                    var turbineId = turbineStructure.First(x => x.MasterDataId == ((Int32Array)recordBatch.Column(schema.GetFieldIndex("TurbineId"))).Values[i]).Id;
                    result.Add(new EpcDot
                    {
                        TurbineId = turbineId,
                        PowerMean = ((DoubleArray)recordBatch.Column(schema.GetFieldIndex("PowerMean"))).Values[i],
                        WindSpeedNormDens = ((DoubleArray)recordBatch.Column(schema.GetFieldIndex("WindSpeedNormDens"))).Values[i],
                        Utc = DateTime.SpecifyKind(((TimestampArray)recordBatch.Column(schema.GetFieldIndex("UTC"))).GetTimestamp(i).Value.DateTime, DateTimeKind.Utc)
                    });
                }
            }
        }

        var groupedData = result
            .GroupBy(data => data.TurbineId)
            .Select(group => new FullPerformancePowerData
            {
                TurbineId = group.Key,
                TurbineName = turbineStructure.FirstOrDefault(turbine => turbine.Id == group.Key)?.Name ?? "Unknown",
                Coordinates = group.Select(x => new WindSpeedPower { WindSpeed = x.WindSpeedNormDens, Power = x.PowerMean, Utc = x.Utc }).Distinct()
            }).ToList();

        return groupedData;
    }
}