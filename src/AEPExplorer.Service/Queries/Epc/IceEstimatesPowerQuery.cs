using AEPExplorer.Data.EF;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using Apache.Arrow;
using Apache.Arrow.Ipc;
using Microsoft.Azure.Databricks.Client;
using Microsoft.Azure.Databricks.Client.Models;
using Microsoft.Extensions.Configuration;

namespace AEPExplorer.Service.Queries.Epc;

public class IceEstimatesPowerQuery : MediatR.IRequest<List<IceEstimatesPowerData>>
{
    public Query Query { get; init; }
}

public class IceEstimatesPowerQueryHandler(AepExplorerDbContext dbContext, IConfiguration configuration, DatabricksClient databricksClient) : IRequestHandler<IceEstimatesPowerQuery, List<IceEstimatesPowerData>>
{
    public async Task<List<IceEstimatesPowerData>> Handle(IceEstimatesPowerQuery request, CancellationToken cancellationToken)
    {
        var databricksCatalog = configuration.GetSection("Databricks")["Catalog"]
                                ?? throw new InvalidOperationException("Databricks Catalog is missing");
        var warehouseId = configuration.GetSection("Databricks")["WarehouseId"]
                          ?? throw new InvalidOperationException("Databricks WarehouseId is missing");

        var result = new List<IceEstimatesPowerData>();
        var turbineStructure = await dbContext.Turbines.Where(x => request.Query.TurbineId.Contains(x.Id)).ToListAsync(cancellationToken);
        var turbinesJoin = string.Join(',', turbineStructure.Select(x => x.MasterDataId));

        var sqlStatement = new SqlStatement
        {
            WarehouseId = warehouseId,
            Statement = $"""
                         SELECT
                             aic.MeasuringTime_UTC as UTC
                             ,aic.TurbineId as TurbineId
                             ,CASE WHEN aic.IceCode = 999990 THEN 0
                                 WHEN aic.IceCode = 999991 THEN 1
                                 WHEN aic.IceCode = 999992 THEN 2
                                 WHEN aic.IceCode = 999993 THEN 3
                                 WHEN aic.IceCode = 999994 THEN 4
                              END as IceLabel
                             ,aic.WindSpeedNormDens as WindSpeedNormDens
                             ,aic.PowerMean as PowerMean
                         FROM aep_result.aep_ice_code aic
                         WHERE TurbineId IN ({turbinesJoin})
                             AND MeasuringTime_UTC BETWEEN '{request.Query.DateFrom:yyyy-MM-dd}' AND '{request.Query.DateTo:yyyy-MM-dd}'
                         LIMIT 50000;
                         """,
            Catalog = databricksCatalog,
            WaitTimeout = "50s",
            Format = StatementFormat.ARROW_STREAM,
            Disposition = SqlStatementDisposition.EXTERNAL_LINKS
        };

        var statement = await databricksClient.SQL.StatementExecution.Execute(sqlStatement, cancellationToken);

        while (statement.Status.State == StatementExecutionState.PENDING || statement.Status.State == StatementExecutionState.RUNNING)
        {
            await Task.Delay(1000, cancellationToken);
            statement = await databricksClient.SQL.StatementExecution.Get(statement.StatementId, cancellationToken);
        }

        if (statement.Status.State != StatementExecutionState.SUCCEEDED)
        {
            throw new Exception($"Query failed: {statement.Status.Error}");
        }

        if (statement.Result?.ExternalLinks == null || !statement.Result.ExternalLinks.Any())
        {
            return result;
        }

        using var httpClient = new HttpClient();
        foreach (var link in statement.Result.ExternalLinks)
        {
            var response = await httpClient.GetAsync(link.ExternalLink, cancellationToken);
            if (!response.IsSuccessStatusCode)
            {
                continue;
            }

            await using var stream = await response.Content.ReadAsStreamAsync(cancellationToken);
            using var reader = new ArrowStreamReader(stream);

            while (await reader.ReadNextRecordBatchAsync(cancellationToken) is { } recordBatch)
            {
                var schema = recordBatch.Schema;
                for (var i = 0; i < recordBatch.Length; i++)
                {
                    var iceLabel = ((Int32Array)recordBatch.Column(schema.GetFieldIndex("IceLabel"))).Values[i];
                    var turbineId = turbineStructure.First(x => x.MasterDataId == ((Int32Array)recordBatch.Column(schema.GetFieldIndex("TurbineId"))).Values[i]).Id;

                    result.Add(new IceEstimatesPowerData
                    {
                        TurbineId = turbineId,
                        PowerMean = ((DoubleArray)recordBatch.Column(schema.GetFieldIndex("PowerMean"))).Values[i],
                        WindSpeed = ((DoubleArray)recordBatch.Column(schema.GetFieldIndex("WindSpeedNormDens"))).Values[i],
                        IceLabel = Enum.Parse<IceLabelEnum>(iceLabel.ToString(), true),
                        Utc = DateTime.SpecifyKind(((TimestampArray)recordBatch.Column(schema.GetFieldIndex("UTC"))).GetTimestamp(i).Value.DateTime, DateTimeKind.Utc)
                    });
                }
            }
        }

        return result;
    }
}
