using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Epc;

public class PowerCurvesEpcListQuery : MediatR.IRequest<List<ChildStructure<ValueIntLabel>>>
{
    public Query Query { get; init; }
}

public class PowerCurvesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<PowerCurvesEpcListQuery, List<ChildStructure<ValueIntLabel>>>
{
    private const string NOT_VALID = "*";

    public async Task<List<ChildStructure<ValueIntLabel>>> Handle(PowerCurvesEpcListQuery request, CancellationToken cancellationToken)
    {
        var turbineIds = request.Query.TurbineId;

        var turbines = await dbContext.Turbines.Where(turbine => turbineIds.Contains(turbine.Id)).ToListAsync(cancellationToken);

        var epcList = await dbContext.Epc.Where(x => turbineIds.Contains(x.TurbineGuid)).ToListAsync(cancellationToken);

        var groupedEpcs = epcList
            .GroupBy(epc => epc.TurbineGuid)
            .ToList();

        var result = new List<ChildStructure<ValueIntLabel>>();

        foreach (var group in groupedEpcs)
        {
            var turbine = turbines.FirstOrDefault(t => t.Id == group.Key);
            var turbineName = turbine?.Name ?? "Unknown";

            var children = group.Select(epc => new ValueIntLabel
            {
                Label = epc.Succeeded
                    ? $"EPC {turbineName} {epc.From:yyyy-MM-dd} - {epc.To:yyyy-MM-dd}"
                    : $"EPC {turbineName} {epc.From:yyyy-MM-dd} - {epc.To:yyyy-MM-dd} {NOT_VALID}",
                Value = epc.EpcId
            }).ToList();

            result.Add(new ChildStructure<ValueIntLabel>
            {
                Label = turbineName,
                Value = group.Key,
                Children = children
            });
        }

        return result;
    }
}