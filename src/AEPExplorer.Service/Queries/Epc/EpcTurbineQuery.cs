using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Epc;

public class EpcTurbineQuery : MediatR.IRequest<List<ValueLabel>>
{
    public Query Query { get; init; }
}

public class EpcTurbineQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<EpcTurbineQuery, List<ValueLabel>>
{
    public async Task<List<ValueLabel>> Handle(EpcTurbineQuery request, CancellationToken cancellationToken)
    {
        var epcTurbines = await dbContext.Epc.Select(x => x.TurbineGuid).Distinct().ToListAsync(cancellationToken);
        var turbines= await dbContext.Turbines
            .Where(x => (request.Query.Region == null || request.Query.Region.Count == 0 || request.Query.Region.Contains(x.Site.Country.RegionId))
                        && (request.Query.Country == null || request.Query.Country.Count == 0 || request.Query.Country.Contains(x.Site.CountryId))
                        && (request.Query.Site == null || request.Query.Site.Count == 0 || request.Query.Site.Contains(x.SiteId))
                         && (request.Query.TurbineId == null || request.Query.TurbineId.Count == 0 || request.Query.TurbineId.Contains(x.Id))
                        )
            .Select(x => new ValueLabel
            {
                Value = x.Id,
                Label = x.Name
            })
            .OrderBy(x => x.Label)
            .ToListAsync(cancellationToken);
        return turbines.Where(x => epcTurbines.Contains(x.Value)).ToList();
    
    }
}