using AEPExplorer.Data.EF;
using AEPExplorer.Model.Response;
using Newtonsoft.Json;

namespace AEPExplorer.Service.Queries.Epc;

public class EpcLinesQuery : MediatR.IRequest<List<EmpiricalPowerCurveLine>>
{
    public List<int> EpcIds { get; init; }
}

public class EpcLinesQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<EpcLinesQuery, List<EmpiricalPowerCurveLine>>
{
    private const string NOT_VALID = "*";
    public async Task<List<EmpiricalPowerCurveLine>> Handle(EpcLinesQuery request, CancellationToken cancellationToken)
    {

        var epcList = await dbContext.Epc.Where(epc => request.EpcIds.Contains(epc.EpcId)).ToListAsync(cancellationToken);

        var turbineIds = epcList.Select(epc => epc.TurbineGuid).ToList();

        var turbines = await dbContext.Turbines
            .Where(turbine => turbineIds.Contains(turbine.Id))
            .ToListAsync(cancellationToken);

        var result = new List<EmpiricalPowerCurveLine>();

        foreach (var epc in epcList)
        {
            var turbineName = turbines.FirstOrDefault(turbine => turbine.Id == epc.TurbineGuid).Name ?? "Unknown";

            result.Add(new EmpiricalPowerCurveLine
            {
                Id = epc.EpcId,
                TurbineId = epc.TurbineGuid,
                Label = epc.Succeeded
                    ? $"EPC {turbineName} {epc.From:yyyy-MM-dd} - {epc.To:yyyy-MM-dd}"
                    : $"EPC {turbineName} {epc.From:yyyy-MM-dd} - {epc.To:yyyy-MM-dd} {NOT_VALID}",
                Coordinates = JsonConvert.DeserializeObject<List<WindSpeedEpcPercentiles>>(epc.LineCoordinates)
            });
        }

        return result;
    }
}