using AEPExplorer.Data.EF;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;

namespace AEPExplorer.Service.Queries.Epc;

public class EpcListQuery : MediatR.IRequest<List<ValueIntLabel>>
{
    public Query Query { get; init; }
}

public class EpcListQueryHandler(AepExplorerDbContext dbContext) : IRequestHandler<EpcListQuery, List<ValueIntLabel>>
{
    private const string NOT_VALID = "*";

    public async Task<List<ValueIntLabel>> Handle(EpcListQuery request, CancellationToken cancellationToken)
    {
        var turbineIds = request.Query.TurbineId;

        var turbines = await dbContext.Turbines.Where(turbine => turbineIds.Contains(turbine.Id)).ToListAsync(cancellationToken);

        var epcList = await dbContext.Epc.Where(x => turbineIds.Contains(x.TurbineGuid)).ToListAsync(cancellationToken);

        var result = new List<ValueIntLabel>();

        foreach (var epc in epcList)
        {
            var turbineName = turbines.FirstOrDefault(x => x.Id == epc.TurbineGuid).Name ?? "Unknown";

            result.Add(new ValueIntLabel
            {
                Label = epc.Succeeded
                    ? $"EPC {turbineName} {epc.From:yyyy-MM-dd} - {epc.To:yyyy-MM-dd}"
                    : $"EPC {turbineName} {epc.From:yyyy-MM-dd} - {epc.To:yyyy-MM-dd} {NOT_VALID}",
                Value = epc.EpcId
            });
        }

        return [..result.OrderBy(x=>x.Label)];
    }
}