using AEPExplorer.Data.EF;
using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;

namespace AEPExplorer.Service;

public static class FilterHelper
{
    public static (FilterByEnum? filterBy, string groupProperty) GetFilterGroupProperty(Query query)
    {
        FilterByEnum? filterBy = null;
        var groupProperty = nameof(AepReadData.TurbineId);
        if (query.TurbineId != null && query.TurbineId.Any())
        {
            filterBy = FilterByEnum.Turbine;
            groupProperty = nameof(AepReadData.TurbineId);

        }
        else if (query.Site != null && query.Site.Any())
        {
            filterBy = FilterByEnum.Site;
            groupProperty = nameof(AepReadData.SiteId);
        }
        else if (query.Country != null && query.Country.Any())
        {
            filterBy = FilterByEnum.Country;
            groupProperty = nameof(AepReadData.CountryId);
        }
        else if (query.Region != null && query.Region.Any())
        {
            filterBy = FilterByEnum.Region;
            groupProperty = nameof(AepReadData.RegionId);
        }
        else if (query.Customer != null && query.Customer.Any())
        {
            filterBy = FilterByEnum.Customer;
            groupProperty = nameof(AepReadData.CustomerId);
        }

        return (filterBy, groupProperty);
    }

    public static (FilterByEnum?, List<Guid>) GetFilterBy(Query query)
    {
        var comparisonItems = new List<Guid>();
        FilterByEnum? filterBy = null;
        if (query.TurbineId != null && query.TurbineId.Count != 0)
        {
            filterBy = FilterByEnum.Turbine;
            comparisonItems = query.TurbineId;
        }
        else if (query.Site != null && query.Site.Count != 0)
        {
            filterBy = FilterByEnum.Site;
            comparisonItems = query.Site;
        }
        else if (query.Country != null && query.Country.Count != 0)
        {
            filterBy = FilterByEnum.Country;
            comparisonItems = query.Country;
        }

        else if (query.Region != null && query.Region.Count != 0)
        {
            filterBy = FilterByEnum.Region;
            comparisonItems = query.Region;
        }
        else if (query.Customer != null && query.Customer.Count != 0)
        {
            filterBy = FilterByEnum.Customer;
            comparisonItems = query.Customer;
        }
        
        return (filterBy, comparisonItems);
    }

    public static Query GetSingleFilterQuery(Query query, FilterByEnum filterBy, Guid item)
    {
        query.Customer = filterBy == FilterByEnum.Customer ? [item] : null;
        query.Region = filterBy == FilterByEnum.Region ? [item] : null;
        query.Country = filterBy == FilterByEnum.Country ? [item] : null;
        query.Site = filterBy == FilterByEnum.Site ? [item] : null;
        query.TurbineId = filterBy == FilterByEnum.Turbine ? [item] : null;

        return query;
    }

    public static Query AppendFilterQuery(this Query query, FilterByEnum filterBy, Guid item)
    {
        switch (filterBy)
        {
            case FilterByEnum.Customer:
                query.Customer = [item];
                break;
            case FilterByEnum.Region:
                query.Region = [item];
                break;
            case FilterByEnum.Country:
                query.Country = [item];
                break;
            case FilterByEnum.Site:
                query.Site = [item];
                break;
            case FilterByEnum.Turbine:
                query.TurbineId = [item];
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(filterBy), filterBy, null);
        }
        return query;
    }

    public static List<T> SortBy<T>(this List<T> list, string sortBy, SortDirectionEnum? sortDirection)
    {
        if (string.IsNullOrEmpty(sortBy))
        {
            return list;
        }

        if (!sortDirection.HasValue || sortDirection == SortDirectionEnum.Asc)
        {
            return list.OrderBy(obj => obj.GetType().GetProperty(sortBy).GetValue(obj, null)).ToList();
        }
        else
        {
            return list.OrderByDescending(obj => obj.GetType().GetProperty(sortBy).GetValue(obj, null)).ToList();
        }
    }

    public static List<Guid> MapToCategories(List<Guid> categoryQuery, AepExplorerDbContext dbContext)
    {
        if (categoryQuery.Contains(CategoryHelper.OPERATIVE_CATEGORY))
        {
            var operative = dbContext.Categories
                .Where(x => x.Level4.Level3Id == CategoryHelper.LEVEL3_IN_SERVICE_ID
                            || x.Level4.Level3Id == CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            categoryQuery.Remove(CategoryHelper.OPERATIVE_CATEGORY);
            categoryQuery.AddRange(operative);
        }

        if (categoryQuery.Contains(CategoryHelper.NON_OPERATIVE_CATEGORY))
        {
            var nonOperative = dbContext.Categories
                .Where(x => x.Level4.Level3Id != CategoryHelper.LEVEL3_IN_SERVICE_ID
                            && x.Level4.Level3Id != CategoryHelper.LEVEL3_OUT_OF_SERVICE_ID)
                .Select(x => x.Id)
                .ToList();
            categoryQuery.Remove(CategoryHelper.NON_OPERATIVE_CATEGORY);
            categoryQuery.AddRange(nonOperative);
        }

        return categoryQuery;
    }

    public static List<Guid> MapToSubcategories(List<Guid> queryIds, AepExplorerDbContext dbContext)
    {
       var subcategoryIdsFromQuery = dbContext.Subcategories
            .Where(x => queryIds.Contains(x.Id))
            .Select(y => y.Id)
            .ToList();

        var subcategoryIdsFromQueryCategories = dbContext.Categories
            .Where(x => queryIds.Contains(x.Id))
            .SelectMany(y => y.Subcategories.Select(z => z.Id))
            .ToList();

        return subcategoryIdsFromQuery.Concat(subcategoryIdsFromQueryCategories).ToList();

    }
}