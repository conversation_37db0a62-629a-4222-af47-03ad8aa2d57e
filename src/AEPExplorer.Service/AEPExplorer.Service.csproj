<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <CodeAnalysisRuleSet>..\.sonarlint\dfp-aepexplorer-apicsharp.ruleset</CodeAnalysisRuleSet>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>13</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <AdditionalFiles Include="..\.sonarlint\dfp-aepexplorer-api\CSharp\SonarLint.xml" Link="SonarLint.xml" />
  </ItemGroup>

  <ItemGroup>
    <RuntimeHostConfigurationOption Include="System.Drawing.EnableUnixSupport" Value="true" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Apache.Arrow" Version="19.0.1" />
    <PackageReference Include="Apache.Arrow.Compression" Version="19.0.1" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.1.0" />
    <PackageReference Include="itext7" Version="8.0.5" />
    <PackageReference Include="itext7.bouncy-castle-adapter" Version="8.0.5" />
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="Microsoft.Azure.Databricks.Client" Version="2.8.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="MigraDocCore.Rendering" Version="1.3.65" />
    <PackageReference Include="NEST" Version="7.17.5" />
    <PackageReference Include="ScottPlot" Version="5.0.39" />
    <PackageReference Include="System.Collections" Version="4.3.0" />
    <PackageReference Include="System.Diagnostics.Debug" Version="4.3.0" />
    <PackageReference Include="System.IO.FileSystem.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Runtime.Extensions" Version="4.3.1" />
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AEPExplorer.Data.EF\AEPExplorer.Data.EF.csproj" />
    <ProjectReference Include="..\AEPExplorer.Model\AEPExplorer.Model.csproj" />
  </ItemGroup>
</Project>
