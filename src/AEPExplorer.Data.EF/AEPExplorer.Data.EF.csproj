<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <CodeAnalysisRuleSet>..\.sonarlint\dfp-aepexplorer-apicsharp.ruleset</CodeAnalysisRuleSet>
    <!-- Exclude the project from analysis -->
    <SonarQubeExclude>true</SonarQubeExclude>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>13</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <AdditionalFiles Include="..\.sonarlint\dfp-aepexplorer-api\CSharp\SonarLint.xml" Link="SonarLint.xml" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.8" />
    <PackageReference Include="System.IO.FileSystem.Primitives" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AEPExplorer.Model\AEPExplorer.Model.csproj" />
  </ItemGroup>

</Project>
