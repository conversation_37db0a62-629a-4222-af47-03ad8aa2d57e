using Microsoft.Extensions.Logging;
using AEPExplorer.Model.Domain;
using AEPExplorer.Model.Response;
using Microsoft.EntityFrameworkCore;

namespace AEPExplorer.Data.EF
{
    public class AepExplorerDbContext(DbContextOptions<AepExplorerDbContext> options) : DbContext(options)
    {
        public DbSet<FilterPreset> FilterPreset { get; set; }
        public DbSet<Epc> Epc { get; set; }
        public DbSet<Tpc> Tpc { get; set; }
        public DbSet<Performance> Performance { get; set; }
        public DbSet<Region> Regions { get; set; }
        public DbSet<Country> Countries { get; set; }
        public DbSet<Site> Sites { get; set; }
        public DbSet<Turbine> Turbines { get; set; }
        public DbSet<TurbineOem> Oems { get; set; }
        public DbSet<TurbinePlatform> TurbinePlatforms { get; set; }
        public DbSet<TurbineModel> Models { get; set; }
        public DbSet<ImportLog> ImportLogs { get; set; }
        public DbSet<Level3> Level3s { get; set; }
        public DbSet<Level4> Level4s { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Subcategory> Subcategories { get; set; }
        public DbSet<AlarmDescription> AlarmDescriptions { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<TurbineStatus> TurbineStatus { get; set; }
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.RemovePluralizingTableNameConvention();
            modelBuilder.RemoveCascadeDelete();

            modelBuilder.Entity<Region>()
                .HasMany(e => e.Countries);

            modelBuilder.Entity<Country>()
                .HasMany(e => e.Sites)
                .WithOne(e => e.Country)
                .HasForeignKey(e => e.CountryId);

            modelBuilder.Entity<Site>()
                .HasMany(e => e.Turbines)
                .WithOne(e => e.Site)
                .HasForeignKey(e => e.SiteId);

            modelBuilder.Entity<TurbineOem>()
                .HasMany(e => e.TurbinePlatforms)
                .WithOne(e => e.Oem)
                .HasForeignKey(e => e.OemId);

            modelBuilder.Entity<TurbinePlatform>()
                .HasMany(e => e.TurbineModels)
                .WithOne(e => e.TurbinePlatform)
                .HasForeignKey(e => e.TurbinePlatformId);

            modelBuilder.Entity<TurbineModel>()
                .HasMany(e => e.Turbines)
                .WithOne(e => e.TurbineModel)
                .HasForeignKey(e => e.ModelId);

            modelBuilder.Entity<Level3>()
                .HasMany(e => e.Level4s)
                .WithOne(e=>e.Level3)
                .HasForeignKey(e=>e.Level3Id);

            modelBuilder.Entity<Level4>()
                .HasMany(e => e.Categories)
                .WithOne(e=>e.Level4)
                .HasForeignKey(e=>e.Level4Id);

            modelBuilder.Entity<Category>()
                .HasMany(e => e.Subcategories)
                .WithOne(e => e.Category)
                .HasForeignKey(e => e.CategoryId);

            modelBuilder.Entity<Customer>()
                .HasMany(e => e.Turbines)
                .WithOne(e => e.Customer)
                .HasForeignKey(e => e.CustomerId);

            modelBuilder.Entity<Tpc>()
                .HasMany(t => t.Turbines)
                .WithMany(t => t.Tpcs)
                .UsingEntity(j => j.ToTable("TpcTurbine"));
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseLoggerFactory(loggerFactory).EnableSensitiveDataLogging();
            base.OnConfiguring(optionsBuilder);
        }

        ILoggerFactory loggerFactory = new LoggerFactory();
    }
}
