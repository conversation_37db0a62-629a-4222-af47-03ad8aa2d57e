using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class AddMissingFieldsToTurbineStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CountryName",
                table: "TurbineStatus",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LocationTypeName",
                table: "TurbineStatus",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegionName",
                table: "TurbineStatus",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegionShortName",
                table: "TurbineStatus",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TurbineName",
                table: "TurbineStatus",
                type: "text",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "LocationTypeName",
                table: "Turbine",
                type: "integer",
                nullable: false,
                oldClrType: typeof(byte),
                oldType: "smallint");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountryName",
                table: "TurbineStatus");

            migrationBuilder.DropColumn(
                name: "LocationTypeName",
                table: "TurbineStatus");

            migrationBuilder.DropColumn(
                name: "RegionName",
                table: "TurbineStatus");

            migrationBuilder.DropColumn(
                name: "RegionShortName",
                table: "TurbineStatus");

            migrationBuilder.DropColumn(
                name: "TurbineName",
                table: "TurbineStatus");

            migrationBuilder.AlterColumn<byte>(
                name: "LocationTypeName",
                table: "Turbine",
                type: "smallint",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");
        }
    }
}
