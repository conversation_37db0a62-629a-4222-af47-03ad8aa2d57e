// <auto-generated />
using System;
using AEPExplorer.Data.EF;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    [DbContext(typeof(AepExplorerDbContext))]
    partial class AEPExplorerDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("AEPExplorer.Model.Domain.AlarmDescription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Alarm")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("FunctionalSystem")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AlarmDescription");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Category", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("Level4Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Level4Id");

                    b.ToTable("Category");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Country", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<double>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<bool>("PowerBoost")
                        .HasColumnType("boolean");

                    b.Property<Guid>("RegionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RegionId");

                    b.ToTable("Country");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<bool>("PowerBoost")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("Customer");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Epc", b =>
                {
                    b.Property<int>("EpcId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("EpcId"));

                    b.Property<DateTime>("From")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LineCoordinates")
                        .HasColumnType("text");

                    b.Property<bool>("Succeeded")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("To")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("TurbineGuid")
                        .HasColumnType("uuid");

                    b.HasKey("EpcId");

                    b.ToTable("Epc");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.FilterPreset", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Favorite")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Query")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("FilterPreset");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.ImportLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("From")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("InsertedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MasterDataId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<DateTime>("To")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("TurbineId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("ImportLog");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Level3", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Level3");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Level4", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("Level3Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Level3Id");

                    b.ToTable("Level4");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Performance", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("Limit1")
                        .HasColumnType("double precision");

                    b.Property<double>("Limit2")
                        .HasColumnType("double precision");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("Performance");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Region", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<double>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<bool>("PowerBoost")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("Region");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsPresent")
                        .HasColumnType("boolean");

                    b.Property<double>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<double>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<int>("MasterDataId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<double>("NominalPower")
                        .HasColumnType("double precision");

                    b.Property<bool>("PowerBoost")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.ToTable("Site");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Subcategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.ToTable("Subcategory");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Tpc", b =>
                {
                    b.Property<int>("TpcId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("TpcId"));

                    b.Property<DateTime>("From")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LineCoordinates")
                        .HasColumnType("text");

                    b.Property<DateTime>("To")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("TpcId");

                    b.ToTable("Tpc");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Turbine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Altitude")
                        .HasColumnType("integer");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsPresent")
                        .HasColumnType("boolean");

                    b.Property<string>("IsPrototype")
                        .HasColumnType("text");

                    b.Property<double>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<int>("LocationTypeName")
                        .HasColumnType("integer");

                    b.Property<double>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<int>("MasterDataId")
                        .HasColumnType("integer");

                    b.Property<Guid>("ModelId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<double>("NominalPower")
                        .HasColumnType("double precision");

                    b.Property<bool>("PowerBoost")
                        .HasColumnType("boolean");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("TurbineStartUpDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("TurbineType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("WarrantyStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("ModelId");

                    b.HasIndex("SiteId");

                    b.ToTable("Turbine");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.TurbineModel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<bool>("PowerBoost")
                        .HasColumnType("boolean");

                    b.Property<Guid>("TurbinePlatformId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TurbinePlatformId");

                    b.ToTable("TurbineModel");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.TurbineOem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TurbineOem");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.TurbinePlatform", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("OemId")
                        .HasColumnType("uuid");

                    b.Property<bool>("PowerBoost")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("OemId");

                    b.ToTable("TurbinePlatform");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.TurbineStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CountryName")
                        .HasColumnType("text");

                    b.Property<bool>("IsTurbinePresent")
                        .HasColumnType("boolean");

                    b.Property<string>("LocationTypeName")
                        .HasColumnType("text");

                    b.Property<string>("ProjectParkId")
                        .HasColumnType("text");

                    b.Property<string>("ProjectParkName")
                        .HasColumnType("text");

                    b.Property<string>("RegionName")
                        .HasColumnType("text");

                    b.Property<string>("RegionShortName")
                        .HasColumnType("text");

                    b.Property<string>("ScadaParkName")
                        .HasColumnType("text");

                    b.Property<int>("TurbineId")
                        .HasColumnType("integer");

                    b.Property<string>("TurbineMissingInfo")
                        .HasColumnType("text");

                    b.Property<string>("TurbineModel")
                        .HasColumnType("text");

                    b.Property<string>("TurbineName")
                        .HasColumnType("text");

                    b.Property<string>("TurbineOem")
                        .HasColumnType("text");

                    b.Property<string>("TurbinePlatform")
                        .HasColumnType("text");

                    b.Property<DateTime>("TurbineStartUpDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("TurbineStatus");
                });

            modelBuilder.Entity("TpcTurbine", b =>
                {
                    b.Property<int>("TpcsTpcId")
                        .HasColumnType("integer");

                    b.Property<Guid>("TurbinesId")
                        .HasColumnType("uuid");

                    b.HasKey("TpcsTpcId", "TurbinesId");

                    b.HasIndex("TurbinesId");

                    b.ToTable("TpcTurbine", (string)null);
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Category", b =>
                {
                    b.HasOne("AEPExplorer.Model.Domain.Level4", "Level4")
                        .WithMany("Categories")
                        .HasForeignKey("Level4Id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Level4");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Country", b =>
                {
                    b.HasOne("AEPExplorer.Model.Domain.Region", "Region")
                        .WithMany("Countries")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Region");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Level4", b =>
                {
                    b.HasOne("AEPExplorer.Model.Domain.Level3", "Level3")
                        .WithMany("Level4s")
                        .HasForeignKey("Level3Id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Level3");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Site", b =>
                {
                    b.HasOne("AEPExplorer.Model.Domain.Country", "Country")
                        .WithMany("Sites")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Subcategory", b =>
                {
                    b.HasOne("AEPExplorer.Model.Domain.Category", "Category")
                        .WithMany("Subcategories")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Turbine", b =>
                {
                    b.HasOne("AEPExplorer.Model.Domain.Customer", "Customer")
                        .WithMany("Turbines")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("AEPExplorer.Model.Domain.TurbineModel", "TurbineModel")
                        .WithMany("Turbines")
                        .HasForeignKey("ModelId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("AEPExplorer.Model.Domain.Site", "Site")
                        .WithMany("Turbines")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Site");

                    b.Navigation("TurbineModel");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.TurbineModel", b =>
                {
                    b.HasOne("AEPExplorer.Model.Domain.TurbinePlatform", "TurbinePlatform")
                        .WithMany("TurbineModels")
                        .HasForeignKey("TurbinePlatformId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("TurbinePlatform");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.TurbinePlatform", b =>
                {
                    b.HasOne("AEPExplorer.Model.Domain.TurbineOem", "Oem")
                        .WithMany("TurbinePlatforms")
                        .HasForeignKey("OemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Oem");
                });

            modelBuilder.Entity("TpcTurbine", b =>
                {
                    b.HasOne("AEPExplorer.Model.Domain.Tpc", null)
                        .WithMany()
                        .HasForeignKey("TpcsTpcId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("AEPExplorer.Model.Domain.Turbine", null)
                        .WithMany()
                        .HasForeignKey("TurbinesId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Category", b =>
                {
                    b.Navigation("Subcategories");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Country", b =>
                {
                    b.Navigation("Sites");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Customer", b =>
                {
                    b.Navigation("Turbines");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Level3", b =>
                {
                    b.Navigation("Level4s");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Level4", b =>
                {
                    b.Navigation("Categories");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Region", b =>
                {
                    b.Navigation("Countries");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.Site", b =>
                {
                    b.Navigation("Turbines");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.TurbineModel", b =>
                {
                    b.Navigation("Turbines");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.TurbineOem", b =>
                {
                    b.Navigation("TurbinePlatforms");
                });

            modelBuilder.Entity("AEPExplorer.Model.Domain.TurbinePlatform", b =>
                {
                    b.Navigation("TurbineModels");
                });
#pragma warning restore 612, 618
        }
    }
}
