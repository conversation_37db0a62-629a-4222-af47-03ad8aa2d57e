using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class UpdatedTpcAndTurbineTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TurbineGuid",
                table: "Tpc");

            migrationBuilder.CreateTable(
                name: "TpcTurbine",
                columns: table => new
                {
                    TpcsTpcId = table.Column<int>(type: "integer", nullable: false),
                    TurbinesId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TpcTurbine", x => new { x.TpcsTpcId, x.TurbinesId });
                    table.ForeignKey(
                        name: "FK_TpcTurbine_Tpc_TpcsTpcId",
                        column: x => x.TpcsTpcId,
                        principalTable: "Tpc",
                        principalColumn: "TpcId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_TpcTurbine_Turbine_TurbinesId",
                        column: x => x.TurbinesId,
                        principalTable: "Turbine",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TpcTurbine_TurbinesId",
                table: "TpcTurbine",
                column: "TurbinesId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TpcTurbine");

            migrationBuilder.AddColumn<Guid>(
                name: "TurbineGuid",
                table: "Tpc",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));
        }
    }
}
