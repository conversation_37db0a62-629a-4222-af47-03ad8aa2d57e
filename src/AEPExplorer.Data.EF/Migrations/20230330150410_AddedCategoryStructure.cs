using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class AddedCategoryStructure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TurbineModel_TurbinePlatform_PlatformId",
                table: "TurbineModel");

            migrationBuilder.RenameColumn(
                name: "PlatformId",
                table: "TurbineModel",
                newName: "TurbinePlatformId");

            migrationBuilder.RenameIndex(
                name: "IX_TurbineModel_PlatformId",
                table: "TurbineModel",
                newName: "IX_TurbineModel_TurbinePlatformId");

            migrationBuilder.CreateTable(
                name: "Category",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Category", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Subcategory",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    CategoryId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Subcategory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Subcategory_Category_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Category",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Subcategory_CategoryId",
                table: "Subcategory",
                column: "CategoryId");

            migrationBuilder.AddForeignKey(
                name: "FK_TurbineModel_TurbinePlatform_TurbinePlatformId",
                table: "TurbineModel",
                column: "TurbinePlatformId",
                principalTable: "TurbinePlatform",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TurbineModel_TurbinePlatform_TurbinePlatformId",
                table: "TurbineModel");

            migrationBuilder.DropTable(
                name: "Subcategory");

            migrationBuilder.DropTable(
                name: "Category");

            migrationBuilder.RenameColumn(
                name: "TurbinePlatformId",
                table: "TurbineModel",
                newName: "PlatformId");

            migrationBuilder.RenameIndex(
                name: "IX_TurbineModel_TurbinePlatformId",
                table: "TurbineModel",
                newName: "IX_TurbineModel_PlatformId");

            migrationBuilder.AddForeignKey(
                name: "FK_TurbineModel_TurbinePlatform_PlatformId",
                table: "TurbineModel",
                column: "PlatformId",
                principalTable: "TurbinePlatform",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
