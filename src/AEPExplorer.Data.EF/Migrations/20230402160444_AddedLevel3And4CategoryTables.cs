using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class AddedLevel3And4CategoryTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "Level4Id",
                table: "Category",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateTable(
                name: "Level3",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Level3", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Level4",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    Level3Id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Level4", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Level4_Level3_Level3Id",
                        column: x => x.Level3Id,
                        principalTable: "Level3",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Category_Level4Id",
                table: "Category",
                column: "Level4Id");

            migrationBuilder.CreateIndex(
                name: "IX_Level4_Level3Id",
                table: "Level4",
                column: "Level3Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Category_Level4_Level4Id",
                table: "Category",
                column: "Level4Id",
                principalTable: "Level4",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Category_Level4_Level4Id",
                table: "Category");

            migrationBuilder.DropTable(
                name: "Level4");

            migrationBuilder.DropTable(
                name: "Level3");

            migrationBuilder.DropIndex(
                name: "IX_Category_Level4Id",
                table: "Category");

            migrationBuilder.DropColumn(
                name: "Level4Id",
                table: "Category");
        }
    }
}
