using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class AddTurbineNewFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "IsPrototype",
                table: "Turbine",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TurbineStartUpDate",
                table: "Turbine",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TurbineType",
                table: "Turbine",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "WarrantyStartDate",
                table: "Turbine",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsPrototype",
                table: "Turbine");

            migrationBuilder.DropColumn(
                name: "TurbineStartUpDate",
                table: "Turbine");

            migrationBuilder.DropColumn(
                name: "TurbineType",
                table: "Turbine");

            migrationBuilder.DropColumn(
                name: "WarrantyStartDate",
                table: "Turbine");
        }
    }
}
