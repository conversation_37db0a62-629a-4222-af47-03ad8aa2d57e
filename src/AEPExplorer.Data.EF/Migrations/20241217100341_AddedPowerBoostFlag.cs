using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class AddedPowerBoostFlag : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "PowerBoost",
                table: "TurbinePlatform",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PowerBoost",
                table: "TurbineModel",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PowerBoost",
                table: "Turbine",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PowerBoost",
                table: "Site",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PowerBoost",
                table: "Region",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PowerBoost",
                table: "Customer",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PowerBoost",
                table: "Country",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PowerBoost",
                table: "TurbinePlatform");

            migrationBuilder.DropColumn(
                name: "PowerBoost",
                table: "TurbineModel");

            migrationBuilder.DropColumn(
                name: "PowerBoost",
                table: "Turbine");

            migrationBuilder.DropColumn(
                name: "PowerBoost",
                table: "Site");

            migrationBuilder.DropColumn(
                name: "PowerBoost",
                table: "Region");

            migrationBuilder.DropColumn(
                name: "PowerBoost",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "PowerBoost",
                table: "Country");
        }
    }
}
