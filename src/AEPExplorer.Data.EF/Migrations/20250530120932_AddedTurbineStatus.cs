using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class AddedTurbineStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "TurbineStatus",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TurbineOem = table.Column<string>(type: "text", nullable: true),
                    ProjectParkName = table.Column<string>(type: "text", nullable: true),
                    ScadaParkName = table.Column<string>(type: "text", nullable: true),
                    ProjectParkId = table.Column<int>(type: "integer", nullable: false),
                    TurbineId = table.Column<int>(type: "integer", nullable: false),
                    TurbinePlatform = table.Column<string>(type: "text", nullable: true),
                    TurbineModel = table.Column<string>(type: "text", nullable: true),
                    TurbineStartUpDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsTurbinePresent = table.Column<bool>(type: "boolean", nullable: false),
                    TurbineMissingInfo = table.Column<string>(type: "text", nullable: true),
                    InsertTimestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TurbineStatus", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TurbineStatus");
        }
    }
}
