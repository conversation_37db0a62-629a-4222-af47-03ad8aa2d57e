using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    public partial class ChangedIndexUserIdInFilterPreset : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_FilterPreset_UserId",
                table: "FilterPreset");

            migrationBuilder.CreateIndex(
                name: "IX_FilterPreset_UserId",
                table: "FilterPreset",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_FilterPreset_UserId",
                table: "FilterPreset");

            migrationBuilder.CreateIndex(
                name: "IX_FilterPreset_UserId",
                table: "FilterPreset",
                column: "UserId",
                unique: true);
        }
    }
}
