using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AEPExplorer.Data.EF.Migrations
{
    public partial class AddedPerformanceTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Performance",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    LimitForBad = table.Column<double>(type: "double precision", nullable: false),
                    LimitForUnderperforming = table.Column<double>(type: "double precision", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Performance", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FilterPreset_UserId",
                table: "FilterPreset",
                column: "UserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Performance_UserId",
                table: "Performance",
                column: "UserId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Performance");

            migrationBuilder.DropIndex(
                name: "IX_FilterPreset_UserId",
                table: "FilterPreset");
        }
    }
}
