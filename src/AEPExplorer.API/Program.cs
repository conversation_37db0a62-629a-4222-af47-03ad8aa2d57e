using AEPExplorer.API.Extensions;
using AEPExplorer.API.Middleware;
using AEPExplorer.Data.EF;
using AEPExplorer.Service.Queries.Table.Export;
using Microsoft.Azure.Databricks.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddControllers();
builder.Services.AddElasticsearch(builder.Configuration);
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(AEPExplorer.Service.Commands.Import.ImportData).Assembly));
builder.Services.AddOutputCache(options =>
{
    options.AddPolicy("OutputCacheWithAuthPolicy", policy => policy.AddPolicy<OutputCacheWithAuthPolicy>().Tag("ResponseTag"));
    options.AddPolicy("NoCache", policy => policy.NoCache());
});

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "AEPExplorer",
        Version = "v1"
    });
});
builder.Services.AddSecurityConfiguration(builder.Configuration);
builder.Services.AddCors();
builder.Services.AddRouting(options => options.LowercaseUrls = true);
builder.Services.AddDbContext<AepExplorerDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection"),
        b => b.MigrationsAssembly(typeof(AepExplorerDbContext).Assembly.FullName)));
builder.Services.AddScoped<IExportFilter, ExportFilter>();
builder.Services.AddSingleton<DatabricksClient>(_ =>
{
    var workspaceUrl = builder.Configuration.GetSection("Databricks")["WorkspaceUrl"]
                       ?? throw new InvalidOperationException("Databricks WorkspaceUrl is missing");
    var token = builder.Configuration.GetSection("Databricks")["Token"]
                ?? throw new InvalidOperationException("Databricks Token is missing");
    return DatabricksClient.CreateClient(workspaceUrl, token);
});

var app = builder.Build();

using (var scope = app.Services.CreateScope())
{
    var db = scope.ServiceProvider.GetRequiredService<AepExplorerDbContext>();
    db.Database.Migrate();
}

if (app.Environment.IsDevelopment() || app.Environment.IsEnvironment("Local"))
{
    app.UseDeveloperExceptionPage();
}

app.UseMiddleware<ExceptionMiddleware>();

app.UseCors(options => options.AllowAnyOrigin()
    .AllowAnyMethod()
    .AllowAnyHeader()
    .WithExposedHeaders("Content-Disposition", "content-disposition", "content-transfer-encoding"));

app.UseHttpsRedirection();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();
app.UseOutputCache();
app.MapControllers();

app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint(builder.Configuration["SwaggerUrl"], "EAPExplorer Api V1");
    c.RoutePrefix = "swagger/ui";
});


app.Run();