using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Commands;
using AEPExplorer.Service.Queries;
using AEPExplorer.Service.Queries.Map;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class MapController(IMediator mediator, IConfiguration configuration) : ControllerBase
    {
        /// <summary>
        /// Return data for map with regions
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("region")]
        public async Task<ActionResult<List<MapModelTotalLosses>>> GetRegion(Query query)
        {
            return await mediator.Send(new MapTotalLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.RegionId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return detailed categories for map with regions
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("region/detail")]
        public async Task<ActionResult<List<MapModelDetailedLosses>>> GetRegionDetailedLosses(Query query)
        {
            return await mediator.Send(new MapDetailedLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.RegionId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return ice data for map with regions
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("region/ice")]
        public async Task<ActionResult<List<MapModelIceLosses>>> GetRegionIceLosses(Query query)
        {
            return await mediator.Send(new MapIceLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.RegionId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return data for map with countries
        /// </summary>
        /// <returns>List</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("country")]
        public async Task<ActionResult<List<MapModelTotalLosses>>> GetCountry(Query query)
        {
            return await mediator.Send(new MapTotalLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.CountryId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return detailed categories for map with countries
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("country/detail")]
        public async Task<ActionResult<List<MapModelDetailedLosses>>> GetCountryDetailedLosses(Query query)
        {
            return await mediator.Send(new MapDetailedLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.CountryId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return ice data for map with countries
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("country/ice")]
        public async Task<ActionResult<List<MapModelIceLosses>>> GetCountryIceLosses(Query query)
        {
            return await mediator.Send(new MapIceLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.CountryId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return data for map with sites
        /// </summary>
        /// <returns>List</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("site")]
        public async Task<ActionResult<List<MapModelTotalLosses>>> GetSite(Query query)
        {
            return await mediator.Send(new MapTotalLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.SiteId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return detailed categories for map with sites
        /// </summary>
        /// <returns>List</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("site/detail")]
        public async Task<ActionResult<List<MapModelDetailedLosses>>> GetSiteDetailedLosses(Query query)
        {
            return await mediator.Send(new MapDetailedLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.SiteId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return ice data for map with sites
        /// </summary>
        /// <returns>List</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("site/ice")]
        public async Task<ActionResult<List<MapModelIceLosses>>> GetSiteIceLosses(Query query)
        {
            return await mediator.Send(new MapIceLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.SiteId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return data for map with turbines
        /// </summary>
        /// <returns>List</returns>
        [HttpPost]
        [Route("turbine")]
        public async Task<ActionResult<List<MapModelTotalLosses>>> GetTurbine(Query query)
        {
            return await mediator.Send(new MapTotalLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.TurbineId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return detailed categories for map with turbines
        /// </summary>
        /// <returns>List</returns>
        [HttpPost]
        [Route("turbine/detail")]
        public async Task<ActionResult<List<MapModelDetailedLosses>>> GetTurbineDetailedLosses(Query query)
        {
            return await mediator.Send(new MapDetailedLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.TurbineId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Return ice data for map with turbines
        /// </summary>
        /// <returns>List</returns>
        [HttpPost]
        [Route("turbine/ice")]
        public async Task<ActionResult<List<MapModelIceLosses>>> GetTurbineIceLosses(Query query)
        {
            return await mediator.Send(new MapIceLossesQuery
            {
                Query = query,
                AepElasticsearchDataProperty = nameof(AepReadData.TurbineId),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Get settings for performance on map, if defaultValue is true returns default values
        /// </summary>
        /// <returns>Limit1, Limit2</returns>
        [HttpGet]
        [Route("performance/{defaultValue:bool?}")]
        public async Task<ActionResult<PerformanceModel>> GetPerformance(bool? defaultValue = false)
        {
            return await mediator.Send(new PerformanceQuery
            {
                DefaultValue = defaultValue ?? false,
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Save settings for performance on map
        /// </summary>
        /// <returns>true/false</returns>
        [HttpPost]
        [Route("performance")]
        public async Task<ActionResult<bool>> SetPerformance(PerformanceModel performance)
        {
            return await mediator.Send(new CreatePerformanceCommand
            {
                Limit1 = Math.Round(performance.Limit1 / 100, 2),
                Limit2 = Math.Round(performance.Limit2 / 100, 2),
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Get mapbox api key
        /// </summary>
        /// <returns>string</returns>

        [HttpGet]
        [Route("mapbox-token")]
        public ActionResult<string> GetMapboxToken()
        {
            var mapboxToken = configuration.GetValue<string>("Mapbox:ApiKey");

            if (mapboxToken == null)
            {
                return NotFound();
            }

            return mapboxToken;
        }

    }
}