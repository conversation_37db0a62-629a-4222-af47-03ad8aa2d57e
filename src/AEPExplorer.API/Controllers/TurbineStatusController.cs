using AEPExplorer.Model.Domain;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.TurbineStatus;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TurbineStatusController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Get all turbine status data
        /// </summary>
        /// <returns>List of turbine status items</returns>
        [HttpPost]
        [ProducesResponseType(typeof(IEnumerable<TurbineStatus>), 200)]
        public async Task<ActionResult<TableTotalModelResponse<TurbineStatus>>> GetAll([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusQuery { Query = filter };
            var result = await mediator.Send(query);
            return Ok(result);
        }
    }
}
