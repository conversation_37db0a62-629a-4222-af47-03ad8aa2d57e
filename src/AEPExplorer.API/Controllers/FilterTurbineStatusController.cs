using AEPExplorer.Model.Request;
using AEPExplorer.Service.Queries.TurbineStatus;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FilterTurbineStatusController : ControllerBase
    {
        private readonly IMediator _mediator;

        public FilterTurbineStatusController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// Get distinct regions based on other filter criteria
        /// </summary>
        [HttpPost("regions")]
        public async Task<IActionResult> GetRegions([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusRegionsQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct region short names based on other filter criteria
        /// </summary>
        [HttpPost("region-short-names")]
        public async Task<IActionResult> GetRegionShortNames([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusRegionShortNamesQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct countries based on other filter criteria
        /// </summary>
        [HttpPost("countries")]
        public async Task<IActionResult> GetCountries([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusCountriesQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct location type names based on other filter criteria
        /// </summary>
        [HttpPost("location-type-names")]
        public async Task<IActionResult> GetLocationTypeNames([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusLocationTypeNamesQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct project park names based on other filter criteria
        /// </summary>
        [HttpPost("project-park-names")]
        public async Task<IActionResult> GetProjectParkNames([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusProjectParkNamesQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct SCADA park names based on other filter criteria
        /// </summary>
        [HttpPost("scada-park-names")]
        public async Task<IActionResult> GetScadaParkNames([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusScadaParkNamesQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct project park IDs based on other filter criteria
        /// </summary>
        [HttpPost("project-park-ids")]
        public async Task<IActionResult> GetProjectParkIds([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusProjectParkIdsQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct turbine names based on other filter criteria
        /// </summary>
        [HttpPost("turbine-names")]
        public async Task<IActionResult> GetTurbineNames([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusTurbineNamesQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct turbine IDs based on other filter criteria
        /// </summary>
        [HttpPost("turbine-ids")]
        public async Task<IActionResult> GetTurbineIds([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusTurbineIdsQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct turbine OEMs based on other filter criteria
        /// </summary>
        [HttpPost("turbine-oems")]
        public async Task<IActionResult> GetTurbineOems([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusOemsQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct turbine platforms based on other filter criteria
        /// </summary>
        [HttpPost("turbine-platforms")]
        public async Task<IActionResult> GetTurbinePlatforms([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusPlatformsQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get distinct turbine models based on other filter criteria
        /// </summary>
        [HttpPost("turbine-models")]
        public async Task<IActionResult> GetTurbineModels([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusModelsQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        
        /// <summary>
        /// Get distinct turbine models based on other filter criteria
        /// </summary>
        [HttpPost("is-turbine-present")]
        public async Task<IActionResult> GetIsTurbinePresent([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusIsTurbinePresentsQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        
        /// <summary>
        /// Get distinct turbine missing info values based on other filter criteria
        /// </summary>
        [HttpPost("turbine-missing-info")]
        public async Task<IActionResult> GetTurbineMissingInfo([FromBody] TurbineStatusFilterRequest filter)
        {
            var query = new TurbineStatusMissingInfoQuery { Filter = filter };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
    }
}
