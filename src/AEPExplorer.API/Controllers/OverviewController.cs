using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Overview;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class OverviewController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Return data for main KPI
        /// </summary>
        /// <returns>Value and percentage of energy potential, actual and lost energy</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("actual-production-vs-lost-energy")]
        public async Task<ActionResult<List<NameValuePercentage> >> GetActualProductionVsLostEnergy(Query query)
        {
            var result = await mediator.Send(new ActualProductionVsLostEnergyQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for Losses by platform
        /// </summary>
        /// <returns>List of values and percentages for losses by platform</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("losses-by-platform")]
        public async Task<ActionResult<List<LossesByPlatformModel>>> GetLossesByPlatform(Query query)
        {
            var result = await mediator.Send(new LossByPlatformQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for Losses by platform
        /// </summary>
        /// <returns>List of values and percentages for losses by platform spread across categories</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("losses-by-platform/{turbinePlatformId:guid}")]
        public async Task<ActionResult<List<NameValuePercentageWithChildren>>> GetLossesByPlatformAndCategory([FromRoute] Guid turbinePlatformId, [FromBody] Query query)
        {
            query.Platform = new List<Guid> { turbinePlatformId };
            var result = await mediator.Send(new LossByPlatformAndCategoryQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for production adn losses per platform 
        /// </summary>
        /// <returns>List of values and percentages for production and losses by platform</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("platform-production-and-losses")]
        public async Task<ActionResult<List<PlatformProdutionAndLosses>>> GetPlatformProductionAndLosses(Query query)
        {
            var result = await mediator.Send(new PlatformProductionAndLossesQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for Losses by platform
        /// </summary>
        /// <returns>List of values and percentages for losses by platform spread across categories</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("losses-by-platforms")]
        public async Task<ActionResult<List<PlatformLosses>>> GetPlatformLosses(Query query)
        {
            var result = await mediator.Send(new PlatformLossesQuery { Query = query });
            return result;
        }


        /// <summary>
        /// Return data for Losses by platform per category
        /// </summary>
        /// <returns>List of values and percentages for losses per category by platform</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("losses-by-platform-per-category/{turbinePlatformId:guid}")]
        public async Task<ActionResult<List<NameValuePercentageWithChildren>>> GetPlatformLossesPerCategory([FromRoute] Guid turbinePlatformId, [FromBody] Query query)
        {
            query.Platform = new List<Guid> { turbinePlatformId };
            var result = await mediator.Send(new PlatformLossesPerCategoryQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for Losses by platform per subcategory
        /// </summary>
        /// <returns>List of values and percentages for losses per subcategory by platform</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("losses-by-platform-per-subcategory/{turbinePlatformId:guid}")]
        public async Task<ActionResult<List<NameValuePercentageWithChildren>>> GetLossesByPlatformPerSubcategory([FromRoute] Guid turbinePlatformId, [FromBody] Query query)
        {
            query.Platform = new List<Guid> { turbinePlatformId };
            var result = await mediator.Send(new PlatformLossesPerSubcategoryQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for Losses by functional system
        /// </summary>
        /// <returns>List of values and percentages for losses by functional system</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("losses-by-functional-system")]
        public async Task<ActionResult<List<LossesBaseModel>>> GetLossesByFunctionalSystem(AlarmQuery query)
        {
            var result = await mediator.Send(new LossByFunctionalSystemQuery() { AlarmQuery = query });
            return result;
        }

        /// <summary>
        /// Return data for Losses by alarm
        /// </summary>
        /// <returns>List of values and percentages for losses by alarm</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("losses-by-alarm")]
        public async Task<ActionResult<List<LossesBaseModel>>> GetLossesByAlarm(AlarmQuery query)
        {
            var result = await mediator.Send(new LossByAlarmQuery() { AlarmQuery = query });
            return result;
        }
        
        /// <summary>
        /// Returns percentage of Alarms and Stops losses
        /// </summary>
        /// <returns>double</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("alarms-and-stops")]
        public async Task<ActionResult<double>> GetAlarmsAndStops(Query query)
        {
            var result = await mediator.Send(new AlarmsAndStopsQuery() { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for Losses by alarm
        /// </summary>
        /// <returns>List of values and percentages for losses by alarm</returns>
        [HttpGet]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("alarm-description/{alarmId}")]
        public async Task<ActionResult<DescriptionModel>> GetAlarmDescription(string alarmId)
        {
            var result = await mediator.Send(new AlarmDescriptionQuery() { AlarmId = alarmId });
            return result;
        }

        /// <summary>
        /// Return data for Losses by category
        /// </summary>
        /// <returns>List of values and percentages for losses by categories and subcategories</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("losses-by-category")]
        public async Task<ActionResult<List<LossLevel>>> GetLossesByCategory([FromBody] Query query)
        {
            var result = await mediator.Send(new LossByCategoryQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data coverage timeline 
        /// </summary>
        /// <returns>List of data coverage values per day</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("data-coverage")]
        public async Task<ActionResult<List<CoverageTimelineModel>>> GetDataCoverage([FromBody] Query query)
        {
            var result = await mediator.Send(new DataCoverageQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Generate Overview page report
        /// </summary>
        /// <returns>File</returns>
        [HttpPost]
        [Route("report")]
        public async Task<ActionResult> GetReport(Query query)
        {
            var result = await mediator.Send(new ReportQuery { Query = query, UserFullName = HttpContext.User.GetFullName() });
            return File(result, "application/octet-stream");
        }
    }
}