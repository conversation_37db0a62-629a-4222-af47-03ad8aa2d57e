using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.AssetCheck;
using AEPExplorer.Service.Queries.Epc;
using AEPExplorer.Service.Queries.Tpc;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class AssetCheckController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Returns data for estimated energy losses
        /// </summary>
        /// <returns>Value, percentage and duration in hours per category</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("estimated-energy-losses")]
        public async Task<ActionResult<List<LossByCategoryModel>>> GetEstimatedEnergyLosses([FromBody] Query query)
        {
            var result = await mediator.Send(new EstimatedEnergyLossesQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for estimated energy losses per category
        /// </summary>
        /// <returns>Value, percentage and duration in hours per subcategory</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("estimated-energy-losses/{categoryId}")]
        public async Task<ActionResult<List<LossByCategoryModel>>> GetEstimatedEnergyLossesPerCategory([FromBody] Query query, [FromRoute] Guid categoryId)
        {
            var result = await mediator.Send(new EstimatedEnergyLossesPerCategoryQuery { Query = query, CategoryId = categoryId });
            return result;
        }

        /// <summary>
        /// Returns production and losses per turbine
        /// </summary>
        /// <returns>Value and percentage for production and losses per turbine</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("production-and-losses-per-turbine")]
        public async Task<ActionResult<List<ProductionAndLossesPerTurbine>>> GetProductionAndLossesPerTurbine([FromBody] Query query)
        {
            var result = await mediator.Send(new ProductionAndLossesPerTurbineQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns turbine losses per category
        /// </summary>
        /// <returns>Value and percentage for turbine losses per category</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("turbine-losses-per-category")]
        public async Task<ActionResult<List<LossesByGroups>>> GetTurbineLossesPerCategory([FromBody] Query query)
        {
            var result = await mediator.Send(new TurbineLossesPerCategoryQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for turbine selection
        /// </summary>
        /// <returns>List of turbines</returns>
        [HttpPost]
        [Route("epc-turbines")]
        public async Task<ActionResult<List<ValueLabel>>> GetEpcData(Query query)
        {
            var result = await mediator.Send(new EpcTurbineQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for epc selection
        /// </summary>
        /// <returns>List of epc dates</returns>
        [HttpPost]
        [Route("epc-list")]
        public async Task<ActionResult<List<ValueIntLabel>>> GetEpcList(Query query)
        {
            var result = await mediator.Send(new EpcListQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns EPC lines for chart
        /// </summary>
        /// <returns>EPC coordinates</returns>
        [HttpPost]
        [Route("epc-lines")]
        public async Task<ActionResult<List<EmpiricalPowerCurveLine>>> GetEpcLines(EpcQuery epcQuery)
        {
            var result = await mediator.Send(new EpcLinesQuery { EpcIds = epcQuery.EpcIds });
            return result;
        }

        /// <summary>
        /// Returns EPC dots for chart
        /// </summary>
        /// <returns>Dot coordinates</returns>
        [HttpPost]
        [Route("epc-dots")]
        public async Task<ActionResult<List<FullPerformancePowerData>>> GetEpcDots(Query query)
        {
            var result = await mediator.Send(new FullPerformancePowerQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for turbine selection
        /// </summary>
        /// <returns>List of turbines</returns>
        [HttpPost]
        [Route("tpc-turbines")]
        public async Task<ActionResult<List<ValueLabel>>> GetTpcData(Query query)
        {
            var result = await mediator.Send(new TpcTurbineQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns EPC lines for chart
        /// </summary>
        /// <returns>EPC coordinates</returns>
        [HttpPost]
        [Route("tpc-lines")]
        public async Task<ActionResult<List<TheoreticalPowerCurveLine>>> GetTpcLines(TpcQuery tpcQuery)
        {
            var result = await mediator.Send(new TpcLinesQuery { TurbineIds = tpcQuery.TurbineIds });
            return result;
        }
        
        /// <summary>
        /// Generate Asset Check report
        /// </summary>
        /// <returns>File</returns>
        [HttpPost]
        [Route("report")]
        public async Task<ActionResult> GetReport(Query query)
        {

                var result = await mediator.Send(new ReportQuery { Query = query, UserFullName = HttpContext.User.GetFullName() });
                return File(result, "application/octet-stream");
            
        }
    }
}