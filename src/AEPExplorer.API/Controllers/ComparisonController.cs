using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Comparison;
using AEPExplorer.Service.Queries.Comparison.Report;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class ComparisonController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Return data for LPF timeline
        /// </summary>
        /// <returns>List of energy Potential, actual and lost energy</returns>
        [HttpPost]
        [Route("production-losses-barchart")]
        public async Task<ActionResult<List<BarChartComparison>>> GetProductionLossesBarchart([FromBody] Query query)
        {
            var result = await mediator.Send(new ProductionAndLossesBarChartComparisonQuery { Query = query });
            return result;
        }
        
        /// <summary>
        /// Return data for Production/Losses timeline comparison
        /// </summary>
        /// <returns>List of energy Potential, actual, and lost energy, and downtime period</returns>
        [HttpPost]
        [Route("production-losses-timeline")]
        public async Task<ActionResult<ProductionAndLossesTimelineComparison>> GetProductionLossesTimeline([FromBody]Query query)
        {
            var result = await mediator.Send(new ProductionAndLossesTimelineComparisonQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for LPF timeline
        /// </summary>
        /// <returns>List of energy Potential, actual and lost energy</returns>
        [HttpPost]
        [Route("losses-by-category-barchart")]
        public async Task<ActionResult<List<LossesByGroups>>> GetLossesBarchart([FromBody] Query query)
        {
            var result = await mediator.Send(new LossesByCategoryBarChartComparisonQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Generate page report
        /// </summary>
        /// <returns>File</returns>
        [HttpPost]
        [Route("report")]
        public async Task<ActionResult> GetReport(Query query)
        {
            var result = await mediator.Send(new ReportQuery { Query = query, UserFullName = HttpContext.User.GetFullName() });
            return File(result, "application/octet-stream");
        }
    }
}