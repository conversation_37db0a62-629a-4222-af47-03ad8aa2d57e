using AEPExplorer.Model.Request;
using AEPExplorer.Service;
using AEPExplorer.Service.Commands.Import;
using AEPExplorer.Service.Elasticsearch;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ImportController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Use this endpoint to import AEP Data Structure
        /// </summary>
        [HttpPost]
        [Route("aep-data-structure/{isOldVersion}")]
        public async Task<ActionResult> ImportAepDataStructure([FromRoute] bool isOldVersion)
        {
            await mediator.Send(new ImportAepDataStructureCommand {IsOldVersion = isOldVersion});

            return Ok();
        }
        
        /// <summary>
        /// Use this endpoint to delete AEP Data Structure
        /// </summary>
        [HttpDelete]
        [Route("aep-data-structure")]
        public async Task<ActionResult> DeleteAepDataStructure()
        {
            await mediator.Send(new DeleteAepDataStructureCommand());

            return Ok();
        }
        
        /// <summary>
        /// Use this endpoint to import Daily Aggregated Data
        /// </summary>
        [HttpPost]
        [Route("data")]
        public async Task<ActionResult> ImportData([FromBody] ImportQuery query)
        {
            ElasticsearchGeneralService.IsImportEnabled = true;
            await mediator.Send(new ImportData { ImportQuery = query });

            return Ok();
        }

        /// <summary>
        /// Use this endpoint to import Daily Aggregated Alarms Data
        /// </summary>
        [HttpPost]
        [Route("alarms-data")]
        public async Task<ActionResult> ImportAlarmsData([FromBody] ImportQuery query)
        {
            ElasticsearchGeneralService.IsImportEnabled = true;
            await mediator.Send(new ImportAlarmsData() { ImportQuery = query });

            return Ok();
        }        
        
        /// <summary>
        /// Use this endpoint to import Ice Estimate Data
        /// </summary>
        [HttpPost]
        [Route("ice-estimate-data")]
        public async Task<ActionResult> ImportIceEstimateData([FromBody] ImportQuery query)
        {
            ElasticsearchGeneralService.IsImportEnabled = true;
            await mediator.Send(new ImportIceEstimateData { ImportQuery = query });

            return Ok();
        }
        
        /// <summary>
        /// Use this endpoint to stop import
        /// </summary>
        [HttpPost]
        [Route("stop")]
        public ActionResult StopImportData()
        {
            ElasticsearchGeneralService.IsImportEnabled = false;

            return Ok();
        }

        /// <summary>
        /// Use this endpoint to import EPC data
        /// </summary>
        [HttpPost]
        [Route("epc")]
        public async Task<ActionResult> ImportEpc([FromBody] ImportQuery query)
        {
            ElasticsearchGeneralService.IsImportEnabled = true;
            await mediator.Send(new ImportEpcCommand {ImportQuery = query});

            return Ok();
        }
        
        /// <summary>
        /// Use this endpoint to delete EPC data
        /// </summary>
        [HttpDelete]
        [Route("epc")]
        public async Task<ActionResult> DeleteEpc()
        {
            await mediator.Send(new DeleteEpcCommand());

            return Ok();
        }
        
        /// <summary>
        /// Use this endpoint to delete raw data
        /// </summary>
        [HttpDelete]
        [Route("data")]
        public async Task<ActionResult> DeleteData([FromBody] ImportQuery query)
        {
            ElasticsearchGeneralService.IsImportEnabled = true;
            await mediator.Send(new DeleteData {ImportQuery = query});

            return Ok();
        }
        
        /// <summary>
        /// Use this endpoint to delete EPC data
        /// </summary>
        [HttpDelete]
        [Route("alarms-data")]
        public async Task<ActionResult> DeleteAlarmsData()
        {
            await mediator.Send(new DeleteAlarmsDataCommand());

            return Ok();
        }

        /// <summary>
        /// Use this endpoint to import EPC data
        /// </summary>
        [HttpPost]
        [Route("tpc")]
        public async Task<ActionResult> ImportTpc([FromBody] ImportQuery query)
        {
            ElasticsearchGeneralService.IsImportEnabled = true;
            await mediator.Send(new ImportTpcCommand {ImportQuery = query});

            return Ok();
        }
        
        /// <summary>
        /// Use this endpoint to delete EPC data
        /// </summary>
        [HttpDelete]
        [Route("tpc")]
        public async Task<ActionResult> DeleteTpc()
        {
            await mediator.Send(new DeleteTpcCommand());

            return Ok();
        }
        
        /// <summary>
        /// Use this endpoint to import Daily Aggregated Power Boost Data
        /// </summary>
        [HttpPost]
        [Route("power-boost-data")]
        public async Task<ActionResult> ImportPowerBoostData([FromBody] ImportQuery query)
        {
            ElasticsearchGeneralService.IsImportEnabled = true;
            await mediator.Send(new ImportPowerBoostData { ImportQuery = query });

            return Ok();
        }  
        
        /// <summary>
        /// Use this endpoint to update Turbines, Sites, Countries, Regions, Customers, Platforms and Models that have Power Boost
        /// </summary>
        [HttpPost]
        [Route("update-structure")]
        public async Task<ActionResult> UpdateStructureData([FromBody] ImportQuery query)
        {
            await mediator.Send(new UpdateStructureDataCommand { ImportQuery = query });

            return Ok();
        }
        
        /// <summary>
        /// Use this endpoint to update Turbines, Sites, Countries, Regions, Customers, Platforms and Models that have Power Boost
        /// </summary>
        [HttpPost]
        [Route("update-structure-power-boost")]
        public async Task<ActionResult> UpdateStructureDataPowerBoost([FromBody] ImportQuery query)
        {
            await mediator.Send(new UpdateStructureDataPowerBoostCommand { ImportQuery = query });

            return Ok();
        }

        /// <summary>
        /// Use this endpoint to import Turbine Status Data
        /// </summary>
        [HttpPost]
        [Route("turbine-status")]
        public async Task<ActionResult> ImportTurbineStatus([FromBody] ImportQuery query)
        {
            await mediator.Send(new ImportTurbineStatusCommand { ImportQuery = query });
            return Ok();
        }
    }
}
