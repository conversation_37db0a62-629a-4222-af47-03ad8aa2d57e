using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Filter;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class FilterController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Returns data for Customer list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("customer")]
        public async Task<ActionResult<List<ValueLabel>>> GetCustomer(FilterQuery query)
        {
            var result = await mediator.Send(new CustomerFilterQuery { Query = query });
            return result;
        }
        
        /// <summary>
        /// Returns data for Region list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("region")]
        public async Task<ActionResult<List<ValueLabel>>> GetRegion(FilterQuery query)
        {
            var result = await mediator.Send(new RegionFilterQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Country list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("country")]
        public async Task<ActionResult<List<ValueLabel>>> GetCountry(FilterQuery query)
        {
            var result = await mediator.Send(new CountryFilterQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Site list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("site")]
        public async Task<ActionResult<List<ValueLabel>>> GetSite(FilterQuery query)
        {
            var result = await mediator.Send(new SiteFilterQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Turbine list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("turbine")]
        public async Task<ActionResult<List<ValueLabel>>> GetTurbine(FilterQuery query)
        {
            var result = await mediator.Send(new TurbineFilterQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for LocationTypeNames list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("location-type-name")]
        public async Task<ActionResult<List<ValueIntLabel>>> GetLocationTypeName(FilterQuery query)
        {
            var result = await mediator.Send(new LocationTypeNameFilterQuery { Query = query });
            return result;
        }
        
        /// <summary>
        /// Returns data for Platform list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("platform")]
        public async Task<ActionResult<List<ValueLabelWithChildren>>> GetPlatform(FilterQuery query)
        {
            var result = await mediator.Send(new TurbinePlatformFilterQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Model list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("model")]
        public async Task<ActionResult<List<ValueLabelWithChildren>>> GetModel(FilterQuery query)
        {
            var result = await mediator.Send(new TurbineModelFilterQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Category list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("category")]
        public async Task<ActionResult<List<ValueLabelWithChildren>>> GetCategory(FilterQuery query)
        {
            var result = await mediator.Send(new CategoryFilterQuery());
            return result;
        }

        /// <summary>
        /// Returns selected categories
        /// </summary>
        /// <returns>List of selected categories</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("selected-category")]
        public async Task<ActionResult<List<ValueLabel>>> GetSelectedCategory([FromBody] List<Guid> categories)
        {
            var result = await mediator.Send(new SelectedCategoryQuery { Categories = categories});
            return result;
        }

        /// <summary>
        /// Returns default date range
        /// </summary>
        /// <returns>From and to dates</returns>
        [HttpGet]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("date-range")]
        public async Task<ActionResult<DateRangeModel>> GetDefaultDateRange()
        {
            var result = await mediator.Send(new DefaultDateRangeFilterQuery());
            return result;
        }
    }
}