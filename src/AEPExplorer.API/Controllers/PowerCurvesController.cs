using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Epc;
using AEPExplorer.Service.Queries.Tpc;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class PowerCurvesController(IMediator mediator) : ControllerBase
{
    /// <summary>
    /// Returns data for epc selection
    /// </summary>
    /// <returns>List of epc dates</returns>
    [HttpPost]
    [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
    [Route("epc-list")]
    public async Task<ActionResult<List<ChildStructure<ValueIntLabel>>>> GetPowerCurvesEpcList(Query query)
    {
        var result = await mediator.Send(new PowerCurvesEpcListQuery { Query = query });
        return result;
    }


    /// <summary>
    /// Returns data for tpc selection
    /// </summary>
    /// <returns>List of tpc dates</returns>
    [HttpPost]
    [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
    [Route("tpc-list")]
    public async Task<ActionResult<List<ChildStructure<ValueLabel>>>> GetTheoreticalPowerCurves(Query query)
    {
        var result = await mediator.Send(new PowerCurvesTpcListQuery { Query = query });
        return result;
    }

    /// <summary>
    /// Returns EPC lines
    /// </summary>
    /// <returns>EPC coordinates</returns>
    [HttpPost]
    [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
    [Route("epc-lines")]
    public async Task<ActionResult<List<EmpiricalPowerCurveLine>>> GetEpcLines(EpcQuery epcQuery)
    {
        var result = await mediator.Send(new EpcLinesQuery { EpcIds = epcQuery.EpcIds });
        return result;
    }

    /// <summary>
    /// Returns 10 minutes power and wind data
    /// </summary>
    /// <returns>Dot coordinates</returns>
    [HttpPost]
    [Route("epc-dots")]
    public async Task<ActionResult<List<FullPerformancePowerData>>> GetEpcDots(Query query)
    {
        var result = await mediator.Send(new FullPerformancePowerQuery { Query = query });
        return result;
    }

    /// <summary>
    /// Returns TPC lines
    /// </summary>
    /// <returns>TPC coordinates</returns>
    [HttpPost]
    [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
    [Route("tpc-lines")]
    public async Task<ActionResult<List<TheoreticalPowerCurveLine>>> GetTpcLines(TpcQuery tpcQuery)
    {
        var result = await mediator.Send(new TpcLinesQuery { TurbineIds = tpcQuery.TurbineIds });
        return result;
    }
}