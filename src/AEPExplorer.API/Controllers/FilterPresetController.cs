using AEPExplorer.Model.Domain;
using AEPExplorer.Model.Request;
using AEPExplorer.Service.Commands;
using AEPExplorer.Service.Commands.FilterPreset;
using AEPExplorer.Service.Queries.FilterPreset;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class FilterPresetController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Get list of filter presets
        /// </summary>
        /// <returns>List of Filter preset objects</returns>
        [HttpGet]
        public async Task<ActionResult<List<FilterPreset>>> GetFilterPresets()
        {            
            return await mediator.Send(new FilterPresetQuery { UserId = HttpContext.User.GetOId() });
        }

        /// <summary>
        /// Get list of filter presets
        /// </summary>
        /// <returns>List of Filter preset objects</returns>
        [HttpGet]
        [Route("global")]
        public async Task<ActionResult<List<FilterPreset>>> GetGlobalFilterPresets()
        {
            return await mediator.Send(new GlobalFilterPresetQuery {});
        }

        /// <summary>
        /// Add Filter Preset
        /// </summary>
        /// <returns>Guid</returns>
        [HttpPost]
        public async Task<ActionResult<Guid>> AddFilterPreset(AddFilterPresetModel filterPresetModel)
        {
            return await mediator.Send(new CreateFilterPresetCommand
            {
                Query = filterPresetModel.Query,
                Name = filterPresetModel.Name,
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Add/Remove Filter Preset From Favorites
        /// </summary>
        /// <returns>Guid</returns>
        [HttpPost]
        [Route("favorite")]
        public async Task<ActionResult<Guid?>> UpdateFavoriteFilterPreset(FavoriteFilterPresetModel favoriteFilterPresetModel)
        {
            return await mediator.Send(new UpdateFavoriteFilterPresetCommand
            {
                Id = favoriteFilterPresetModel.Id,
                Favorite = favoriteFilterPresetModel.Favorite,
                UserId = HttpContext.User.GetOId()
            });
        }

        /// <summary>
        /// Update Filter Preset
        /// </summary>
        /// <returns>Guid</returns>
        [HttpPut]
        public async Task<ActionResult<Guid>> UpdateFilterPreset(UpdateFilterPresetModel filterPresetModel)
        {
            var result = await mediator.Send(new UpdateFilterPresetCommand
            {
                Id = filterPresetModel.Id,
                Name = filterPresetModel.Name,
                UserId = HttpContext.User.GetOId()
            });

            if (result == null)
            {
                return NotFound();
            }

            return result;
        }

        /// <summary>
        /// Delete Filter Preset
        /// </summary>
        /// <returns>true/false</returns>
        [HttpDelete]
        public async Task<ActionResult<bool>> DeleteFilterPreset(Guid id)
        {
            var result = await mediator.Send(new DeleteFilterPresetCommand
            {
                Id = id,
                UserId = HttpContext.User.GetOId()
            });

            if (!result)
            {
                return NotFound();
            }

            return result;
        }
    }
}
