using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Ice;
using AEPExplorer.Service.Queries.Run;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using Nest;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class RunController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Returns data for Run topic
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("total")]
        public async Task<ActionResult<List<RunTotal>>> GetTotal(Query query)
        {
            var result = await mediator.Send(new RunTotalQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns cumulative data for Run topic
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("cumulative")]
        public async Task<ActionResult<ItemsDates>> GetCumulative(Query query)
        {
            var result = await mediator.Send(new RunCumulativeQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns aggregated sum per interval for Run topic
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("interval/{dateInterval}")]
        public async Task<ActionResult<ItemsIntervals>> GetInterval([FromBody] Query query, [FromRoute] DateInterval dateInterval)
        {
            var result = await mediator.Send(new RunIntervalQuery { Query = query, DateInterval = dateInterval });
            return result;
        }
    }
}
