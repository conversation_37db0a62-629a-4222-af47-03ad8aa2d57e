using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Epc;
using AEPExplorer.Service.Queries.Ice.EstimationLosses;
using AEPExplorer.Service.Queries.Ice.TurbineLosses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class IceController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Returns data for Ice Data Analytics - Total Losses
        /// </summary>
        /// <returns>Total losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("turbine-losses/analytics-losses-total")]
        public async Task<ActionResult<List<NameValuePercentageWithChildren>>> GetIceDataAnalyticsLossesTotal(Query query)
        {
            var result = await mediator.Send(new IceDataAnalyticsLossesTotalQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Ice Data Analytics - Timeline Losses
        /// </summary>
        /// <returns>Timeline losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("turbine-losses/analytics-losses-timeline")]
        public async Task<ActionResult<List<IceLossesWithDate>>> GetIceDataAnalyticsLossesTimeline(Query query)
        {
            var result = await mediator.Send(new IceDataAnalyticsLossesTimelineQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Ice Data Analytics - Total Losses
        /// </summary>
        /// <returns>Total hours</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("turbine-losses/analytics-losses-hours")]
        public async Task<ActionResult<List<NameValue>>> GetIceDataAnalyticsLossesHours(Query query)
        {
            var result = await mediator.Send(new IceDataAnalyticsLossesHoursQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for turbine selection
        /// </summary>
        /// <returns>List of turbines</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("estimation-losses/epc-turbines")]
        public async Task<ActionResult<List<ValueLabel>>> GetEpcData(Query query)
        {
            var result = await mediator.Send(new EpcTurbineQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for epc selection
        /// </summary>
        /// <returns>List of epc dates</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("estimation-losses/epc-list")]
        public async Task<ActionResult<List<ValueIntLabel>>> GetEpcList(Query query)
        {
            var result = await mediator.Send(new EpcListQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns EPC lines for chart
        /// </summary>
        /// <returns>EPC coordinates</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("estimation-losses/epc-lines")]
        public async Task<ActionResult<List<EmpiricalPowerCurveLine>>> GetEpcLines(EpcQuery epcQuery)
        {
            var result = await mediator.Send(new EpcLinesQuery { EpcIds = epcQuery.EpcIds });
            return result;
        }

        /// <summary>
        /// Returns EPC dots for chart
        /// </summary>
        /// <returns>Dot coordinates</returns>
        [HttpPost]
        [Route("estimation-losses/epc-dots")]
        public async Task<ActionResult<List<IceEstimatesPowerData>>> GetEpcDots(Query query)
        {
            var result = await mediator.Send(new IceEstimatesPowerQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Ice Data Analytics - Total Losses
        /// </summary>
        /// <returns>Total losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("estimation-losses/analytics-losses-total")]
        public async Task<ActionResult<List<NameValuePercentageWithChildren>>> GetIceDataAnalyticsEstimationLossesTotal(Query query)
        {
            var result = await mediator.Send(new IceDataAnalyticsEstimationLossesTotalQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Ice Data Analytics - Timeline Losses
        /// </summary>
        /// <returns>Timeline losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("estimation-losses/analytics-losses-timeline")]
        public async Task<ActionResult<List<IceLossesWithDate>>> GetIceDataAnalyticsEstimationLossesTimeline(Query query)
        {
            var result = await mediator.Send(new IceDataAnalyticsEstimationLossesTimelineQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Ice Data Analytics - Duration
        /// </summary>
        /// <returns>Total duration</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("estimation-losses/distribution")]
        public async Task<ActionResult<List<NameValue>>> GetIceDataAnalyticsDistribution(Query query)
        {
            var result = await mediator.Send(new IceDataAnalyticsDistributionQuery { Query = query });
            return result;
        }


        /// <summary>
        /// Returns data for Ice Data Analytics - Period
        /// </summary>
        /// <returns>List of periods</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("estimation-losses/distribution-over-time")]
        public async Task<ActionResult<IceDataAnalyticsDistributionOverTime>> GetIceDataAnalyticsPeriod(Query query)
        {
            var result = await mediator.Send(new IceDataAnalyticsDistributionOverTimeQuery { Query = query });
            return result;
        }
    }
}
