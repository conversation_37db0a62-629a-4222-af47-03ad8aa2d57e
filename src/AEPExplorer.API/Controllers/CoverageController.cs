using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Coverage;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class CoverageController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Return data for average coverage
        /// </summary>
        /// <returns>Value for data coverage and method coverage</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        public async Task<ActionResult<CoverageModel>> GetAverageCoverage(Query query)
        {
            var result = await mediator.Send(new CoverageQuery { Query = query });
            return result;
        }
        
        /// <summary>
        /// Return data for average coverage
        /// </summary>
        /// <returns>Value for data coverage and method coverage</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("power-boost")]
        public async Task<ActionResult<PowerBoostCoverageModel>> GetPowerBoostAverageCoverage(Query query)
        {
            var result = await mediator.Send(new PowerBoostCoverageQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for timeline coverage for each selected item
        /// </summary>
        /// <returns>Values for data coverage and method coverage by each day</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("per-item")]
        public async Task<ActionResult<List<CoverageItemModel>>> GetItemBarchartCoverage(Query query)
        {
            var result = await mediator.Send(new CoverageItemQuery { Query = query });
            return result;
        }
    }
}
