using System.IO.Compression;
using AEPExplorer.Model.Enums;
using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Table;
using AEPExplorer.Service.Queries.Table.Export;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class TableController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show customers with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("customer")]
        public async Task<ActionResult<TableTotalModelResponse<TableTotalLossesCustomerModel>>> GetCustomerData([FromBody] Query query)
        {
            var result = await mediator.Send(new TableTotalLossesCustomerQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show customers with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("customer/{id}")]
        public async Task<ActionResult<TableCategories>> GetCustomerCategories([FromRoute] Guid id, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableCategoriesQuery { Id = id, Query = query, FilterBy = FilterByEnum.Customer });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show customers with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("customer/{id:guid}/{categoryId:guid}")]
        public async Task<ActionResult<TableSubcategories>> GetCustomerSubcategories([FromRoute] Guid id, [FromRoute] Guid categoryId, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableSubcategoriesQuery { Id = id, CategoryId = categoryId, Query = query, FilterBy = FilterByEnum.Customer });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("region")]
        public async Task<ActionResult<TableTotalModelResponse<TableTotalLossesRegionModel>>> GetRegionData([FromBody] Query query)
        {
            var result = await mediator.Send(new TableTotalLossesRegionQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("region/{id}")]
        public async Task<ActionResult<TableCategories>> GetRegionCategories([FromRoute] Guid id, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableCategoriesQuery { Id = id, Query = query, FilterBy = FilterByEnum.Region });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("region/{id:guid}/{categoryId:guid}")]
        public async Task<ActionResult<TableSubcategories>> GetRegionSubcategories([FromRoute] Guid id, [FromRoute] Guid categoryId, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableSubcategoriesQuery { Id = id, CategoryId = categoryId, Query = query, FilterBy = FilterByEnum.Region });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("country")]
        public async Task<ActionResult<TableTotalModelResponse<TableTotalLossesCountryModel>>> GetCountryData([FromBody] Query query)
        {
            var result = await mediator.Send(new TableTotalLossesCountryQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("country/{id}")]
        public async Task<ActionResult<TableCategories>> GetCountryCategories([FromRoute] Guid id, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableCategoriesQuery { Id = id, Query = query, FilterBy = FilterByEnum.Country });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("country/{id:guid}/{category:guid}")]
        public async Task<ActionResult<TableSubcategories>> GetCountrySubcategories([FromRoute] Guid id, [FromRoute] Guid category, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableSubcategoriesQuery { Id = id, CategoryId = category, Query = query, FilterBy = FilterByEnum.Country });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("site")]
        public async Task<ActionResult<TableTotalModelResponse<TableTotalLossesSiteModel>>> GetSiteData([FromBody] Query query)
        {
            var result = await mediator.Send(new TableTotalLossesSiteQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("site/{id}")]
        public async Task<ActionResult<TableCategories>> GetSiteCategories([FromRoute] Guid id, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableCategoriesQuery { Id = id, Query = query, FilterBy = FilterByEnum.Site });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("site/{id:guid}/{category:guid}")]
        public async Task<ActionResult<TableSubcategories>> GetSiteSubcategories([FromRoute] Guid id, [FromRoute] Guid category, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableSubcategoriesQuery { Id = id, CategoryId = category, Query = query, FilterBy = FilterByEnum.Site });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("turbine")]
        public async Task<ActionResult<TableTotalModelResponse<TableTotalLossesTurbineModel>>> GetTurbineData([FromBody] Query query)
        {
            var result = await mediator.Send(new TableTotalLossesTurbineQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("turbine/{id}")]
        public async Task<ActionResult<TableCategories>> GetTurbineCategories([FromRoute] Guid id, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableCategoriesQuery { Id = id, Query = query, FilterBy = FilterByEnum.Turbine });
            return result;
        }

        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions with actual production and losses</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.SHORT_CACHING)]
        [Route("turbine/{id:guid}/{category:guid}")]
        public async Task<ActionResult<TableSubcategories>> GetTurbineSubcategories([FromRoute] Guid id, [FromRoute] Guid category, [FromBody] Query query)
        {
            var result = await mediator.Send(new TableSubcategoriesQuery { Id = id, CategoryId = category, Query = query, FilterBy = FilterByEnum.Turbine });
            return result;
        }
        
        /// <summary>
        /// Returns region data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/customer")]
        public async Task<ActionResult> ExportCustomerTableToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new TableTotalLossesCustomerExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }

        /// <summary>
        /// Returns region data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/region")]
        public async Task<ActionResult> ExportRegionTableToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new TableTotalLossesRegionExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }

        /// <summary>
        /// Returns country data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/country")]
        public async Task<ActionResult> ExportCountryTableToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new TableTotalLossesCountryExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }

        /// <summary>
        /// Returns site data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/site")]
        public async Task<ActionResult> ExportSiteTableToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new TableTotalLossesSiteExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }

        /// <summary>
        /// Returns turbine data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/turbine")]
        public async Task<ActionResult> ExportTurbineTableToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new TableTotalLossesTurbineExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }
    }
}