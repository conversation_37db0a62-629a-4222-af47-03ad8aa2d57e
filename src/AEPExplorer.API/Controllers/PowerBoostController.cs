using AEPExplorer.Model.Request;
using AEPExplorer.Model.Response;
using AEPExplorer.Service.Queries.Filter;
using AEPExplorer.Service.Queries.PowerBoost;
using AEPExplorer.Service.Queries.PowerBoost.Export;
using AEPExplorer.Service.Queries.PowerBoost.Table;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class PowerBoostController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// Return data for Turbine list
        /// </summary>
        /// <returns>List of Id-Value pairs</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("turbine")]
        public async Task<ActionResult<List<ValueLabel>>> GetTurbine(Query query)
        {
            var result = await mediator.Send(new BoostTurbineQuery { Query = query });
            return result;
        }
        
        /// <summary>
        /// Returns data for Boost Performance
        /// </summary>
        /// <returns>List of Expected, Efficiency and Utilisation averages and timelines</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("boost-performance")]
        public async Task<ActionResult<List<BoostPerformance>>> GetBoostPerformance([FromBody] Query query)
        {
            var result = await mediator.Send(new BoostPerformanceQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Boost Energy
        /// </summary>
        /// <returns>List of Boost Time, Boost Counter, Boost Estimated and Boost Actual</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("boost-energy-bar")]
        public async Task<ActionResult<List<NameValuePercentage>>> GetBoostEnergyBars([FromBody] Query query)
        {
            var result = await mediator.Send(new BoostEnergyBarsQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Boost Energy Cumulative Timeline
        /// </summary>
        /// <returns>List of Boost Actual and Boost Energy timelines</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("boost-energy-cumulative-timeline")]
        public async Task<ActionResult<List<BoostEnergy>>> GetBoostEnergyCumulativeTimeline([FromBody] Query query)
        {
            var result = await mediator.Send(new BoostEnergyCumulativeTimelineQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Boost Energy Timeline
        /// </summary>
        /// <returns>List of Boost Actual and Boost Available timelines</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("boost-energy-timeline")]
        public async Task<ActionResult<List<BoostEnergy>>> GetBoostEnergyTimeline([FromBody] Query query)
        {
            var result = await mediator.Send(new BoostEnergyTimelineQuery { Query = query });
            return result;
        }

        /// <summary>
        /// Returns data for Boost Criteria fulfilled and not fulfilled Timeline
        /// </summary>
        /// <returns>List of Boost Actual and Boost Available timelines</returns>
        [HttpPost]
        [OutputCache(PolicyName = "OutputCacheWithAuthPolicy", Duration = Constants.LONG_CACHING)]
        [Route("boost-criteria/{fulfilled:bool}")]
        public async Task<ActionResult<List<BoostCriteria>>> GetCriteriaTimeline([FromRoute] bool fulfilled, [FromBody] Query query)
        {
            var result = fulfilled ? await mediator.Send(new BoostCriteriaFulfilledQuery { Query = query}) : await mediator.Send(new BoostCriteriaNotFulfilledQuery { Query = query});
            return result;
        }
        
        /// <summary>
        /// Returns turbine data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/turbine")]
        public async Task<ActionResult> ExportPowerBoostTurbineToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new PowerBoostTurbineExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }
        
        /// <summary>
        /// Returns site data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/site")]
        public async Task<ActionResult> ExportPowerBoostSiteToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new PowerBoostSiteExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }
        
        /// <summary>
        /// Returns country data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/country")]
        public async Task<ActionResult> ExportPowerBoostCountryToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new PowerBoostCountryExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }
        
        /// <summary>
        /// Returns region data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/region")]
        public async Task<ActionResult> ExportPowerBoostRegionToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new PowerBoostRegionExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }
        
        /// <summary>
        /// Returns customer data
        /// </summary>
        /// <returns>csv file</returns>
        [HttpPost]
        [Route("export/customer")]
        public async Task<ActionResult> ExportPowerBoostCustomerToCsv([FromBody] ExportQuery query)
        {
            var result = await mediator.Send(new PowerBoostCustomerExportQuery { Query = query });
            return File(result.file, "application/zip", result.fileName);
        }
        
        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show turbines</returns>
        [HttpPost]
        [Route("table/turbine")]
        public async Task<ActionResult<PowerBoostTableTotalModelResponse<BoostTableTurbineModel>>> GetTurbineData([FromBody] Query query)
        {
            var result = await mediator.Send(new BoostTableTurbineQuery { Query = query });
            return result;
        }
        
        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show sites</returns>
        [HttpPost]
        [Route("table/site")]
        public async Task<ActionResult<PowerBoostTableTotalModelResponse<BoostTableSiteModel>>> GetSiteData([FromBody] Query query)
        {
            var result = await mediator.Send(new BoostTableSiteQuery { Query = query });
            return result;
        }
        
        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show countries</returns>
        [HttpPost]
        [Route("table/country")]
        public async Task<ActionResult<PowerBoostTableTotalModelResponse<BoostTableCountryModel>>> GetCountryData([FromBody] Query query)
        {
            var result = await mediator.Send(new BoostTableCountryQuery { Query = query });
            return result;
        }
        
        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show regions</returns>
        [HttpPost]
        [Route("table/region")]
        public async Task<ActionResult<PowerBoostTableTotalModelResponse<BoostTableRegionModel>>> GetSRegionData([FromBody] Query query)
        {
            var result = await mediator.Send(new BoostTableRegionQuery { Query = query });
            return result;
        }
        
        /// <summary>
        /// Return data for the table 
        /// </summary>
        /// <returns>Dataset for the table to show customers</returns>
        [HttpPost]
        [Route("table/customer")]
        public async Task<ActionResult<PowerBoostTableTotalModelResponse<BoostTableCustomerModel>>> GetCustomerData([FromBody] Query query)
        {
            var result = await mediator.Send(new BoostTableCustomerQuery { Query = query });
            return result;
        }
    }
}