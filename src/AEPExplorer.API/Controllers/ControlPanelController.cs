using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using AEPExplorer.Service.Queries.ControlPanel;

namespace AEPExplorer.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ControlPanelController(IOutputCacheStore cache, IMediator mediator) : ControllerBase
    {
        private const string API_VERSION = "1.3.0";

        /// <summary>
        /// Return version
        /// </summary>
        /// <returns>Version number</returns>
        [HttpGet]
        public string GetVersion()
        {
            return API_VERSION;
        }

        /// <summary>
        /// Use this endpoint to delete cached data
        /// </summary>
        [HttpDelete]
        [Route("cache")]
        public async Task<ActionResult> DeleteCache(CancellationToken cancellationToken)
        {
            await cache.EvictByTagAsync("ResponseTag", cancellationToken);
            return Ok();
        }

        /// <summary>
        /// Return data from PostgreSQL
        /// </summary>
        /// <returns>json</returns>
        [HttpGet]
        [Route("postgres/{tableName}")]
        public async Task<ActionResult<string>> GetPostgreSqlData(string tableName)
        {
            var result = await mediator.Send(new PostgreSqlDataQuery { TableName = tableName });
            return result;
        }
        
        /// <summary>
        /// Return data from PostgreSQL
        /// </summary>
        /// <returns>json</returns>
        [HttpDelete]
        [Route("postgres/customer")]
        public async Task<ActionResult<bool>> CustomerDown()
        {
            var result = await mediator.Send(new PostgreSqlCustomerDownQuery());
            return result;
        }
    }
}