{
  "ConnectionStrings": {
    "DefaultConnection": "User ID=postgres;Password=*********;Server=localhost;Port=5432;Database=AEPExplorer;Pooling=true;Command Timeout=60;Timeout=30;",
  },
  "Elasticsearch": {
    "Username": "elastic",
    "Password": "password",
    "Url": "http://localhost:9200"
  },
  "Databricks": {
    "Catalog": "aepexplorerqlt",
    "WorkspaceUrl": "https://adb-5380751825034360.0.azuredatabricks.net",
    "Token": "**************************************",
    "WarehouseId": "d24040ba9f59a482"
  },
  "AzureAdKeys-Issuers": "https://sts.windows.net/",
  "AzureAdKeys-Issuers-v2": "https://login.microsoftonline.com/",
  "AzureAdKeys-DiscoveryEndPoint": "https://login.microsoftonline.com/common/discovery/keys",
  "Audience": "223421d8-9860-4bcd-b965-176a7e8e2685",
  "Tenant": "12f921d8-f30d-4596-a652-7045b338485a"
}
