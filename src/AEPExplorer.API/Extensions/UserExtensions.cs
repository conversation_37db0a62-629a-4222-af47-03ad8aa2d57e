using AEPExplorer.API.Exceptions;
using System.Security.Claims;

namespace AEPExplorer.API.Controllers
{
    public static class UserExtensions 
    {
        public static Guid GetOId(this ClaimsPrincipal principal)
        {            
            var oid = principal?.Claims?.SingleOrDefault(p => p.Type == "http://schemas.microsoft.com/identity/claims/objectidentifier")?.Value;
            if (oid == null)
            {
                throw new NotFoundException($"User not valid");
            }

            if (Guid.TryParse(oid, out var oidGuid))
            {
                return oidGuid;
            }
            else
            {
                throw new NotFoundException($"Unable to convert {oid} to a Guid");                
            }
        }

        public static string GetFullName(this ClaimsPrincipal principal)
        {
            var surname = principal?.Claims?.SingleOrDefault(p => p.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname")?.Value;
            var givenname = principal?.Claims?.SingleOrDefault(p => p.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname")?.Value;

            return $"{givenname} {surname}";
        }
    }
}
