using AEPExplorer.Model.Elasticsearch;
using AEPExplorer.Service.Elasticsearch;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Nest;

namespace AEPExplorer.API.Extensions
{
    public static class ElasticsearchExtensions
    {
        public static void AddElasticsearch(this IServiceCollection services, IConfiguration configuration)
        {
            var url = configuration["Elasticsearch:Url"];
            var username = configuration["Elasticsearch:Username"];
            var password = configuration["Elasticsearch:Password"];

            if (url == null)
            {
                return;
            }

            var settings = new ConnectionSettings(new Uri(url))
                .DefaultMappingFor<AepReadData>(x=>x.IndexName(ElasticsearchConstants.INDEX_NAME))
                .DefaultIndex(ElasticsearchConstants.INDEX_NAME)
                .BasicAuthentication(username, password)
                .DisableDirectStreaming()
                .RequestTimeout(TimeSpan.FromMinutes(ElasticsearchConstants.TIMEOUT));

            var client = new ElasticClient(settings);

            client.Indices.UpdateSettings(ElasticsearchConstants.INDEX_NAME, s => s
                .IndexSettings(i => i
                    .Setting(UpdatableIndexSettings.MaxResultWindow, ElasticsearchConstants.BUCKET_SIZE)));

            client.Cluster.PutSettings(s => s
                .Persistent(p => p
                    .Add("search.max_buckets", ElasticsearchConstants.BUCKET_SIZE)));

            services.TryAddSingleton<IElasticClient>(client);
        }
    }
}
