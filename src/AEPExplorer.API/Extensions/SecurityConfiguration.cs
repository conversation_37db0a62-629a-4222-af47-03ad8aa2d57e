using AEPExplorer.API.Exceptions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System.Security.Cryptography;

namespace AEPExplorer.API.Extensions
{
    public static class SecurityConfiguration
    {
        private static List<RsaSecurityKey>? PublicKeys { get; set; }

        public static IServiceCollection AddSecurityConfiguration(this IServiceCollection services, IConfiguration configuration)
        {
            GetPublicKeyForIssuers(configuration["AzureAdKeys-DiscoveryEndPoint"]!);

            var issuers = new[]
            {
                $"{configuration["AzureAdKeys-Issuers"]}{configuration["Tenant"]}/",
                $"{configuration["AzureAdKeys-Issuers-v2"]}{configuration["Tenant"]}/v2.0"
            };
            var audiences = configuration["Audience"]!.Split(",");

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(cfg =>
            {
                cfg.RequireHttpsMetadata = true;
                cfg.SaveToken = false;
                cfg.Authority = issuers[0];
                cfg.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidIssuers = issuers,
                    ValidateIssuerSigningKey = true,
                    RequireExpirationTime = true,
                    RequireSignedTokens = true,
                    ValidateAudience = true,
                    ValidAudiences = audiences,
                    ValidateIssuer = true,
                    ValidateLifetime = true,
                    IssuerSigningKeys = PublicKeys
                };
            });

            return services;
        }

        private static void GetPublicKeyForIssuers(string issuers)
        {
            string[] values = issuers.Split(',');
            Parallel.ForEach(values, issuer =>
            {
                using HttpClient client = new();
                try
                {
                    string responseString = client.GetStringAsync(new Uri(issuer)).ConfigureAwait(false).GetAwaiter().GetResult();
                    if (!string.IsNullOrEmpty(responseString))
                    {
                        ConstructSecurityKeysFromPublicKeys(responseString);
                    }
                }
                catch (TaskCanceledException ex)
                {
                    throw new NotFoundException($"{nameof(GetPublicKeyForIssuers)} method failed to construct keys: {ex.Message}");
                }
            });
        }

        private static void ConstructSecurityKeysFromPublicKeys(string responseString)
        {
            KeyList list = JsonConvert.DeserializeObject<KeyList>(responseString) ?? new KeyList();

            foreach (var key in list.Keys)
            {
                if (PublicKeys == null)
                {
                    PublicKeys = new List<RsaSecurityKey>
                    {
                        new RsaSecurityKey(new RSAParameters
                            {
                                Modulus = Encoding.ASCII.GetBytes(key.N),
                                Exponent = Encoding.ASCII.GetBytes(key.E)
                            })
                    };
                }
                else
                {
                    PublicKeys.Add(new RsaSecurityKey(new RSAParameters
                    {
                        Modulus = Encoding.ASCII.GetBytes(key.N),
                        Exponent = Encoding.ASCII.GetBytes(key.E)
                    }));
                }
            }
        }

        internal class KeyList
        {
            public List<Key> Keys { get; set; } = new List<Key>();
        }

        public class Key
        {
            public string Kty { get; set; } = string.Empty;
            public string Use { get; set; } = string.Empty;
            public string Kid { get; set; } = string.Empty;
            public string X5t { get; set; } = string.Empty;
            public string N { get; set; } = string.Empty;
            public string E { get; set; } = string.Empty;
            public List<string> X5c { get; set; } = new List<string>();
        }
    }
}
