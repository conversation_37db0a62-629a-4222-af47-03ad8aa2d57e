using AEPExplorer.Service;
using Microsoft.AspNetCore.OutputCaching;

namespace AEPExplorer.API.Extensions;

public class OutputCacheWithAuthPolicy : IOutputCachePolicy
{
    ValueTask IOutputCachePolicy.CacheRequestAsync(OutputCacheContext context, CancellationToken cancellationToken)
    {
        var attemptOutputCaching = AttemptOutputCaching(context);
        context.EnableOutputCaching = true;
        context.AllowCacheLookup = attemptOutputCaching;
        context.AllowCacheStorage = attemptOutputCaching;
        context.AllowLocking = true;
        context.CacheVaryByRules.QueryKeys = "*";
        
        var body = GetRawBodyAsync(context.HttpContext.Request);
        context.CacheVaryByRules.CacheKeyPrefix = ConvertExtensions.Md5(body);
        return ValueTask.CompletedTask;
    }

    ValueTask IOutputCachePolicy.ServeFromCacheAsync(OutputCacheContext context, CancellationToken cancellationToken)
    {
        return ValueTask.CompletedTask;
    }

    ValueTask IOutputCachePolicy.ServeResponseAsync(OutputCacheContext context, CancellationToken cancellationToken)
    {
        context.AllowCacheStorage = true;
        return ValueTask.CompletedTask;
    }

    private static bool AttemptOutputCaching(OutputCacheContext context)
    {
        var request = context.HttpContext.Request;
        return HttpMethods.IsGet(request.Method) || HttpMethods.IsPost(request.Method) || HttpMethods.IsHead(request.Method);
    }
    
    private static string GetRawBodyAsync(HttpRequest request, Encoding? encoding = null)
    {
        if (!request.Body.CanSeek)
        {
            request.EnableBuffering();
        }

        request.Body.Position = 0;
        var reader = new StreamReader(request.Body, encoding ?? Encoding.UTF8);
        var body = reader.ReadToEndAsync().ConfigureAwait(false);
        request.Body.Position = 0;

        return body.GetAwaiter().GetResult();
    }
}