<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<CodeAnalysisRuleSet>..\.sonarlint\dfp-aepexplorer-apicsharp.ruleset</CodeAnalysisRuleSet>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<LangVersion>13</LangVersion>
	</PropertyGroup>

	<ItemGroup>
		<SonarQubeSetting Include="sonar.coverage.exclusions">
			<Value>**/Program.cs, **/SecurityConfiguration.cs</Value>
		</SonarQubeSetting>
	</ItemGroup>
	
	<ItemGroup>
		<AdditionalFiles Include="..\.sonarlint\dfp-aepexplorer-api\CSharp\SonarLint.xml" Link="SonarLint.xml" />
	</ItemGroup>

	<ItemGroup>
		<None Include="..\.editorconfig" Link=".editorconfig" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="MediatR" Version="12.4.1" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.1" />
		<PackageReference Include="System.Collections" Version="4.3.0" />
		<PackageReference Include="System.Diagnostics.Debug" Version="4.3.0" />
		<PackageReference Include="System.IO.FileSystem.Primitives" Version="4.3.0" />
		<PackageReference Include="System.Runtime.Extensions" Version="4.3.1" />
		<PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\AEPExplorer.Data.EF\AEPExplorer.Data.EF.csproj" />
		<ProjectReference Include="..\AEPExplorer.Model\AEPExplorer.Model.csproj" />
		<ProjectReference Include="..\AEPExplorer.Service\AEPExplorer.Service.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Content Include="Assets\WeissenhofGrotesk.ttf">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Include="Assets\AssetCheckTemplate.pptx">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>

	<ItemGroup>
	  <None Update="logo.png">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
