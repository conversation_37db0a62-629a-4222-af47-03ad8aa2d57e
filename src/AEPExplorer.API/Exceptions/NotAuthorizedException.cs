using System.Runtime.Serialization;

namespace AEPExplorer.API.Exceptions
{
    [Serializable]
    public class NotAuthorizedException : Exception, IHttpResponseException
    {
        public NotAuthorizedException() : base("Not authorized!") { }
        public NotAuthorizedException(string message) : base(message) { }
        public NotAuthorizedException(string message, Exception innerException) : base(message, innerException) { }
        public int HttpResponseCode => 401;
    }
}