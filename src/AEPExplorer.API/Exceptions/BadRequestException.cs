using System.Runtime.Serialization;

namespace AEPExplorer.API.Exceptions
{
    [Serializable]
    public class BadRequestException : Exception, IHttpResponseException
    {
        public BadRequestException() : base("The request was not understood by the server.") { }
        public BadRequestException(string message) : base(message) { }
        public BadRequestException(string message, Exception innerException) : base(message, innerException) { }
        public int HttpResponseCode => 400;
    }
}