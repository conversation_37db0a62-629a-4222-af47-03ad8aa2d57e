using System.Runtime.Serialization;

namespace AEPExplorer.API.Exceptions
{
    [Serializable]
    public class ServiceUnavailableException : Exception, IHttpResponseException
    {
        public ServiceUnavailableException() : base("We are currently upgrading our service and some of our data is not available at the moment. Thank you for understanding.") { }
        public ServiceUnavailableException(string message) : base(message) { }
        public ServiceUnavailableException(string message, Exception innerException) : base(message, innerException) { }
        public int HttpResponseCode => 503;
    }
}