using System.Runtime.Serialization;

namespace AEPExplorer.API.Exceptions
{
    [Serializable]
    public class NotFoundException : Exception, IHttpResponseException
    {
        public NotFoundException() : base("No results found") { }
        public NotFoundException(string message) : base(message) { }
        public NotFoundException(string message, Exception innerException) : base(message, innerException) { }
        public int HttpResponseCode => 404;
    }
}

