using System.Net;
using AEPExplorer.API.Exceptions;

namespace AEPExplorer.API.Middleware
{
    public class ExceptionMiddleware(RequestDelegate next)
    {
        public async Task InvokeAsync(HttpContext httpContext)
        {
            try
            {
                await next(httpContext);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(httpContext, ex);
            }
        }

        private static Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";

            var exceptionMessage = exception is AggregateException aggregateException ? aggregateException.Flatten().Message
                : exception is IHttpResponseException ? exception.Message : $"{exception.Message}\n{exception.StackTrace}";
            
            context.Response.StatusCode = exception is IHttpResponseException exceptionWithStatusCode ?
                                exceptionWithStatusCode.HttpResponseCode :
                                (int)HttpStatusCode.InternalServerError;

            return context.Response.WriteAsync(new ErrorDetails
            {        
                StatusCode = context.Response.StatusCode,
                Message = exceptionMessage
            }.ToString());
        }
    }
}
